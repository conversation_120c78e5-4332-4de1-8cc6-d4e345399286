# Via Grid 状态回滚提示词

## 🔄 快速回滚指令

当我偏离 ROADMAP.md 规范时，请使用以下提示词快速恢复正确状态：

```
请立即停止当前实现，严格按照 ROADMAP.md 执行以下检查和修正：

1. **插槽命名规范检查**：
   - 所有插槽必须使用连字符 `-` 分隔，不能使用冒号 `:`
   - 正确格式：`#table-name`、`#search-status`、`#form-tags`
   - 扩展插槽格式：`#toolbar-buttons-after`、`#table-action-after`
   - vxe-form 原生插槽：`#search-status-title`、`#search-status-prefix`、`#search-status-suffix`

2. **功能实现检查**：
   - 必须支持 vxe-form 原生插槽功能
   - 弹窗组件必须支持 modal 和 drawer 动态切换
   - 必须基于 vxe-table 和 vxe-pc-ui 实现
   - 遵循约定优于配置原则

3. **立即执行**：
   - 查看 ROADMAP.md 插槽系统设计部分
   - 对照检查清单验证当前实现
   - 修正所有不符合规范的代码

请确认理解并开始修正。
```

## 🎯 具体修正指令

### 插槽系统修正
```
插槽系统偏离规范，请按 ROADMAP.md 修正：

1. 实现 vxe-form 原生插槽：`#search-field-title`、`#search-field-prefix`、`#search-field-suffix`
2. 支持特定模式插槽：`#add-field`、`#edit-field`
3. 支持扩展插槽：`#toolbar-buttons-after`、`#table-action-after`

参考 ROADMAP.md 插槽系统设计部分。
```

### 弹窗组件修正
```
弹窗组件实现不符合要求，请修正：

1. 必须使用动态组件方式切换 vxe-modal/vxe-drawer
2. 不能使用 v-if 条件渲染两个组件
3. 必须提供统一的配置接口
4. 必须支持所有 vxe-modal 和 vxe-drawer 的属性

参考 ROADMAP.md 中的组件架构要求。
```

### 组件基础修正
```
组件基础实现偏离 vxe 生态，请修正：

1. Vue2 版本：基于 vxe-table v3.x，支持 Element UI、Ant Design Vue 1.x
2. Vue3 版本：基于 vxe-table v4.x，支持 Element Plus、Ant Design Vue 4.x、Naive UI
3. 构建工具：必须使用 Vite + Rollup + UnoCSS
4. 代码规范：必须遵循 antfu/eslint-config
5. 表单必须使用 vxe-form，不能使用 el-form
6. 弹窗必须使用 vxe-modal/vxe-drawer，不能使用 el-dialog
7. 必须遵循约定优于配置原则

立即查看 ROADMAP.md 技术架构部分并修正。
```

## 📋 检查确认流程

### 第一步：暂停当前工作
```
我注意到实现偏离了 ROADMAP.md 规范，请：
1. 立即停止当前代码编写
2. 查看 ROADMAP.md 相关部分
3. 确认具体偏离的地方
4. 制定修正计划
```

### 第二步：规范对照
```
请对照 ROADMAP.md 检查以下关键点：
1. 插槽命名是否使用连字符分隔
2. 是否支持 vxe-form 原生插槽功能
3. 是否基于正确的 vxe 版本
4. 组件架构是否符合要求
5. 是否遵循约定优于配置原则
```

### 第三步：修正确认
```
修正完成后，请确认：
1. 所有插槽命名符合 `scope-field` 格式
2. vxe-form 原生插槽 `scope-field-title/prefix/suffix` 功能正常
3. 组件基于 vxe 生态实现
4. 遵循约定优于配置原则
5. 通过功能测试验证
```

## 🚨 紧急修正模板

```
【紧急修正】发现严重偏离 ROADMAP.md 规范：

问题：[具体问题描述]
影响：[影响范围]
要求：[ROADMAP.md 具体要求]

立即修正方案：
1. [修正步骤1]
2. [修正步骤2]  
3. [修正步骤3]

验证方法：
- [ ] 对照 ROADMAP.md 检查
- [ ] 运行功能测试
- [ ] 确认规范符合

请立即执行修正。
```

## 🔧 自动检查机制

### 开发前检查提示词
```
开始新的开发任务前，请执行强制检查：

1. 查看 ROADMAP.md 相关部分和开发阶段
2. 确认插槽命名规范（使用连字符分隔）
3. 确认技术要求和规范（vxe 生态、UnoCSS、antfu/eslint-config）
4. 确认设计原则（约定优于配置）
5. 列出关键实现点
6. 开始编码实现

请确认完成检查后再开始编码。
```

### 开发中验证提示词
```
完成一个功能模块后，请立即验证：

1. 对照 ROADMAP.md 检查实现
2. 验证插槽命名规范（连字符分隔）
3. 验证 vxe-form 原生插槽功能
4. 测试关键功能点
5. 确认遵循约定优于配置原则
6. 确认无偏离规范

发现问题立即修正，不要继续后续开发。
```

## 🎯 设计原则修正

### 约定优于配置修正
```
发现违反约定优于配置原则，请修正：

1. 检查是否提供了合理的默认值
2. 确认最小配置即可使用
3. 验证按需覆盖实现定制
4. 移除不必要的强制配置项
5. 简化复杂的配置结构

参考 ROADMAP.md 设计原则部分。
```

### UI 库适配修正
```
UI 库适配不符合要求，请修正：

Vue2 版本：
- Element UI + @vxe-ui/plugin-render-element
- Ant Design Vue 1.x + @vxe-ui/plugin-render-antd

Vue3 版本：
- Element Plus + @vxe-ui/plugin-render-element (最高优先级)
- Ant Design Vue 4.x + @vxe-ui/plugin-render-antd
- Naive UI + @vxe-ui/plugin-render-naive

必须使用官方渲染插件，不能直接使用 UI 库组件。
```

## 📞 求助机制

当遇到复杂问题时：
```
我在实现 [功能名称] 时遇到困难：

问题描述：[具体问题]
ROADMAP 要求：[相关要求]
当前实现：[当前状态]
困难点：[具体困难]
开发阶段：[第几阶段]

请提供指导或澄清 ROADMAP.md 中的具体要求。
```