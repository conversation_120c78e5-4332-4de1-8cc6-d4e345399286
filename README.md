# Via Grid

基于 vxe-table 的现代化表格组件库，旨在提供贴合开发习惯的 API 与灵活扩展能力，助力高效开发与定制。

## ✨ 特色功能

- 🚀 **跨版本支持**: 同时支持 Vue2 和 Vue3，提供一致的开发体验
- 📦 **多包架构**: 基于 pnpm workspace 的 monorepo 管理
- 🎯 **Schema 驱动**: 通过配置对象统一管理字段和行为
- 🔌 **配置式 API**: 充分利用 vxe-form items 和 vxe-toolbar 配置式 API
- 🏗️ **站在巨人肩膀上**: 基于 vxe-table 生态，而非重复造轮子
- 🎨 **多 UI 库支持**: Element Plus/UI、Ant Design Vue、Naive UI
- 📝 **TypeScript**: 完整的类型定义和智能提示
- 🎪 **灵活插槽**: 支持 vxe 原生插槽系统，使用 "-" 分割符，简洁直观
- ⚡ **约定优于配置**: 提供合理默认值，最小配置即可使用，按需覆盖实现定制
- 🔥 **高性能**: 减少组件嵌套，充分利用 vxe 内置优化

## 📦 包说明

| 包名 | 描述 | Vue 版本 | 主要依赖 |
|------|------|----------|----------|
| `@via-grid/core` | 核心逻辑和类型定义 | 2.x / 3.x | es-toolkit |
| `@via-grid/shared` | 共享工具和常量 | 2.x / 3.x | es-toolkit |
| `@via-grid/vue2` | Vue2 实现包 | 2.6+ | @via-grid/core, vxe-table@3.x |
| `@via-grid/vue3` | Vue3 实现包 | 3.2+ | @via-grid/core, vxe-table@4.x |

## 🎯 支持的 UI 库

| UI 库 | Vue2 | Vue3 | 官方插件 | 状态 |
|-------|------|------|----------|------|
| Element UI | ✅ | ❌ | `@vxe-ui/plugin-render-element` | 计划支持 |
| Element Plus | ❌ | ✅ | `@vxe-ui/plugin-render-element` | 优先支持 |
| Ant Design Vue | ✅ | ✅ | `@vxe-ui/plugin-render-antd` | 计划支持 |
| Naive UI | ❌ | ✅ | `@vxe-ui/plugin-render-naive` | 计划支持 |

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装

```bash
# 克隆项目
git clone https://github.com/viarotel/via-grid.git
cd via-grid

# 安装 pnpm (如果还没有安装)
npm install -g pnpm

# 一键设置开发环境
pnpm setup
```

### 最简配置

```typescript
import { ViaGrid } from '@via-grid/vue3'

const config = {
  api: {
    list: '/api/users'
  },
  schema: {
    name: { type: 'text', label: '姓名' },
    email: { type: 'text', label: '邮箱' },
    status: {
      type: 'select',
      label: '状态',
      dict: {
        data: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    }
  }
}
```

```vue
<template>
  <ViaGrid :config="config" />
</template>
```

### 使用 es-toolkit

Via Grid 直接使用 [es-toolkit](https://es-toolkit.slash.page/) 提供高性能的工具函数：

```typescript
// 直接从 es-toolkit 导入常用工具函数
import { isEmpty, merge, isObject, cloneDeep } from 'es-toolkit'

// 从 @via-grid/shared 导入项目特定工具
import { generateId, formatSlotName } from '@via-grid/shared'

// 高性能的工具函数
const merged = merge(defaultConfig, userConfig)
const cloned = cloneDeep(originalData)
const id = generateId('via-grid')
```

## 🚀 开发环境

```bash
# 安装依赖
pnpm install

# 启动开发服务器 (Vue3 playground)
cd playground/vue3
pnpm dev

# 构建项目
pnpm build
```

## 📚 文档

- [快速开始](./docs/quick-start.md)
- [VXE 配置式 API 核心用法](./docs/vxe-components-config-api.md)
- [API 文档](./docs/api.md)
- [组件文档](./docs/components.md)
- [示例代码](./docs/examples.md)

## 🗺️ 开发路线图

### 第一阶段 ✅ - 核心架构
- [x] 项目架构设计
- [x] 核心类型定义
- [x] 基础工具函数
- [x] 适配器模式实现

### 第二阶段 ✅ - Vue3 版本实现 (已重构)
- [x] Vue3 组件开发
- [x] Composition API 设计
- [x] TypeScript 集成
- [x] **ViaForm 配置式 API 重构** - 使用 vxe-form items 配置
- [x] **ViaToolbar 配置式 API 重构** - 基于 vxe-toolbar 插槽
- [x] **Vue3 Playground 示例** - 完整的测试和演示环境
- [ ] 单元测试覆盖

### 第三阶段 📋 - Vue2 版本实现
- [ ] Vue2 组件开发 (基于重构后的架构)
- [ ] Options API 适配
- [ ] 向下兼容处理
- [ ] Vue2 Playground 示例
- [ ] 测试用例补充

### 第四阶段 📋 - 生态完善
- [ ] 官方插件集成优化
- [ ] 文档站点建设
- [ ] 更多示例项目
- [ ] 性能优化和最佳实践
- [ ] 社区建设

## 🔄 重构亮点

### 核心理念调整
**站在优秀框架的肩膀上，通过配置式 API 提升用户体验和工程效率**

### ViaForm 重构
- ✅ 从基础的 `vxe-form-item` 改为 `items` 配置式 API
- ✅ 充分利用 vxe-form 的设计初衷和能力
- ✅ 支持字段联动和动态配置
- ✅ 保持插槽系统的兼容性

### ViaToolbar 重构
- ✅ 基于 `vxe-toolbar` 而不是重新封装
- ✅ 利用 `#buttons` 和 `#tools` 插槽
- ✅ 支持 vxe-toolbar 的原生功能
- ✅ 增强与 vxe-table 的集成

### 开发节奏优化
- ✅ **立即补齐 Vue3 playground 示例** - 便于尽早测试和反馈
- 📋 Vue2 版本将基于重构后的架构进行开发
- 📋 每个主要功能完成后立即补充对应示例

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
