# Via Grid 项目设计文档

## 项目概述

Via Grid 是一个基于 vxe-table 的现代化表格组件库，旨在提供贴合开发习惯的 API 与灵活扩展能力，助力高效开发与定制。

### 核心特色

- 🚀 **跨版本支持**: 同时支持 Vue2 和 Vue3，提供一致的开发体验
- 📦 **多包架构**: 基于 pnpm workspace 的 monorepo 管理
- 🎯 **Schema 驱动**: 通过配置对象统一管理字段和行为
- 🔌 **官方插件集成**: 直接使用 vxe-ui 官方渲染插件
- 🎨 **多 UI 库支持**: Element Plus/UI、Ant Design Vue、Naive UI
- 📝 **TypeScript**: 完整的类型定义和智能提示
- 🎪 **灵活插槽**: 支持 vxe-form 原生插槽，使用 "-" 分割符，简洁直观
- ⚡ **约定优于配置**: 提供合理默认值，最小配置即可使用，按需覆盖实现定制

## 技术架构

### 版本兼容策略

| Vue 版本 | vxe-table 版本 | 实现包 | 支持的 UI 库 |
|----------|----------------|--------|--------------|
| Vue 2.6+ | v3.x | @via-grid/vue2 | Element UI, Ant Design Vue 1.x |
| Vue 3.2+ | v4.x | @via-grid/vue3 | Element Plus, Ant Design Vue 4.x, Naive UI |

### 项目结构

```
via-grid/
├── docs/                       # VitePress 文档站点
├── playground/
│   ├── vue2/                   # Vue2 开发调试
│   └── vue3/                   # Vue3 开发调试
├── packages/
│   ├── core/                   # 共享核心逻辑
│   ├── shared/                 # 共享工具和类型
│   ├── vue2/                   # Vue2 实现包
│   └── vue3/                   # Vue3 实现包
└── scripts/                    # 脚本
```

### 技术栈

- **包管理**: pnpm workspace
- **构建工具**: Vite + Rollup
- **开发语言**: TypeScript
- **测试框架**: Vitest + Vue Test Utils --暂不集成
- **文档工具**: VitePress
- **代码规范**: antfu/eslint-config
- **样式框架**: UnoCSS (preset-wind, preset-icons, transformer-directives)

## 开发路线图

### 第一阶段：核心架构搭建（3-4周）

**目标**：建立跨版本兼容的基础架构

- [ ] **项目结构设计**（1周）
  - Monorepo 配置（pnpm workspace）
  - 包依赖关系设计
  - 构建工具配置（Vite + UnoCSS）
- [ ] **核心包开发**（2周）
  - 统一类型定义
  - Schema 转换逻辑
  - 适配器基类
  - 工具函数库
- [ ] **版本检测机制**（0.5周）
  - 自动版本识别
  - 动态加载机制
- [ ] **代码规范配置**（0.5周）
  - antfu/eslint-config 集成
  - UnoCSS 配置和预设

### 第二阶段：Vue3 版本实现（4-5周）

**目标**：基于 vxe-table v4.x 实现 Vue3 版本

- [ ] **基础组件**（2周）
  - ViaGrid 主组件（Composition API）
  - ViaSearch 搜索组件
  - ViaToolbar 工具栏组件
  - ViaTable 表格组件
  - ViaPager 分页组件
  - ViaForm 表单组件(支持新增和编辑两种模式)
  - ViaInfo 详情组件
- [ ] **Composables 系统**（1周）
  - useGrid composable
  - useSearch composable
  - useToolbar composable
  - useTable composable
  - usePager composable
  - useForm composable
  - ...
- [ ] **适配器集成**（1.5周）
  - Element Plus 适配器
  - Ant Design Vue 4.x 适配器
  - Naive UI 适配器
- [ ] **插槽系统**（0.5周）
  - 基于 Vxe 定制插槽支持
  - 作用域插槽实现

### 第三阶段：Vue2 版本实现（4-5周）

**目标**：基于 vxe-table v3.x 实现 Vue2 版本

- [ ] **基础组件**（2周）
  - ViaGrid 主组件
  - ViaSearch 搜索组件
  - ViaToolbar 工具栏组件
  - ViaTable 表格组件
  - ViaPager 分页组件
  - ViaForm 表单组件(支持新增和编辑两种模式)
  - ViaInfo 详情组件
- [ ] **Mixins 系统**（1周）
  - 数据管理 mixin
  - 事件处理 mixin
  - 生命周期 mixin
  - 各个模块的 mixin
  - ...
- [ ] **适配器集成**（1.5周）
  - Element UI 适配器
  - Ant Design Vue 1.x 适配器
- [ ] **插槽系统**（0.5周）
  - 基于 Vxe 定制插槽支持
  - 作用域插槽实现

### 第四阶段：集成和完善（3-4周）

**目标**：统一体验和生态完善

- [ ] **跨版本测试**（1周）
  - 兼容性测试
  - API 一致性验证
  - 性能对比测试
- [ ] **文档和示例**（1.5周）
  - 统一文档站点
  - Vue2/Vue3 示例项目
  - 迁移指南
- [ ] **工具链完善**（1周）
  - CLI 工具
  - 类型检查
  - 开发调试工具
- [ ] **发布准备**（0.5周）
  - 自动化发布流程
  - 版本管理策略

## 完整 API 设计文档

### 基础类型定义

```typescript
// 基础类型
export type VueVersion = 'vue2' | 'vue3'
export type UILibrary = 'element-ui' | 'element-plus' | 'ant-design-vue' | 'naive-ui'
export type FieldType = 'text' | 'number' | 'select' | 'date' | 'switch' | 'upload' | 'tag' | 'radio' | 'checkbox' | string

// 字段配置接口
export interface FieldSchema {
  type: FieldType
  label: string
  value?: any
  placeholder?: string
  
  // 字典支持
  dict?: DictConfig | DictFunction
  options?: Array<{ label: string; value: any; [key: string]: any }>
  
  // 验证规则
  rules?: ValidationRule[]
  
  // 显示控制（支持动态函数）
  hidden?: boolean | string[] | HiddenFunction
  
  // 字段属性
  fieldProps?: Record<string, any>
  
  // 数据转换函数
  formatter?: FormatterFunction
  parameter?: ParameterFunction
  
  // 作用域特定配置
  search?: Partial<FieldSchema> & SearchFieldConfig
  table?: Partial<FieldSchema> & TableFieldConfig
  form?: Partial<FieldSchema> & FormFieldConfig
  add?: Partial<FieldSchema> & FormFieldConfig
  edit?: Partial<FieldSchema> & FormFieldConfig
  info?: Partial<FieldSchema> & InfoFieldConfig
}

// 数据转换函数类型
export type FormatterFunction = (value: any, row?: any, context?: any) => any
export type ParameterFunction = (value: any, row?: any, context?: any) => any
export type HiddenFunction = (context: HiddenContext) => boolean
export type DictFunction = (context?: any) => DictItem[] | Promise<DictItem[]>

// 字典配置
export interface DictConfig {
  data?: DictItem[]
  remote?: string | Function
  labelKey?: string
  valueKey?: string
  cache?: boolean
}

export interface DictItem {
  label: string
  value: any
  [key: string]: any
}

// 隐藏函数上下文
export interface HiddenContext {
  scope: string
  row?: any
  mode?: 'add' | 'edit' | 'info'
  formData?: any
  [key: string]: any
}
```

### 主配置接口

```typescript
export interface ViaGridConfig {
  // ==================== 基础配置 ====================
  title?: string                          // 表格标题，默认为空
  height?: number | string                // 表格高度，默认为 'auto'
  loading?: boolean                       // 加载状态，默认为 false

  // ==================== API 配置 ====================
  api: {
    // 基础 CRUD 接口
    list: string | Function               // 列表查询接口（必填）
    add?: string | Function               // 新增接口
    edit?: string | Function              // 编辑接口
    remove?: string | Function            // 删除接口
    info?: string | Function              // 详情接口

    // 导入导出接口
    import?: string | Function            // 导入接口
    export?: string | Function            // 导出接口
    template?: string | Function          // 模板下载接口

    // 批量操作接口
    batchRemove?: string | Function       // 批量删除接口
    batchUpdate?: string | Function       // 批量更新接口
  }

  // ==================== Schema 字段定义 ====================
  schema: Record<string, FieldSchema>

  // ==================== 布局配置 ====================
  // 注意：layout 专注于组件的显示/隐藏和基础布局，具体配置在各自的配置项中
  layout?: {
    search?: boolean                      // 是否显示搜索区域，默认为 true
    toolbar?: boolean                     // 是否显示工具栏，默认为 true
    table?: boolean                       // 是否显示表格，默认为 true
    pager?: boolean                       // 是否显示分页，默认为 true
  }

  // ==================== 权限配置 ====================
  permissions?: PermissionConfig

  // ==================== 排序配置 ====================
  sort?: {
    search?: string[]                     // 搜索字段排序
    table?: string[]                      // 表格列排序
    form?: string[]                       // 表单字段排序
    add?: string[]                        // 新增表单字段排序
    edit?: string[]                       // 编辑表单字段排序
    info?: string[]                       // 详情字段排序
  }

  // ==================== 事件处理 ====================
  events?: EventHandlers

  // ==================== 模块配置 ====================
  // 约定优于配置：所有配置项都有合理的默认值，可按需覆盖
  form?: FormConfig                       // 表单配置
  dialog?: DialogConfig                   // 弹窗配置
  search?: SearchConfig                   // 搜索配置
  toolbar?: ToolbarConfig                 // 工具栏配置
  table?: TableConfig                     // 表格配置
  pager?: PagerConfig                     // 分页配置
  data?: DataConfig                       // 数据处理配置
  i18n?: I18nConfig                       // 国际化配置
}
```

### 完整使用示例

```typescript
const fullConfig: ViaGridConfig = {
  // ==================== 基础配置 ====================
  title: '用户管理',                    // 可选，默认为空
  height: 600,                          // 可选，默认为 'auto'
  loading: false,                       // 可选，默认为 false
  
  // ==================== API 配置 ====================
  api: {
    list: '/api/users',
    add: '/api/users',
    edit: '/api/users/:id',
    remove: '/api/users/:id',
    info: '/api/users/:id',
    import: '/api/users/import',
    export: '/api/users/export',
    template: '/api/users/template',
    batchRemove: '/api/users/batch',
    batchUpdate: '/api/users/batch',
  },

  // ==================== Schema 字段定义 ====================
  schema: {
    // 文本字段
    name: {
      type: 'text',
      label: '姓名',
      value: '',
      placeholder: '请输入姓名',
      rules: [
        { required: true, message: '请输入姓名' },
        { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符' }
      ],
      fieldProps: {
        clearable: true,
        showWordLimit: true,
        maxlength: 20
      },
      
      // 数据转换
      formatter: (value, row) => value?.toUpperCase(),
      parameter: (value) => value?.trim(),
      
      // 作用域特定配置
      search: {
        placeholder: '请输入姓名搜索',
        itemProps: { labelWidth: '80px' }
      },
      table: {
        width: 120,
        fixed: 'left',
        sortable: true,
        align: 'center',
        columnProps: { showOverflowTooltip: true }
      },
      form: {
        span: 12,
        rules: [{ required: true, message: '姓名不能为空' }],
        itemProps: { labelWidth: '100px' }
      },
      add: {
        span: 24,
        placeholder: '请输入用户姓名'
      },
      edit: {
        span: 12,
        disabled: false
      },
      info: {
        span: 8
      }
    },

    // 选择字段（支持字典）
    status: {
      type: 'select',
      label: '状态',
      value: 1,
      dict: {
        data: [
          { label: '启用', value: 1, color: 'success' },
          { label: '禁用', value: 0, color: 'danger' }
        ]
      },
      fieldProps: {
        clearable: true,
        filterable: true
      },
      formatter: (value, row, context) => {
        const dict = context.dict.find(item => item.value === value)
        return dict?.label || value
      },
      table: {
        width: 100,
        align: 'center',
        cellRender: {
          name: 'ElTag',
          props: (row) => ({
            type: row.status === 1 ? 'success' : 'danger'
          })
        }
      },
      search: {
        placeholder: '请选择状态',
        dict: {
          data: [
            { label: '全部', value: '' },
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
          ]
        }
      }
    },

    // 标签字段
    tags: {
      type: 'tag',
      label: '标签',
      value: [],
      dict: async () => {
        const response = await fetch('/api/tags')
        return response.json()
      },
      formatter: (value) => Array.isArray(value) ? value.join(', ') : value,
      table: {
        width: 150,
        cellRender: {
          name: 'ElTag',
          props: (row) => ({ type: 'info', size: 'small' })
        }
      },
      hidden: ['search'] // 在搜索中隐藏
    },

    // 动态隐藏字段
    secretInfo: {
      type: 'text',
      label: '机密信息',
      value: '',
      hidden: (context) => {
        // 根据用户权限动态隐藏
        return !context.user?.hasPermission('view_secret')
      },
      table: {
        width: 120
      }
    },

    // 关联字段（远程字典）
    departmentId: {
      type: 'select',
      label: '部门',
      value: null,
      dict: {
        remote: '/api/departments',
        labelKey: 'name',
        valueKey: 'id',
        cache: true
      },
      fieldProps: {
        clearable: true,
        filterable: true,
        remote: true,
        remoteMethod: (query) => {
          return axios.get('/api/departments', { params: { keyword: query } })
        }
      },
      formatter: (value, row) => row.departmentName || '-',
      table: {
        width: 120
      }
    },

    // 只读字段
    createTime: {
      type: 'text',
      label: '创建时间',
      value: '',
      hidden: ['form', 'search'],
      formatter: (value) => value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
      table: {
        width: 160
      },
      info: {
        span: 12
      }
    }
  },

  // ==================== 布局配置 ====================
  // 注意：layout 专注于组件的显示/隐藏，具体配置在各自的模块配置中
  layout: {
    search: true,                         // 显示搜索区域，默认为 true
    toolbar: true,                        // 显示工具栏，默认为 true
    table: true,                          // 显示表格，默认为 true
    pager: true                           // 显示分页，默认为 true
  },

  // ==================== 权限配置 ====================
  permissions: {
    add: true,                            // 新增权限，默认为 true
    edit: true,                           // 编辑权限，默认为 true
    remove: true,                         // 删除权限，默认为 true
    info: true,                           // 详情权限，默认为 true
    import: true,                         // 导入权限，默认为 true
    export: true,                         // 导出权限，默认为 true
    batchRemove: true,                    // 批量删除权限，默认为 true
    batchUpdate: false,                   // 批量更新权限，默认为 false
    'toolbar-add': true,                  // 工具栏新增按钮权限
    'toolbar-remove': true,               // 工具栏删除按钮权限
    'table-edit': true,                   // 表格编辑权限
    'table-remove': true,                 // 表格删除权限
    'row-edit': (row) => row.status === 1, // 行级编辑权限（动态）
    'row-remove': (row) => row.createdBy === currentUserId // 行级删除权限（动态）
  },

  // ==================== 排序配置 ====================
  sort: {
    search: ['name', 'status', 'departmentId'],
    table: ['name', 'age', 'status', 'createTime'],
    form: ['name', 'age', 'status', 'birthday'],
    add: ['name', 'age', 'departmentId', 'status'],
    edit: ['name', 'age', 'status'],
    info: ['name', 'age', 'status', 'birthday', 'createTime']
  },

  // ==================== 事件处理 ====================
  events: {
    // 数据操作事件
    onSearch: (params) => console.log('搜索参数:', params),
    onAfterSearch: (result) => console.log('搜索完成:', result),
    onAdd: (data) => {
      console.log('新增数据:', data)
      return data
    },
    onEdit: (data, row) => {
      console.log('编辑数据:', data, '原始数据:', row)
      return data
    },
    onRemove: (row) => {
      console.log('删除数据:', row)
      return confirm('确定要删除吗？')
    },
    onInfo: (row) => console.log('查看详情:', row),
    
    // 批量操作事件
    onBatchRemove: (rows) => {
      console.log('批量删除:', rows)
      return confirm(`确定要删除 ${rows.length} 条记录吗？`)
    },
    
    // 导入导出事件
    onImport: (file) => console.log('导入文件:', file),
    onExport: (params) => console.log('导出参数:', params),
    
    // 表格事件
    onRowClick: (row, column, event) => console.log('行点击:', row, column),
    onRowDblclick: (row, column, event) => console.log('行双击:', row, column),
    onSelectionChange: (selection) => console.log('选择变化:', selection),
    onSortChange: ({ column, prop, order }) => console.log('排序变化:', column, prop, order),
    
    // 分页事件
    onPageChange: (page) => console.log('页码变化:', page),
    onSizeChange: (size) => console.log('每页条数变化:', size),
    
    // 生命周期事件
    onMounted: (instance) => console.log('组件挂载:', instance),
    onBeforeDestroy: (instance) => console.log('组件销毁前:', instance)
  },

  // ==================== 模块配置 ====================
  // 约定优于配置：所有配置项都有合理的默认值，开发者可按需覆盖
  form: {
    labelWidth: '100px',                  // 默认标签宽度
    labelPosition: 'right',               // 默认标签位置
    size: 'medium',                       // 默认尺寸
    disabled: false,                      // 默认启用状态
    validateOnRuleChange: true,           // 默认规则变化时验证
    hideRequiredAsterisk: false,          // 默认显示必填星号
    showMessage: true,                    // 默认显示验证消息
    inlineMessage: false                  // 默认不内联显示消息
  },

  dialog: {
    width: '800px',                       // 默认弹窗宽度
    fullscreen: false,                    // 默认非全屏
    modal: true,                          // 默认模态
    lockScroll: true,                     // 默认锁定滚动
    closeOnClickModal: false,             // 默认点击遮罩不关闭
    closeOnPressEscape: true,             // 默认ESC关闭
    showClose: true,                      // 默认显示关闭按钮
    destroyOnClose: true                  // 默认关闭时销毁
  },

  search: {
    resetToDefaultValue: true,            // 默认重置到默认值
    submitOnReset: true,                  // 默认重置后提交
    submitOnEnter: true,                  // 默认回车提交
    trimValues: true,                     // 默认去除空格
    immediate: true,                      // 默认立即搜索
    collapsed: false,                     // 默认不折叠
    collapseRows: 1,                      // 默认折叠行数
    labelWidth: '80px',                   // 默认标签宽度
    itemProps: { labelPosition: 'right' } // 默认标签位置
  },

  toolbar: {
    buttons: ['add', 'import', 'export'], // 默认显示的按钮
    tools: ['refresh', 'fullscreen', 'columns'], // 默认显示的工具
    custom: true,                         // 默认允许自定义
    import: {
      accept: '.xlsx,.xls,.csv',          // 默认接受的文件类型
      multiple: false,                    // 默认单文件上传
      showProgress: true,                 // 默认显示进度
      autoUpload: true                    // 默认自动上传
    },
    export: {
      filename: '数据列表',               // 默认文件名
      format: 'xlsx',                     // 默认导出格式
      includeHeader: true,                // 默认包含表头
      onlySelected: false                 // 默认导出全部数据
    },
    refresh: {
      autoRefresh: false,                 // 默认不自动刷新
      interval: 30000                     // 默认刷新间隔30秒
    }
  },

  table: {
    rowKey: 'id',                         // 默认行键
    border: true,                         // 默认显示边框
    stripe: true,                         // 默认斑马纹
    height: 'auto',                       // 默认高度
    maxHeight: 600,                       // 默认最大高度
    size: 'medium',                       // 默认尺寸
    emptyText: '暂无数据',                // 默认空数据文本
    defaultSort: { prop: 'createTime', order: 'descending' }, // 默认排序
    highlightCurrentRow: true,            // 默认高亮当前行
    showHeader: true,                     // 默认显示表头
    showSummary: false,                   // 默认不显示合计行
    sumText: '合计',                      // 默认合计文本
    summaryMethod: (param) => {
      // 自定义合计方法
    },
    spanMethod: (param) => {
      // 自定义合并行或列
    },
    lazy: false,                          // 默认不懒加载
    load: (row, treeNode, resolve) => {
      // 懒加载子节点
    }
  },

  pager: {
    pageSize: 10,                         // 默认每页条数
    pageSizes: [10, 20, 50, 100],         // 默认每页条数选项
    layout: 'total, sizes, prev, pager, next, jumper', // 默认布局
    background: true,                     // 默认显示背景
    align: 'right',                       // 默认右对齐
    perfect: false,                       // 默认非完美分页
    pageCount: 7,                         // 默认页码按钮数量
    pagerCount: 7,                        // 默认分页器数量
    autoHidden: false                     // 默认不自动隐藏
  },

  data: {
    transformRequest: (params) => ({       // 默认请求参数转换
      ...params,
      page: params.current,
      size: params.pageSize
    }),
    transformResponse: (response) => ({    // 默认响应数据转换
      data: response.data.records,
      total: response.data.total,
      current: response.data.current,
      pageSize: response.data.size
    }),
    validateData: (data) => Array.isArray(data) // 默认数据验证
  },

  i18n: {
    search: '搜索',
    reset: '重置',
    add: '新增',
    edit: '编辑',
    remove: '删除',
    info: '详情',
    import: '导入',
    export: '导出',
    refresh: '刷新',
    confirm: '确定',
    cancel: '取消'
  }
}
```

### 插槽系统设计

基于用户反馈，插槽系统进行了以下优化：

1. **使用 "-" 分割符**：不再使用 ":" 分割，改用 "-" 提升可读性
2. **移除 deep 形式**：简化插槽设计，提供更直观的使用方式
3. **支持 vxe-form 原生插槽**：完全兼容 vxe-form 的插槽功能
4. **保持灵活性**：支持多层级自定义和作用域插槽

### 插槽命名规范

```typescript
// 基础插槽格式：{scope}-{field}
// 扩展插槽格式：{scope}-{field}-{type}
// 区域插槽格式：{area}-{position}

// 示例：
'table-avatar'           // 表格中 avatar 字段的自定义渲染
'search-status'          // 搜索区域 status 字段的自定义组件
'search-status-title'    // 搜索区域 status 字段的标题插槽
'search-status-prefix'   // 搜索区域 status 字段的前缀插槽
'search-status-suffix'   // 搜索区域 status 字段的后缀插槽
'form-tags'              // 表单中 tags 字段的自定义组件
'add-password'           // 新增表单中 password 字段的自定义组件
'edit-password'          // 编辑表单中 password 字段的自定义组件
'toolbar-buttons-after'  // 工具栏按钮区域后的扩展插槽
'table-action-after'     // 表格操作列后的扩展插槽
```

### 插槽使用示例

```vue
<template>
  <ViaGrid :config="gridConfig">
    <!-- 字段自定义渲染 -->
    <template #table-avatar="{ row, field, value }">
      <el-avatar :src="value" :size="40" />
    </template>

    <!-- 表格状态列自定义 -->
    <template #table-status="{ row, field, value }">
      <el-tag :type="row.status === 1 ? 'success' : 'danger'">
        {{ row.status === 1 ? '启用' : '禁用' }}
      </el-tag>
    </template>

    <!-- 搜索区域自定义 -->
    <template #search-status="{ field, value, onChange }">
      <el-radio-group :value="value" @input="onChange">
        <el-radio :label="1">启用</el-radio>
        <el-radio :label="0">禁用</el-radio>
      </el-radio-group>
    </template>

    <!-- 支持 vxe-form 原生插槽：标题插槽 -->
    <template #search-department-title="{ field, itemConfig }">
      <span class="custom-label">
        <el-icon><OfficeBuilding /></el-icon>
        {{ field.label }}
      </span>
    </template>

    <!-- 支持 vxe-form 原生插槽：前缀插槽 -->
    <template #search-keyword-prefix="{ field }">
      <el-icon><Search /></el-icon>
    </template>

    <!-- 支持 vxe-form 原生插槽：后缀插槽 -->
    <template #search-keyword-suffix="{ field }">
      <el-button type="text" size="small">清空</el-button>
    </template>

    <!-- 表单字段自定义 -->
    <template #form-tags="{ field, value, onChange }">
      <el-tag
        v-for="tag in value"
        :key="tag"
        closable
        @close="removeTag(tag)"
      >
        {{ tag }}
      </el-tag>
      <el-input
        v-if="inputVisible"
        ref="saveTagInput"
        v-model="inputValue"
        size="small"
        @keydown.enter="handleInputConfirm"
        @blur="handleInputConfirm"
      />
      <el-button v-else size="small" @click="showInput">+ 新标签</el-button>
    </template>

    <!-- 新增表单特定字段 -->
    <template #add-password="{ field, value, onChange }">
      <el-input
        :value="value"
        type="password"
        placeholder="请输入密码"
        show-password
        @input="onChange"
      />
    </template>

    <!-- 编辑表单特定字段 -->
    <template #edit-password="{ field, value, onChange }">
      <el-input
        :value="value"
        type="password"
        placeholder="留空则不修改密码"
        show-password
        @input="onChange"
      />
    </template>

    <!-- 复杂表单字段组合 -->
    <template #form-address="{ field, value, onChange }">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-select
            :value="value?.province"
            placeholder="省份"
            @change="val => onChange({ ...value, province: val })"
          >
            <el-option label="北京" value="beijing" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            :value="value?.city"
            placeholder="城市"
            @change="val => onChange({ ...value, city: val })"
          >
            <el-option label="北京市" value="beijing" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            :value="value?.district"
            placeholder="区县"
            @change="val => onChange({ ...value, district: val })"
          >
            <el-option label="朝阳区" value="chaoyang" />
          </el-select>
        </el-col>
      </el-row>
    </template>

    <!-- 工具栏扩展 -->
    <template #toolbar-buttons-after="{ selection }">
      <el-button
        type="warning"
        :disabled="!selection.length"
        @click="handleBatchUpdate"
      >
        批量更新
      </el-button>
    </template>

    <!-- 表格操作列扩展 -->
    <template #table-action-after="{ row }">
      <el-button
        type="text"
        size="small"
        @click="handleCustomAction(row)"
      >
        自定义操作
      </el-button>
    </template>

    <!-- 区域间插槽 -->
    <template #search-to-toolbar>
      <el-divider />
    </template>

    <template #toolbar-to-table>
      <el-alert message="这是一个提示信息" type="info" show-icon />
    </template>
  </ViaGrid>
</template>
```

### 组件方法调用示例

```typescript
// 组件引用
const gridRef = ref<ViaGridInstance>()

// 数据操作方法
gridRef.value?.refresh()                    // 刷新数据
gridRef.value?.search(params)               // 执行搜索
gridRef.value?.resetSearch()                // 重置搜索
gridRef.value?.clearSelection()             // 清空选择
gridRef.value?.toggleRowSelection(row)      // 切换行选择状态

// 表单操作方法
gridRef.value?.openAddDialog()              // 打开新增弹窗
gridRef.value?.openEditDialog(row)          // 打开编辑弹窗
gridRef.value?.openInfoDialog(row)          // 打开详情弹窗
gridRef.value?.closeDialog()                // 关闭弹窗

// 数据获取方法
const tableData = gridRef.value?.getTableData()        // 获取表格数据
const selection = gridRef.value?.getSelection()        // 获取选中数据
const searchParams = gridRef.value?.getSearchParams()  // 获取搜索参数

// 表格操作方法
gridRef.value?.setCurrentRow(row)           // 设置当前行
gridRef.value?.toggleRowExpansion(row)      // 切换行展开状态
gridRef.value?.clearSort()                  // 清空排序
gridRef.value?.doLayout()                   // 重新布局
```

### 简化使用示例

基于"约定优于配置"的设计理念，最简配置示例：

```typescript
// 最小配置 - 仅需 API 和 Schema
const minimalConfig: ViaGridConfig = {
  api: {
    list: '/api/users'                      // 仅需列表接口
  },
  schema: {
    name: { type: 'text', label: '姓名' },
    email: { type: 'text', label: '邮箱' },
    status: {
      type: 'select',
      label: '状态',
      dict: {
        data: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    }
  }
  // 其他配置都有合理默认值，无需手动配置
}
```

```vue
<template>
  <!-- 最简使用 -->
  <ViaGrid :config="minimalConfig" />
</template>
```

## 包说明

| 包名 | 描述 | Vue 版本 | 依赖 |
|------|------|----------|------|
| `@via-grid/core` | 核心逻辑和类型定义 | 2.x / 3.x | es-toolkit |
| `@via-grid/shared` | 共享工具和常量 | 2.x / 3.x | - |
| `@via-grid/vue2` | Vue2 实现包 | 2.6+ | @via-grid/core, vxe-table@3.x |
| `@via-grid/vue3` | Vue3 实现包 | 3.2+ | @via-grid/core, vxe-table@4.x |

## 支持的 UI 库

| UI 库 | Vue2 | Vue3 | 官方插件 | 状态 |
|-------|------|------|----------|------|
| Element UI | ✅ | ❌ | `@vxe-ui/plugin-render-element` | 计划支持 |
| Element Plus | ❌ | ✅ | `@vxe-ui/plugin-render-element` | 优先支持 |
| Ant Design Vue | ✅ | ✅ | `@vxe-ui/plugin-render-antd` | 计划支持 |
| Naive UI | ❌ | ✅ | `@vxe-ui/plugin-render-naive` | 计划支持 |

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 快速开始

```bash
# 克隆项目
git clone https://github.com/your-username/via-grid.git
cd via-grid

# 安装 pnpm (如果还没有安装)
npm install -g pnpm

# 一键设置开发环境
pnpm setup
```

### 开发命令

```bash
# 构建所有包
pnpm build

# 启动开发环境
pnpm dev

# 启动文档站点
pnpm dev:docs

# 启动 Vue2 playground
pnpm dev:vue2

# 启动 Vue3 playground  
pnpm dev:vue3

# 运行测试
pnpm test

# 代码检查和格式化
pnpm lint
pnpm format

# 类型检查
pnpm typecheck

# 清理构建产物
pnpm clean
```

### 添加新的字段类型

1. 在 `packages/core/src/types.ts` 中添加类型定义
2. 在适配器中添加组件映射
3. 更新文档和示例

### 添加新的 UI 库支持

1. 在 `packages/core/src/adapters/` 中创建新的适配器
2. 注册到 `AdapterFactory`
3. 添加对应的 peerDependencies

### 发布流程

```bash
# 1. 创建变更集
pnpm changeset

# 2. 更新版本
pnpm version

# 3. 构建所有包
pnpm build

# 4. 发布到 npm
pnpm release
```

## 设计原则

1. **约定优于配置**: 提供合理的默认值，最小配置即可使用，按需覆盖实现定制
2. **API 优先**: 先设计 API，再实现功能，确保接口的一致性和易用性
3. **类型安全**: 完整的 TypeScript 支持，提供智能提示和编译时检查
4. **渐进增强**: 从简单到复杂的使用方式，支持不同层次的定制需求
5. **插件化**: 可扩展的架构设计，支持第三方插件和自定义扩展
6. **性能优先**: 优化渲染性能和内存使用，确保大数据量下的流畅体验
7. **开发体验**: 提供良好的开发和调试体验，简化常见开发任务
8. **生态兼容**: 充分利用 vxe-table 和各 UI 库的原生能力，保持生态一致性

## 贡献指南

1. Fork 项目
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交 Pull Request

## 许可证

[MIT](./LICENSE) © 2025 ViaGrid

## 参考文档

请参考以下链接中的文档内容，以确保 API 设计对齐当前主流实践，并充分利用已有组件库能力：

- [https://deepwiki.com/x-extends/vxe-pc-ui](https://deepwiki.com/x-extends/vxe-pc-ui)
- [https://deepwiki.com/x-extends/vxe-table](https://deepwiki.com/x-extends/vxe-table)

---

**注意**: 本文档将随着项目开发进度持续更新，请关注最新版本。