# es-toolkit 迁移指南

## 概述

Via Grid 现在直接使用 [es-toolkit](https://es-toolkit.slash.page/) 而不是通过中间层映射。这样做的好处：

- ⚡ **更好的性能**: 减少函数调用层级
- 📦 **更小的包体积**: 避免重复代码
- 🔧 **更好的 Tree-shaking**: 直接导入需要的函数
- 📝 **更清晰的依赖**: 明确知道使用的是 es-toolkit 的函数

## 迁移步骤

### 1. 更新导入语句

**之前 (通过 @via-grid/shared 映射):**
```typescript
import {
  isEmpty,
  isNotEmpty,
  deepMerge,
  isObject,
  isFunction,
  debounce,
  cloneDeep
} from '@via-grid/shared'
```

**现在 (直接从 es-toolkit 导入):**
```typescript
// 直接从 es-toolkit 导入
import {
  isEmpty,
  isNotEmpty,
  merge, // 注意：es-toolkit 中是 merge，不是 deepMerge
  isObject,
  isFunction,
  debounce,
  cloneDeep
} from 'es-toolkit'

// 从 @via-grid/shared 导入项目特定的工具函数
import {
  generateId,
  detectVueVersion,
  detectUILibrary,
  formatSlotName,
  parseSlotName
} from '@via-grid/shared'
```

### 2. 函数名称变更

| 旧名称 | 新名称 | 说明 |
|--------|--------|------|
| `deepMerge` | `merge` | es-toolkit 中的深度合并函数名为 `merge` |

### 3. 更新使用方式

**深度合并对象:**
```typescript
// 之前
const result = deepMerge(target, source1, source2)

// 现在
import { merge } from 'es-toolkit'
const result = merge(target, source1, source2)
```

**其他函数保持不变:**
```typescript
// 这些函数的使用方式完全相同
isEmpty(value)
isNotEmpty(value)
isObject(value)
isFunction(value)
debounce(fn, delay)
cloneDeep(obj)
```

## 项目特定工具函数

以下函数仍然从 `@via-grid/shared` 导入：

```typescript
import {
  generateId,        // 生成唯一 ID
  detectVueVersion,  // 检测 Vue 版本
  detectUILibrary,   // 检测 UI 库
  formatSlotName,    // 格式化插槽名称
  parseSlotName      // 解析插槽名称
} from '@via-grid/shared'
```

## 完整示例

### 更新前
```typescript
import {
  isEmpty,
  deepMerge,
  isObject,
  generateId,
  formatSlotName
} from '@via-grid/shared'

const config = deepMerge(defaultConfig, userConfig)
const id = generateId('via-grid')
const slotName = formatSlotName('table', 'name')

if (!isEmpty(config) && isObject(config)) {
  // 处理配置
}
```

### 更新后
```typescript
import { isEmpty, merge, isObject } from 'es-toolkit'
import { generateId, formatSlotName } from '@via-grid/shared'

const config = merge(defaultConfig, userConfig) // 注意：deepMerge -> merge
const id = generateId('via-grid')
const slotName = formatSlotName('table', 'name')

if (!isEmpty(config) && isObject(config)) {
  // 处理配置
}
```

## 包依赖更新

确保在你的 `package.json` 中添加 es-toolkit 依赖：

```json
{
  "dependencies": {
    "es-toolkit": "^1.28.1"
  }
}
```

## 为什么选择 es-toolkit

1. **性能优势**: es-toolkit 比 lodash 快 2-3 倍
2. **包体积**: 更小的打包体积，更好的 Tree-shaking
3. **TypeScript 原生**: 完整的类型支持
4. **现代化**: 使用现代 JavaScript 特性
5. **维护活跃**: 积极维护和更新

## 常见问题

### Q: 为什么不继续使用映射函数？
A: 直接使用 es-toolkit 可以减少函数调用层级，提高性能，并且让依赖关系更加清晰。

### Q: 如何确保兼容性？
A: es-toolkit 的 API 与 lodash 类似，大部分函数可以直接替换。主要区别是函数名称（如 `deepMerge` -> `merge`）。

### Q: 是否需要更新现有代码？
A: 建议逐步迁移。新代码直接使用 es-toolkit，现有代码可以在维护时逐步更新。

## 相关链接

- [es-toolkit 官方文档](https://es-toolkit.slash.page/)
- [es-toolkit GitHub](https://github.com/toss/es-toolkit)
- [性能对比](https://es-toolkit.slash.page/performance.html)
