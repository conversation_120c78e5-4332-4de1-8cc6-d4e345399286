# VXE 系列组件配置式 API 核心用法

本文档整理了 vxe-form 和 vxe-toolbar 的配置式 API 核心用法，为 via-grid 项目的重构提供技术基础。

## vxe-form 配置式 API

### 核心概念

vxe-form 支持通过 `items` 属性进行配置式表单开发，这是其设计初衷，比基础的 `vxe-form-item` 写法更高效。

### 基础用法

```vue
<template>
  <vxe-form v-bind="formOptions">
    <template #action>
      <vxe-button type="reset">重置</vxe-button>
      <vxe-button type="submit" status="primary">提交</vxe-button>
    </template>
  </vxe-form>
</template>

<script setup>
import { reactive } from 'vue'

const formOptions = reactive({
  titleWidth: 120,
  titleAlign: 'right',
  data: {
    name: 'test1',
    nickname: '',
    sex: '',
    role: ''
  },
  items: [
    { 
      field: 'name', 
      title: 'Name', 
      span: 24, 
      itemRender: { name: 'VxeInput' } 
    },
    { 
      field: 'sex', 
      title: 'Sex', 
      span: 12, 
      itemRender: {
        name: 'VxeSelect',
        options: [
          { label: '女', value: 'Women' },
          { label: '男', value: 'Man' }
        ]
      }
    },
    { 
      field: 'role', 
      title: 'Role', 
      span: 12, 
      itemRender: {
        name: 'VxeSelect',
        options: [
          { label: 'Develop', value: '1' },
          { label: 'PM', value: '2' },
          { label: 'Testing', value: '3' }
        ]
      }
    },
    { 
      align: 'center', 
      span: 24, 
      slots: { default: 'action' } 
    }
  ]
})
</script>
```

### 高级特性

#### 1. 表单项联动

```javascript
const sexItemRender = reactive({
  name: 'VxeSelect',
  options: [
    { label: '女', value: 'Women' },
    { label: '男', value: 'Man' }
  ],
  events: {
    change ({ data }) {
      const isDisabled = data.sex !== 'Women'
      roleItemRender.props.disabled = isDisabled
    }
  }
})

const roleItemRender = reactive({
  name: 'VxeSelect',
  props: {
    disabled: true
  },
  options: [
    { label: 'Develop', value: '1' },
    { label: 'PM', value: '2' }
  ]
})
```

#### 2. 日期范围联动

```javascript
const startTimeItemRender = reactive({
  name: 'VxeDatePicker',
  props: {
    disabledMethod ({ date }) {
      const endTime = formOptions.data.endTime
      if (endTime) {
        const eDate = XEUtils.toStringDate(endTime)
        return date >= eDate
      }
      return false
    }
  }
})
```

### 配置项说明

- `field`: 字段名，对应 data 中的属性
- `title`: 字段标题
- `span`: 栅格占位格数（24格系统）
- `itemRender`: 渲染器配置
  - `name`: 组件名称（如 VxeInput、VxeSelect 等）
  - `props`: 组件属性
  - `options`: 选项数据（适用于选择类组件）
  - `events`: 事件处理器
- `slots`: 插槽配置

## vxe-toolbar 配置式 API

### 核心概念

vxe-toolbar 提供了灵活的工具栏配置，支持左右两侧按钮布局，通过插槽系统实现高度自定义。

### 基础用法

```vue
<template>
  <vxe-toolbar>
    <!-- 左侧按钮区域 -->
    <template #buttons>
      <vxe-button status="primary" @click="handleAdd">新建</vxe-button>
      <vxe-button @click="handleEdit">编辑</vxe-button>
      <vxe-button status="danger" @click="handleDelete">删除</vxe-button>
    </template>
    
    <!-- 右侧工具区域 -->
    <template #tools>
      <vxe-button icon="vxe-icon-refresh" @click="handleRefresh" content="刷新"></vxe-button>
      <vxe-button icon="vxe-icon-search" @click="handleSearch" content="筛选"></vxe-button>
    </template>
  </vxe-toolbar>
</template>
```

### 与表格集成

```vue
<template>
  <div>
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <vxe-button status="primary" @click="insertEvent">新增</vxe-button>
        <vxe-button @click="removeEvent">删除</vxe-button>
      </template>
      <template #tools>
        <vxe-button icon="vxe-icon-refresh" @click="refreshEvent"></vxe-button>
      </template>
    </vxe-toolbar>
    
    <vxe-table
      ref="tableRef"
      :data="tableData"
      :toolbar-config="toolbarConfig"
    >
      <!-- 表格列配置 -->
    </vxe-table>
  </div>
</template>

<script setup>
const toolbarConfig = {
  // 关联工具栏
  slots: {
    buttons: 'toolbar_buttons',
    tools: 'toolbar_tools'
  }
}
</script>
```

### 插槽说明

- `#buttons`: 左侧按钮区域，通常放置主要操作按钮（新增、编辑、删除等）
- `#tools`: 右侧工具区域，通常放置辅助功能按钮（刷新、搜索、导出等）

## 与 via-grid 集成策略

### 1. ViaForm 重构方向

- 将当前的 `vxe-form-item` 循环改为 `items` 配置
- 利用 `itemRender` 实现字段渲染器
- 通过 `events` 实现字段联动
- 保持现有的插槽系统兼容性

### 2. ViaToolbar 重构方向

- 基于 `vxe-toolbar` 而不是重新封装
- 利用 `#buttons` 和 `#tools` 插槽
- 保持现有的按钮配置 API
- 增强与 vxe-table 的集成

### 3. 优势

- **更少的代码**: 利用 vxe 的配置式 API 减少模板代码
- **更强的能力**: 充分利用 vxe 的内置功能
- **更好的性能**: 减少不必要的组件嵌套
- **更易维护**: 遵循 vxe 的设计模式

## 参考资源

- [vxe-form 官方文档](https://vxeui.com)
- [vxe-toolbar 官方文档](https://vxeui.com)
- [配置式表单联动示例](https://blog.csdn.net/chenhdowue/article/details/143449276)
- [工具栏按钮配置示例](https://blog.csdn.net/2301_76671906/article/details/137111041)
