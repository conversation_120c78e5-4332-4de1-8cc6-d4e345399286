#!/usr/bin/env tsx

/**
 * Via Grid 构建脚本
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import { resolve } from 'path'

const packages = ['shared', 'core', 'vue2', 'vue3']

async function build() {
  console.log('🚀 开始构建 Via Grid 包...')

  for (const pkg of packages) {
    const pkgPath = resolve(process.cwd(), 'packages', pkg)
    
    if (!existsSync(pkgPath)) {
      console.warn(`⚠️  包 ${pkg} 不存在，跳过构建`)
      continue
    }

    console.log(`📦 构建包: ${pkg}`)
    
    try {
      execSync('pnpm run build', {
        cwd: pkgPath,
        stdio: 'inherit'
      })
      console.log(`✅ 包 ${pkg} 构建成功`)
    } catch (error) {
      console.error(`❌ 包 ${pkg} 构建失败:`, error)
      process.exit(1)
    }
  }

  console.log('🎉 所有包构建完成！')
}

build().catch(console.error)
