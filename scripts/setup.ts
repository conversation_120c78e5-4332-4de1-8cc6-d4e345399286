#!/usr/bin/env tsx

/**
 * Via Grid 开发环境设置脚本
 */

import { execSync } from 'child_process'

async function setup() {
  console.log('🔧 设置 Via Grid 开发环境...')

  try {
    // 安装依赖
    console.log('📦 安装依赖...')
    execSync('pnpm install', { stdio: 'inherit' })

    // 构建包
    console.log('🏗️  构建包...')
    execSync('pnpm run build', { stdio: 'inherit' })

    // 运行类型检查
    console.log('🔍 运行类型检查...')
    execSync('pnpm run typecheck', { stdio: 'inherit' })

    // 运行代码检查
    console.log('🧹 运行代码检查...')
    execSync('pnpm run lint', { stdio: 'inherit' })

    console.log('✅ 开发环境设置完成！')
    console.log('')
    console.log('🚀 可用命令:')
    console.log('  pnpm dev        - 启动开发环境')
    console.log('  pnpm dev:vue3   - 启动 Vue3 playground')
    console.log('  pnpm dev:vue2   - 启动 Vue2 playground')
    console.log('  pnpm dev:docs   - 启动文档站点')
    console.log('  pnpm build      - 构建所有包')
    console.log('  pnpm test       - 运行测试')
    console.log('  pnpm lint       - 代码检查')
    console.log('  pnpm format     - 代码格式化')

  } catch (error) {
    console.error('❌ 设置失败:', error)
    process.exit(1)
  }
}

setup().catch(console.error)
