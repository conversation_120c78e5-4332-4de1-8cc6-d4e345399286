import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  presetWind,
  transformerDirectives,
  transformerVariantGroup
} from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetWind(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true
    })
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup()
  ],
  shortcuts: [
    // Via Grid 组件样式快捷方式
    {
      'via-grid': 'w-full h-full flex flex-col',
      'via-grid-search': 'mb-4 p-4 bg-gray-50 rounded-lg',
      'via-grid-toolbar': 'mb-4 flex items-center justify-between',
      'via-grid-table': 'flex-1 min-h-0',
      'via-grid-pager': 'mt-4 flex justify-end'
    }
  ],
  theme: {
    colors: {
      primary: 'var(--via-grid-primary-color, #409eff)',
      success: 'var(--via-grid-success-color, #67c23a)',
      warning: 'var(--via-grid-warning-color, #e6a23c)',
      danger: 'var(--via-grid-danger-color, #f56c6c)',
      info: 'var(--via-grid-info-color, #909399)'
    }
  }
})
