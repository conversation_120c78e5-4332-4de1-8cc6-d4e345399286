/**
 * Via Grid Vue3 版本入口文件
 * 基于 vxe-table v4.x 和 Composition API 实现
 */

// 导出所有组件
export { default as ViaGrid } from './components/ViaGrid.vue'
export { default as ViaSearch } from './components/ViaSearch.vue'
export { default as ViaToolbar } from './components/ViaToolbar.vue'
export { default as ViaTable } from './components/ViaTable.vue'
export { default as ViaPager } from './components/ViaPager.vue'
export { default as ViaForm } from './components/ViaForm.vue'
export { default as ViaInfo } from './components/ViaInfo.vue'

// 导出所有 Composables
export * from './composables'

// 导出类型定义
export * from './types'

// 导出工具函数
export * from './utils'

// 导出插件安装函数
export { default as install } from './install'

// 导出核心功能
export * from '@via-grid/core'
export * from '@via-grid/shared'

// 导出常用验证规则
export { commonRules } from './composables/useValidation'

// 导出国际化相关
export {
  createGlobalI18n,
  getGlobalI18n,
  type SupportedLocale
} from './composables/useI18n'

// 导出错误处理相关
export {
  createGlobalErrorHandler,
  type ErrorType,
  type ErrorInfo
} from './composables/useError'

// 导出加载管理相关
export {
  createGlobalLoadingManager,
  type LoadingTask
} from './composables/useLoading'
