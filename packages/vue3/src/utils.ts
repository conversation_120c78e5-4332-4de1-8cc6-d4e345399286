/**
 * Via Grid Vue3 版本工具函数
 */

import { unref, type Ref } from 'vue'
import { isRef } from 'vue'

// 导出共享工具函数
export * from '@via-grid/shared'

/**
 * 解包 ref 值
 */
export function unrefValue<T>(val: T | Ref<T>): T {
  return isRef(val) ? unref(val) : val
}

/**
 * 创建组件 ID
 */
export function createComponentId(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
}

/**
 * 合并 class
 */
export function mergeClass(...classes: (string | undefined | null)[]): string {
  return classes.filter(Boolean).join(' ')
}

/**
 * 合并 style
 */
export function mergeStyle(...styles: (string | Record<string, any> | undefined | null)[]): Record<string, any> {
  const result: Record<string, any> = {}
  
  for (const style of styles) {
    if (!style) continue
    
    if (typeof style === 'string') {
      // 解析字符串样式
      const pairs = style.split(';').filter(Boolean)
      for (const pair of pairs) {
        const [key, value] = pair.split(':').map(s => s.trim())
        if (key && value) {
          result[key] = value
        }
      }
    } else if (typeof style === 'object') {
      Object.assign(result, style)
    }
  }
  
  return result
}

/**
 * 深度合并对象（Vue3 响应式友好）
 */
export function deepMergeReactive<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target
  
  const source = sources.shift()
  if (!source) return target
  
  for (const key in source) {
    const sourceValue = source[key]
    const targetValue = target[key]
    
    if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
      if (!targetValue || typeof targetValue !== 'object') {
        target[key] = {} as any
      }
      deepMergeReactive(target[key], sourceValue)
    } else {
      target[key] = sourceValue as any
    }
  }
  
  return deepMergeReactive(target, ...sources)
}

/**
 * 检查是否为空值
 */
export function isEmptyValue(value: any): boolean {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 防抖函数（Vue3 响应式友好）
 */
export function debounceRef<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }
}

/**
 * 节流函数（Vue3 响应式友好）
 */
export function throttleRef<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      fn(...args)
    }
  }
}
