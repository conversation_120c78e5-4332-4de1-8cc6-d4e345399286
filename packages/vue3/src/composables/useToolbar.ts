/**
 * useToolbar Composable
 * 工具栏功能相关逻辑
 */

import { ref, computed, type Ref } from 'vue'
import { useGridContext } from './useGrid'

/**
 * 工具栏按钮配置
 */
export interface ToolbarButton {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  disabled?: boolean | (() => boolean)
  visible?: boolean | (() => boolean)
  handler: () => void | Promise<void>
}

/**
 * useToolbar 选项
 */
export interface UseToolbarOptions {
  // 自定义按钮
  buttons?: ToolbarButton[]
  // 是否显示默认按钮
  showDefaultButtons?: boolean
  // 默认按钮配置
  defaultButtons?: {
    add?: boolean
    edit?: boolean
    delete?: boolean
    refresh?: boolean
    export?: boolean
    import?: boolean
  }
}

/**
 * useToolbar 返回值
 */
export interface UseToolbarReturn {
  // 状态
  buttons: Ref<ToolbarButton[]>
  selectedCount: Ref<number>
  
  // 默认操作方法
  handleAdd(): void
  handleEdit(): void
  handleDelete(): void
  handleRefresh(): void
  handleExport(): void
  handleImport(): void
  
  // 工具方法
  addButton(button: ToolbarButton): void
  removeButton(key: string): void
  updateButton(key: string, updates: Partial<ToolbarButton>): void
  getButton(key: string): ToolbarButton | undefined
  isButtonVisible(button: ToolbarButton): boolean
  isButtonDisabled(button: ToolbarButton): boolean
}

/**
 * useToolbar Composable
 */
export function useToolbar(options: UseToolbarOptions = {}): UseToolbarReturn {
  const {
    buttons: customButtons = [],
    showDefaultButtons = true,
    defaultButtons = {
      add: true,
      edit: true,
      delete: true,
      refresh: true,
      export: false,
      import: false
    }
  } = options
  
  const context = useGridContext()
  
  // 按钮列表
  const buttons = ref<ToolbarButton[]>([])
  
  // 选中行数量
  const selectedCount = computed(() => {
    // 这里需要从表格组件获取选中行数量
    // 暂时返回 0，实际使用时需要与 useTable 集成
    return 0
  })
  
  // 默认操作方法
  const handleAdd = () => {
    context.openForm('add')
  }
  
  const handleEdit = () => {
    // 需要获取选中的行
    console.log('Edit selected rows')
  }
  
  const handleDelete = async () => {
    // 需要获取选中的行并删除
    console.log('Delete selected rows')
  }
  
  const handleRefresh = () => {
    context.search()
  }
  
  const handleExport = () => {
    // 导出功能
    console.log('Export data')
  }
  
  const handleImport = () => {
    // 导入功能
    console.log('Import data')
  }
  
  // 初始化默认按钮
  const initDefaultButtons = () => {
    const defaultButtonConfigs: ToolbarButton[] = []
    
    if (defaultButtons.add) {
      defaultButtonConfigs.push({
        key: 'add',
        label: '新增',
        icon: 'plus',
        type: 'primary',
        handler: handleAdd
      })
    }
    
    if (defaultButtons.edit) {
      defaultButtonConfigs.push({
        key: 'edit',
        label: '编辑',
        icon: 'edit',
        disabled: () => selectedCount.value !== 1,
        handler: handleEdit
      })
    }
    
    if (defaultButtons.delete) {
      defaultButtonConfigs.push({
        key: 'delete',
        label: '删除',
        icon: 'delete',
        type: 'danger',
        disabled: () => selectedCount.value === 0,
        handler: handleDelete
      })
    }
    
    if (defaultButtons.refresh) {
      defaultButtonConfigs.push({
        key: 'refresh',
        label: '刷新',
        icon: 'refresh',
        handler: handleRefresh
      })
    }
    
    if (defaultButtons.export) {
      defaultButtonConfigs.push({
        key: 'export',
        label: '导出',
        icon: 'download',
        handler: handleExport
      })
    }
    
    if (defaultButtons.import) {
      defaultButtonConfigs.push({
        key: 'import',
        label: '导入',
        icon: 'upload',
        handler: handleImport
      })
    }
    
    return defaultButtonConfigs
  }
  
  // 初始化按钮
  if (showDefaultButtons) {
    buttons.value = [...initDefaultButtons(), ...customButtons]
  } else {
    buttons.value = [...customButtons]
  }
  
  // 添加按钮
  const addButton = (button: ToolbarButton) => {
    const existingIndex = buttons.value.findIndex(b => b.key === button.key)
    if (existingIndex !== -1) {
      buttons.value[existingIndex] = button
    } else {
      buttons.value.push(button)
    }
  }
  
  // 移除按钮
  const removeButton = (key: string) => {
    const index = buttons.value.findIndex(b => b.key === key)
    if (index !== -1) {
      buttons.value.splice(index, 1)
    }
  }
  
  // 更新按钮
  const updateButton = (key: string, updates: Partial<ToolbarButton>) => {
    const button = buttons.value.find(b => b.key === key)
    if (button) {
      Object.assign(button, updates)
    }
  }
  
  // 获取按钮
  const getButton = (key: string): ToolbarButton | undefined => {
    return buttons.value.find(b => b.key === key)
  }
  
  // 检查按钮是否可见
  const isButtonVisible = (button: ToolbarButton): boolean => {
    if (button.visible === undefined) return true
    if (typeof button.visible === 'boolean') return button.visible
    if (typeof button.visible === 'function') return button.visible()
    return true
  }
  
  // 检查按钮是否禁用
  const isButtonDisabled = (button: ToolbarButton): boolean => {
    if (button.disabled === undefined) return false
    if (typeof button.disabled === 'boolean') return button.disabled
    if (typeof button.disabled === 'function') return button.disabled()
    return false
  }
  
  return {
    // 状态
    buttons,
    selectedCount,
    
    // 默认操作方法
    handleAdd,
    handleEdit,
    handleDelete,
    handleRefresh,
    handleExport,
    handleImport,
    
    // 工具方法
    addButton,
    removeButton,
    updateButton,
    getButton,
    isButtonVisible,
    isButtonDisabled
  }
}
