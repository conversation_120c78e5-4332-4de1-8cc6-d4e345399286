/**
 * usePager Composable
 * 分页功能相关逻辑
 */

import { computed, type Ref } from 'vue'
import { useGridContext } from './useGrid'

/**
 * usePager 选项
 */
export interface UsePagerOptions {
  // 每页显示条数选项
  pageSizes?: number[]
  // 分页器布局
  layout?: string
  // 是否显示总数
  showTotal?: boolean
  // 是否显示跳转
  showJumper?: boolean
  // 是否显示每页条数选择器
  showSizer?: boolean
}

/**
 * usePager 返回值
 */
export interface UsePagerReturn {
  // 状态
  currentPage: Ref<number>
  pageSize: Ref<number>
  total: Ref<number>
  
  // 计算属性
  totalPages: Ref<number>
  startIndex: Ref<number>
  endIndex: Ref<number>
  hasNextPage: Ref<boolean>
  hasPrevPage: Ref<boolean>
  
  // 方法
  setPage(page: number): void
  setPageSize(size: number): void
  nextPage(): void
  prevPage(): void
  firstPage(): void
  lastPage(): void
  
  // 工具方法
  getPageInfo(): {
    currentPage: number
    pageSize: number
    total: number
    totalPages: number
    startIndex: number
    endIndex: number
  }
}

/**
 * usePager Composable
 */
export function usePager(options: UsePagerOptions = {}): UsePagerReturn {
  const {
    pageSizes = [10, 20, 50, 100],
    layout = 'total, sizes, prev, pager, next, jumper',
    showTotal = true,
    showJumper = true,
    showSizer = true
  } = options
  
  const context = useGridContext()
  const { currentPage, pageSize, total } = context
  
  // 计算属性
  const totalPages = computed(() => {
    return Math.ceil(total.value / pageSize.value)
  })
  
  const startIndex = computed(() => {
    return (currentPage.value - 1) * pageSize.value + 1
  })
  
  const endIndex = computed(() => {
    const end = currentPage.value * pageSize.value
    return Math.min(end, total.value)
  })
  
  const hasNextPage = computed(() => {
    return currentPage.value < totalPages.value
  })
  
  const hasPrevPage = computed(() => {
    return currentPage.value > 1
  })
  
  // 设置页码
  const setPage = (page: number) => {
    if (page < 1 || page > totalPages.value) return
    
    currentPage.value = page
    context.search()
  }
  
  // 设置每页条数
  const setPageSize = (size: number) => {
    if (size < 1) return
    
    pageSize.value = size
    currentPage.value = 1
    context.search()
  }
  
  // 下一页
  const nextPage = () => {
    if (hasNextPage.value) {
      setPage(currentPage.value + 1)
    }
  }
  
  // 上一页
  const prevPage = () => {
    if (hasPrevPage.value) {
      setPage(currentPage.value - 1)
    }
  }
  
  // 第一页
  const firstPage = () => {
    setPage(1)
  }
  
  // 最后一页
  const lastPage = () => {
    setPage(totalPages.value)
  }
  
  // 获取分页信息
  const getPageInfo = () => {
    return {
      currentPage: currentPage.value,
      pageSize: pageSize.value,
      total: total.value,
      totalPages: totalPages.value,
      startIndex: startIndex.value,
      endIndex: endIndex.value
    }
  }
  
  return {
    // 状态
    currentPage,
    pageSize,
    total,
    
    // 计算属性
    totalPages,
    startIndex,
    endIndex,
    hasNextPage,
    hasPrevPage,
    
    // 方法
    setPage,
    setPageSize,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    
    // 工具方法
    getPageInfo
  }
}
