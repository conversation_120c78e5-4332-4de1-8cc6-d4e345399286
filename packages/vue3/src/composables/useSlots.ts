/**
 * useSlots Composable
 * 插槽系统相关逻辑
 */

import { computed, useSlots as vueUseSlots, type Ref } from 'vue'
import { formatSlotName, parseSlotName } from '@via-grid/shared'

/**
 * 插槽信息
 */
export interface SlotInfo {
  name: string
  scope?: string
  field?: string
  suffix?: string
  exists: boolean
}

/**
 * useSlots 返回值
 */
export interface UseSlotsReturn {
  // 状态
  slots: Ref<Record<string, any>>
  
  // 方法
  hasSlot(name: string): boolean
  getSlot(name: string): any
  hasFieldSlot(scope: string, field: string, suffix?: string): boolean
  getFieldSlot(scope: string, field: string, suffix?: string): any
  
  // 工具方法
  formatSlotName(scope: string, field: string, suffix?: string): string
  parseSlotName(name: string): { scope?: string; field?: string; suffix?: string }
  getSlotInfo(name: string): SlotInfo
  getAllSlotNames(): string[]
  getSlotsByScope(scope: string): Record<string, any>
  getFieldSlots(scope: string, field: string): Record<string, any>
}

/**
 * useSlots Composable
 */
export function useSlots(): UseSlotsReturn {
  const vueSlots = vueUseSlots()
  
  // 计算属性 - 所有插槽
  const slots = computed(() => vueSlots)
  
  // 检查插槽是否存在
  const hasSlot = (name: string): boolean => {
    return !!vueSlots[name]
  }
  
  // 获取插槽
  const getSlot = (name: string) => {
    return vueSlots[name]
  }
  
  // 检查字段插槽是否存在
  const hasFieldSlot = (scope: string, field: string, suffix?: string): boolean => {
    const slotName = formatSlotName(scope, field, suffix)
    return hasSlot(slotName)
  }
  
  // 获取字段插槽
  const getFieldSlot = (scope: string, field: string, suffix?: string) => {
    const slotName = formatSlotName(scope, field, suffix)
    return getSlot(slotName)
  }
  
  // 格式化插槽名称
  const formatSlotNameHelper = (scope: string, field: string, suffix?: string): string => {
    return formatSlotName(scope, field, suffix)
  }
  
  // 解析插槽名称
  const parseSlotNameHelper = (name: string) => {
    return parseSlotName(name)
  }
  
  // 获取插槽信息
  const getSlotInfo = (name: string): SlotInfo => {
    const parsed = parseSlotName(name)
    return {
      name,
      scope: parsed.scope,
      field: parsed.field,
      suffix: parsed.suffix,
      exists: hasSlot(name)
    }
  }
  
  // 获取所有插槽名称
  const getAllSlotNames = (): string[] => {
    return Object.keys(vueSlots)
  }
  
  // 按作用域获取插槽
  const getSlotsByScope = (scope: string): Record<string, any> => {
    const result: Record<string, any> = {}
    
    for (const [name, slot] of Object.entries(vueSlots)) {
      const parsed = parseSlotName(name)
      if (parsed.scope === scope) {
        result[name] = slot
      }
    }
    
    return result
  }
  
  // 获取字段的所有插槽
  const getFieldSlots = (scope: string, field: string): Record<string, any> => {
    const result: Record<string, any> = {}
    const baseSlotName = formatSlotName(scope, field)
    
    for (const [name, slot] of Object.entries(vueSlots)) {
      if (name.startsWith(baseSlotName)) {
        result[name] = slot
      }
    }
    
    return result
  }
  
  return {
    // 状态
    slots,
    
    // 方法
    hasSlot,
    getSlot,
    hasFieldSlot,
    getFieldSlot,
    
    // 工具方法
    formatSlotName: formatSlotNameHelper,
    parseSlotName: parseSlotNameHelper,
    getSlotInfo,
    getAllSlotNames,
    getSlotsByScope,
    getFieldSlots
  }
}
