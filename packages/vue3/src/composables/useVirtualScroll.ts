/**
 * useVirtualScroll Composable
 * 虚拟滚动性能优化
 */

import { ref, computed, onMounted, onUnmounted, nextTick, type Ref } from 'vue'

/**
 * 虚拟滚动选项
 */
export interface UseVirtualScrollOptions {
  // 容器高度
  containerHeight: number
  // 每项高度
  itemHeight: number
  // 缓冲区大小（额外渲染的项目数）
  bufferSize?: number
  // 是否启用虚拟滚动
  enabled?: boolean
}

/**
 * 虚拟滚动返回值
 */
export interface UseVirtualScrollReturn {
  // 状态
  scrollTop: Ref<number>
  visibleStartIndex: Ref<number>
  visibleEndIndex: Ref<number>
  visibleItems: Ref<any[]>
  totalHeight: Ref<number>
  offsetY: Ref<number>
  
  // 方法
  setData(data: any[]): void
  scrollToIndex(index: number): void
  scrollToTop(): void
  scrollToBottom(): void
  
  // 事件处理
  onScroll(event: Event): void
  
  // 工具方法
  getItemOffset(index: number): number
  isItemVisible(index: number): boolean
}

/**
 * useVirtualScroll Composable
 */
export function useVirtualScroll(options: UseVirtualScrollOptions): UseVirtualScrollReturn {
  const {
    containerHeight,
    itemHeight,
    bufferSize = 5,
    enabled = true
  } = options
  
  // 状态
  const scrollTop = ref(0)
  const data = ref<any[]>([])
  
  // 计算属性
  const totalHeight = computed(() => data.value.length * itemHeight)
  
  const visibleCount = computed(() => Math.ceil(containerHeight / itemHeight))
  
  const visibleStartIndex = computed(() => {
    if (!enabled) return 0
    return Math.max(0, Math.floor(scrollTop.value / itemHeight) - bufferSize)
  })
  
  const visibleEndIndex = computed(() => {
    if (!enabled) return data.value.length - 1
    return Math.min(
      data.value.length - 1,
      visibleStartIndex.value + visibleCount.value + bufferSize * 2
    )
  })
  
  const visibleItems = computed(() => {
    if (!enabled) return data.value
    return data.value.slice(visibleStartIndex.value, visibleEndIndex.value + 1)
  })
  
  const offsetY = computed(() => {
    if (!enabled) return 0
    return visibleStartIndex.value * itemHeight
  })
  
  // 设置数据
  const setData = (newData: any[]) => {
    data.value = newData
  }
  
  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    const targetScrollTop = Math.max(0, Math.min(
      index * itemHeight,
      totalHeight.value - containerHeight
    ))
    scrollTop.value = targetScrollTop
  }
  
  // 滚动到顶部
  const scrollToTop = () => {
    scrollTop.value = 0
  }
  
  // 滚动到底部
  const scrollToBottom = () => {
    scrollTop.value = Math.max(0, totalHeight.value - containerHeight)
  }
  
  // 滚动事件处理
  const onScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }
  
  // 获取项目偏移量
  const getItemOffset = (index: number): number => {
    return index * itemHeight
  }
  
  // 检查项目是否可见
  const isItemVisible = (index: number): boolean => {
    return index >= visibleStartIndex.value && index <= visibleEndIndex.value
  }
  
  return {
    // 状态
    scrollTop,
    visibleStartIndex,
    visibleEndIndex,
    visibleItems,
    totalHeight,
    offsetY,
    
    // 方法
    setData,
    scrollToIndex,
    scrollToTop,
    scrollToBottom,
    
    // 事件处理
    onScroll,
    
    // 工具方法
    getItemOffset,
    isItemVisible
  }
}

/**
 * useInfiniteScroll Composable
 * 无限滚动
 */
export interface UseInfiniteScrollOptions {
  // 加载更多的阈值（距离底部多少像素时触发）
  threshold?: number
  // 是否启用
  enabled?: boolean
  // 加载函数
  onLoad?: () => Promise<void> | void
}

export interface UseInfiniteScrollReturn {
  // 状态
  loading: Ref<boolean>
  finished: Ref<boolean>
  
  // 方法
  load(): Promise<void>
  reset(): void
  finish(): void
  
  // 事件处理
  onScroll(event: Event): void
}

export function useInfiniteScroll(options: UseInfiniteScrollOptions = {}): UseInfiniteScrollReturn {
  const {
    threshold = 100,
    enabled = true,
    onLoad
  } = options
  
  // 状态
  const loading = ref(false)
  const finished = ref(false)
  
  // 加载更多
  const load = async () => {
    if (loading.value || finished.value || !enabled) return
    
    loading.value = true
    try {
      if (onLoad) {
        await onLoad()
      }
    } catch (error) {
      console.error('Infinite scroll load error:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 重置状态
  const reset = () => {
    loading.value = false
    finished.value = false
  }
  
  // 标记为完成
  const finish = () => {
    finished.value = true
  }
  
  // 滚动事件处理
  const onScroll = (event: Event) => {
    if (!enabled || loading.value || finished.value) return
    
    const target = event.target as HTMLElement
    const { scrollTop, scrollHeight, clientHeight } = target
    
    // 检查是否接近底部
    if (scrollHeight - scrollTop - clientHeight <= threshold) {
      load()
    }
  }
  
  return {
    // 状态
    loading,
    finished,
    
    // 方法
    load,
    reset,
    finish,
    
    // 事件处理
    onScroll
  }
}

/**
 * useLazyLoad Composable
 * 懒加载
 */
export interface UseLazyLoadOptions {
  // 根元素
  root?: Element | null
  // 根边距
  rootMargin?: string
  // 阈值
  threshold?: number | number[]
}

export interface UseLazyLoadReturn {
  // 状态
  isVisible: Ref<boolean>
  isLoaded: Ref<boolean>
  
  // 方法
  observe(element: Element): void
  unobserve(element: Element): void
  load(): void
}

export function useLazyLoad(options: UseLazyLoadOptions = {}): UseLazyLoadReturn {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0.1
  } = options
  
  // 状态
  const isVisible = ref(false)
  const isLoaded = ref(false)
  
  let observer: IntersectionObserver | null = null
  
  // 创建观察器
  const createObserver = () => {
    if (typeof IntersectionObserver === 'undefined') return
    
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible.value = true
            if (!isLoaded.value) {
              load()
            }
          } else {
            isVisible.value = false
          }
        })
      },
      {
        root,
        rootMargin,
        threshold
      }
    )
  }
  
  // 观察元素
  const observe = (element: Element) => {
    if (!observer) {
      createObserver()
    }
    if (observer) {
      observer.observe(element)
    }
  }
  
  // 取消观察
  const unobserve = (element: Element) => {
    if (observer) {
      observer.unobserve(element)
    }
  }
  
  // 加载
  const load = () => {
    isLoaded.value = true
  }
  
  // 清理
  onUnmounted(() => {
    if (observer) {
      observer.disconnect()
    }
  })
  
  return {
    // 状态
    isVisible,
    isLoaded,
    
    // 方法
    observe,
    unobserve,
    load
  }
}
