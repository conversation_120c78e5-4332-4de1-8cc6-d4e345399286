/**
 * useValidation Composable
 * 数据验证逻辑
 */

import { ref, computed, reactive, type Ref } from 'vue'

/**
 * 验证规则类型
 */
export type ValidatorFunction = (value: any, data?: any) => boolean | string | Promise<boolean | string>

/**
 * 验证规则
 */
export interface ValidationRule {
  // 是否必填
  required?: boolean
  // 最小长度
  minLength?: number
  // 最大长度
  maxLength?: number
  // 最小值
  min?: number
  // 最大值
  max?: number
  // 正则表达式
  pattern?: RegExp
  // 自定义验证函数
  validator?: ValidatorFunction
  // 错误消息
  message?: string
  // 触发时机
  trigger?: 'change' | 'blur' | 'submit'
}

/**
 * 字段验证规则
 */
export interface FieldValidationRules {
  [field: string]: ValidationRule[]
}

/**
 * 验证错误
 */
export interface ValidationError {
  field: string
  message: string
  rule: ValidationRule
}

/**
 * useValidation 选项
 */
export interface UseValidationOptions {
  // 验证规则
  rules?: FieldValidationRules
  // 是否立即验证
  immediate?: boolean
  // 是否在值改变时验证
  validateOnChange?: boolean
  // 是否在失焦时验证
  validateOnBlur?: boolean
}

/**
 * useValidation 返回值
 */
export interface UseValidationReturn {
  // 状态
  errors: Ref<Record<string, string>>
  isValid: Ref<boolean>
  isValidating: Ref<boolean>
  
  // 方法
  validate(data: any): Promise<boolean>
  validateField(field: string, value: any, data?: any): Promise<boolean>
  clearErrors(): void
  clearFieldError(field: string): void
  setFieldError(field: string, message: string): void
  
  // 工具方法
  hasError(field: string): boolean
  getFieldError(field: string): string
  getErrorCount(): number
}

/**
 * useValidation Composable
 */
export function useValidation(options: UseValidationOptions = {}): UseValidationReturn {
  const {
    rules = {},
    immediate = false,
    validateOnChange = true,
    validateOnBlur = true
  } = options
  
  // 状态
  const errors = ref<Record<string, string>>({})
  const isValidating = ref(false)
  
  // 计算属性
  const isValid = computed(() => Object.keys(errors.value).length === 0)
  
  // 验证单个字段
  const validateField = async (field: string, value: any, data: any = {}): Promise<boolean> => {
    const fieldRules = rules[field]
    if (!fieldRules || fieldRules.length === 0) {
      clearFieldError(field)
      return true
    }
    
    for (const rule of fieldRules) {
      const result = await validateRule(field, value, rule, data)
      if (result !== true) {
        setFieldError(field, result)
        return false
      }
    }
    
    clearFieldError(field)
    return true
  }
  
  // 验证所有字段
  const validate = async (data: any): Promise<boolean> => {
    isValidating.value = true
    clearErrors()
    
    try {
      const validationPromises = Object.keys(rules).map(field => 
        validateField(field, data[field], data)
      )
      
      const results = await Promise.all(validationPromises)
      return results.every(result => result)
    } finally {
      isValidating.value = false
    }
  }
  
  // 验证单个规则
  const validateRule = async (
    field: string, 
    value: any, 
    rule: ValidationRule, 
    data: any
  ): Promise<boolean | string> => {
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return rule.message || `${field} 是必填项`
    }
    
    // 如果值为空且不是必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return true
    }
    
    // 最小长度验证
    if (rule.minLength !== undefined && String(value).length < rule.minLength) {
      return rule.message || `${field} 最小长度为 ${rule.minLength}`
    }
    
    // 最大长度验证
    if (rule.maxLength !== undefined && String(value).length > rule.maxLength) {
      return rule.message || `${field} 最大长度为 ${rule.maxLength}`
    }
    
    // 最小值验证
    if (rule.min !== undefined && Number(value) < rule.min) {
      return rule.message || `${field} 最小值为 ${rule.min}`
    }
    
    // 最大值验证
    if (rule.max !== undefined && Number(value) > rule.max) {
      return rule.message || `${field} 最大值为 ${rule.max}`
    }
    
    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(String(value))) {
      return rule.message || `${field} 格式不正确`
    }
    
    // 自定义验证
    if (rule.validator) {
      const result = await rule.validator(value, data)
      if (result !== true) {
        return typeof result === 'string' ? result : (rule.message || `${field} 验证失败`)
      }
    }
    
    return true
  }
  
  // 清除所有错误
  const clearErrors = () => {
    errors.value = {}
  }
  
  // 清除字段错误
  const clearFieldError = (field: string) => {
    delete errors.value[field]
  }
  
  // 设置字段错误
  const setFieldError = (field: string, message: string) => {
    errors.value[field] = message
  }
  
  // 检查字段是否有错误
  const hasError = (field: string): boolean => {
    return !!errors.value[field]
  }
  
  // 获取字段错误
  const getFieldError = (field: string): string => {
    return errors.value[field] || ''
  }
  
  // 获取错误数量
  const getErrorCount = (): number => {
    return Object.keys(errors.value).length
  }
  
  return {
    // 状态
    errors,
    isValid,
    isValidating,
    
    // 方法
    validate,
    validateField,
    clearErrors,
    clearFieldError,
    setFieldError,
    
    // 工具方法
    hasError,
    getFieldError,
    getErrorCount
  }
}

/**
 * 常用验证规则
 */
export const commonRules = {
  // 邮箱验证
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址'
  },
  
  // 手机号验证
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码'
  },
  
  // 身份证验证
  idCard: {
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: '请输入有效的身份证号码'
  },
  
  // URL验证
  url: {
    pattern: /^https?:\/\/.+/,
    message: '请输入有效的URL地址'
  },
  
  // 数字验证
  number: {
    pattern: /^\d+$/,
    message: '请输入数字'
  },
  
  // 密码强度验证
  strongPassword: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    message: '密码必须包含大小写字母、数字和特殊字符，且长度不少于8位'
  }
}
