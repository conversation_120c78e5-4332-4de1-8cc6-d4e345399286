/**
 * useGrid Composable
 * 主要的网格管理逻辑
 */

import { ref, reactive, computed, provide, inject, type Ref, type InjectionKey } from 'vue'
import { SchemaTransformer, createAdapter, type ViaGridConfig, type FieldSchema } from '@via-grid/core'
import { generateId } from '@via-grid/shared'
import type { ViaGridInstance } from '../types'
import { useError } from './useError'
import { useLoading } from './useLoading'
import { useValidation } from './useValidation'
import { getGlobalI18n } from './useI18n'

// 注入键
export const ViaGridKey: InjectionKey<ViaGridContext> = Symbol('ViaGrid')

/**
 * Grid 上下文类型
 */
export interface ViaGridContext {
  config: ViaGridConfig
  transformer: SchemaTransformer
  adapter: any
  gridId: string
  loading: Ref<boolean>
  searchParams: Ref<Record<string, any>>
  tableData: Ref<any[]>
  currentPage: Ref<number>
  pageSize: Ref<number>
  total: Ref<number>
  formVisible: Ref<boolean>
  formMode: Ref<'add' | 'edit'>
  formData: Ref<any>
  infoVisible: Ref<boolean>
  infoData: Ref<any>
  // 增强功能
  error: ReturnType<typeof useError>
  loadingManager: ReturnType<typeof useLoading>
  validation: ReturnType<typeof useValidation>
  i18n: ReturnType<typeof getGlobalI18n>
}

/**
 * useGrid 选项
 */
export interface UseGridOptions {
  config: ViaGridConfig
  immediate?: boolean
}

/**
 * useGrid 返回值
 */
export interface UseGridReturn extends ViaGridInstance {
  // 状态
  loading: Ref<boolean>
  searchParams: Ref<Record<string, any>>
  tableData: Ref<any[]>
  currentPage: Ref<number>
  pageSize: Ref<number>
  total: Ref<number>
  formVisible: Ref<boolean>
  formMode: Ref<'add' | 'edit'>
  formData: Ref<any>
  infoVisible: Ref<boolean>
  infoData: Ref<any>
  
  // 计算属性
  searchFields: Ref<Record<string, FieldSchema>>
  tableFields: Ref<Record<string, FieldSchema>>
  formFields: Ref<Record<string, FieldSchema>>
  infoFields: Ref<Record<string, FieldSchema>>
  
  // 上下文
  context: ViaGridContext
}

/**
 * useGrid Composable
 */
export function useGrid(options: UseGridOptions): UseGridReturn {
  const { config, immediate = true } = options

  // 创建适配器和转换器
  const adapter = createAdapter()
  const transformer = new SchemaTransformer(config)
  const gridId = generateId('via-grid')

  // 增强功能实例
  const error = useError({ autoClear: true, clearDelay: 5000 })
  const loadingManager = useLoading({ defaultTimeout: 30000 })
  const validation = useValidation({ validateOnChange: true })
  const i18n = getGlobalI18n()

  // 响应式状态
  const loading = computed(() => loadingManager.loading.value)
  const searchParams = ref<Record<string, any>>({})
  const tableData = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(config.pager?.pageSize || 10)
  const total = ref(0)
  const formVisible = ref(false)
  const formMode = ref<'add' | 'edit'>('add')
  const formData = ref<any>({})
  const infoVisible = ref(false)
  const infoData = ref<any>({})
  
  // 计算属性 - 各个作用域的字段
  const searchFields = computed(() => transformer.getFieldsForScope('search'))
  const tableFields = computed(() => transformer.getFieldsForScope('table'))
  const formFields = computed(() => transformer.getFieldsForScope('form'))
  const infoFields = computed(() => transformer.getFieldsForScope('info'))
  
  // 创建上下文
  const context: ViaGridContext = {
    config,
    transformer,
    adapter,
    gridId,
    loading,
    searchParams,
    tableData,
    currentPage,
    pageSize,
    total,
    formVisible,
    formMode,
    formData,
    infoVisible,
    infoData,
    // 增强功能
    error,
    loadingManager,
    validation,
    i18n
  }
  
  // 搜索相关方法
  const search = async () => {
    if (!config.api?.search) return

    const taskId = loadingManager.startLoading(i18n.t('common.search'))
    error.clearError()

    try {
      const params = {
        ...searchParams.value,
        page: currentPage.value,
        pageSize: pageSize.value
      }

      const response = await config.api.search(params)

      if (response.data) {
        tableData.value = response.data
        total.value = response.total || 0
      }
    } catch (err) {
      error.setError(err as Error, 'network')
      console.error('Search failed:', err)
    } finally {
      await loadingManager.endLoading(taskId)
    }
  }
  
  const reset = () => {
    searchParams.value = {}
    currentPage.value = 1
    search()
  }
  
  const getSearchParams = () => searchParams.value
  const setSearchParams = (params: Record<string, any>) => {
    searchParams.value = { ...params }
  }
  
  // 表格相关方法
  const refresh = () => search()
  const getTableData = () => tableData.value
  const setTableData = (data: any[]) => {
    tableData.value = data
  }
  
  // 分页相关方法
  const getCurrentPage = () => currentPage.value
  const getPageSize = () => pageSize.value
  const setPage = (page: number) => {
    currentPage.value = page
    search()
  }
  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    search()
  }
  
  // 表单相关方法
  const openForm = (mode: 'add' | 'edit', row?: any) => {
    formMode.value = mode
    formData.value = mode === 'edit' && row ? { ...row } : {}
    formVisible.value = true
  }
  
  const closeForm = () => {
    formVisible.value = false
    formData.value = {}
  }
  
  const submitForm = async () => {
    if (!config.api) return

    // 验证表单数据
    const isValid = await validation.validate(formData.value)
    if (!isValid) {
      error.setError(i18n.t('error.validation'), 'validation')
      return
    }

    const taskId = loadingManager.startLoading(
      formMode.value === 'add' ? i18n.t('common.add') : i18n.t('common.edit')
    )
    error.clearError()

    try {
      if (formMode.value === 'add' && config.api.create) {
        await config.api.create(formData.value)
      } else if (formMode.value === 'edit' && config.api.update) {
        await config.api.update(formData.value)
      }

      closeForm()
      refresh()
    } catch (err) {
      error.setError(err as Error, 'business')
      console.error('Submit failed:', err)
      throw err
    } finally {
      await loadingManager.endLoading(taskId)
    }
  }
  
  // 详情相关方法
  const openInfo = (row: any) => {
    infoData.value = { ...row }
    infoVisible.value = true
  }
  
  const closeInfo = () => {
    infoVisible.value = false
    infoData.value = {}
  }
  
  // 初始化
  if (immediate) {
    search()
  }
  
  // 提供上下文
  provide(ViaGridKey, context)
  
  return {
    // 状态
    loading,
    searchParams,
    tableData,
    currentPage,
    pageSize,
    total,
    formVisible,
    formMode,
    formData,
    infoVisible,
    infoData,

    // 计算属性
    searchFields,
    tableFields,
    formFields,
    infoFields,

    // 方法
    search,
    reset,
    getSearchParams,
    setSearchParams,
    refresh,
    getTableData,
    setTableData,
    getCurrentPage,
    getPageSize,
    setPage,
    setPageSize,
    openForm,
    closeForm,
    submitForm,
    openInfo,
    closeInfo,

    // 增强功能
    error: error.error,
    hasError: error.hasError,
    errorMessage: error.errorMessage,
    clearError: error.clearError,
    setError: error.setError,

    // 上下文
    context
  }
}

/**
 * 注入 Grid 上下文
 */
export function useGridContext(): ViaGridContext {
  const context = inject(ViaGridKey)
  if (!context) {
    throw new Error('useGridContext must be used within ViaGrid component')
  }
  return context
}
