/**
 * useSearch Composable
 * 搜索功能相关逻辑
 */

import { ref, computed, watch, type Ref } from 'vue'
import { useGridContext } from './useGrid'
import { debounceRef } from '../utils'
import type { FieldSchema } from '@via-grid/core'

/**
 * useSearch 选项
 */
export interface UseSearchOptions {
  // 自动搜索延迟（毫秒）
  autoSearchDelay?: number
  // 是否启用自动搜索
  autoSearch?: boolean
  // 初始搜索参数
  initialParams?: Record<string, any>
}

/**
 * useSearch 返回值
 */
export interface UseSearchReturn {
  // 状态
  searchParams: Ref<Record<string, any>>
  searchFields: Ref<Record<string, FieldSchema>>
  
  // 方法
  search(): void
  reset(): void
  setFieldValue(field: string, value: any): void
  getFieldValue(field: string): any
  clearField(field: string): void
  
  // 工具方法
  isFieldVisible(field: string): boolean
  getFieldConfig(field: string): FieldSchema | undefined
}

/**
 * useSearch Composable
 */
export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const {
    autoSearchDelay = 300,
    autoSearch = false,
    initialParams = {}
  } = options
  
  const context = useGridContext()
  const { searchParams, transformer } = context
  
  // 初始化搜索参数
  if (Object.keys(initialParams).length > 0) {
    searchParams.value = { ...initialParams }
  }
  
  // 搜索字段
  const searchFields = computed(() => transformer.getFieldsForScope('search'))
  
  // 防抖搜索
  const debouncedSearch = debounceRef(() => {
    context.currentPage.value = 1
    context.search()
  }, autoSearchDelay)
  
  // 监听搜索参数变化
  if (autoSearch) {
    watch(
      searchParams,
      () => {
        debouncedSearch()
      },
      { deep: true }
    )
  }
  
  // 搜索方法
  const search = () => {
    context.currentPage.value = 1
    context.search()
  }
  
  // 重置方法
  const reset = () => {
    searchParams.value = {}
    context.currentPage.value = 1
    context.search()
  }
  
  // 设置字段值
  const setFieldValue = (field: string, value: any) => {
    searchParams.value[field] = value
  }
  
  // 获取字段值
  const getFieldValue = (field: string) => {
    return searchParams.value[field]
  }
  
  // 清除字段
  const clearField = (field: string) => {
    delete searchParams.value[field]
  }
  
  // 检查字段是否可见
  const isFieldVisible = (field: string): boolean => {
    const fieldConfig = searchFields.value[field]
    if (!fieldConfig) return false
    
    // 检查 hidden 配置
    if (fieldConfig.hidden) {
      if (typeof fieldConfig.hidden === 'function') {
        return !fieldConfig.hidden('search', searchParams.value)
      }
      if (typeof fieldConfig.hidden === 'boolean') {
        return !fieldConfig.hidden
      }
      if (Array.isArray(fieldConfig.hidden)) {
        return !fieldConfig.hidden.includes('search')
      }
    }
    
    return true
  }
  
  // 获取字段配置
  const getFieldConfig = (field: string): FieldSchema | undefined => {
    return searchFields.value[field]
  }
  
  return {
    // 状态
    searchParams,
    searchFields,
    
    // 方法
    search,
    reset,
    setFieldValue,
    getFieldValue,
    clearField,
    
    // 工具方法
    isFieldVisible,
    getFieldConfig
  }
}
