/**
 * useTable Composable
 * 表格功能相关逻辑
 */

import { ref, computed, nextTick, type Ref } from 'vue'
import { useGridContext } from './useGrid'
import type { FieldSchema } from '@via-grid/core'

/**
 * 表格选择模式
 */
export type SelectionMode = 'single' | 'multiple' | 'none'

/**
 * useTable 选项
 */
export interface UseTableOptions {
  // 选择模式
  selectionMode?: SelectionMode
  // 是否显示序号
  showIndex?: boolean
  // 是否显示操作列
  showActions?: boolean
  // 操作列宽度
  actionWidth?: number
}

/**
 * useTable 返回值
 */
export interface UseTableReturn {
  // 状态
  tableData: Ref<any[]>
  tableFields: Ref<Record<string, FieldSchema>>
  selectedRows: Ref<any[]>
  loading: Ref<boolean>
  
  // 方法
  refresh(): void
  setData(data: any[]): void
  selectRow(row: any): void
  selectRows(rows: any[]): void
  clearSelection(): void
  toggleRowSelection(row: any): void
  
  // 操作方法
  handleView(row: any): void
  handleEdit(row: any): void
  handleDelete(row: any): void
  
  // 工具方法
  isRowSelected(row: any): boolean
  getSelectedCount(): number
  isColumnVisible(field: string): boolean
  getColumnConfig(field: string): FieldSchema | undefined
}

/**
 * useTable Composable
 */
export function useTable(options: UseTableOptions = {}): UseTableReturn {
  const {
    selectionMode = 'multiple',
    showIndex = true,
    showActions = true,
    actionWidth = 150
  } = options
  
  const context = useGridContext()
  const { tableData, transformer, loading } = context
  
  // 选中的行
  const selectedRows = ref<any[]>([])
  
  // 表格字段
  const tableFields = computed(() => transformer.getFieldsForScope('table'))
  
  // 刷新数据
  const refresh = () => {
    context.search()
  }
  
  // 设置数据
  const setData = (data: any[]) => {
    tableData.value = data
    clearSelection()
  }
  
  // 选择行
  const selectRow = (row: any) => {
    if (selectionMode === 'none') return
    
    if (selectionMode === 'single') {
      selectedRows.value = [row]
    } else {
      const index = selectedRows.value.findIndex(r => r === row)
      if (index === -1) {
        selectedRows.value.push(row)
      }
    }
  }
  
  // 选择多行
  const selectRows = (rows: any[]) => {
    if (selectionMode === 'none') return
    
    if (selectionMode === 'single') {
      selectedRows.value = rows.slice(0, 1)
    } else {
      selectedRows.value = [...rows]
    }
  }
  
  // 清除选择
  const clearSelection = () => {
    selectedRows.value = []
  }
  
  // 切换行选择状态
  const toggleRowSelection = (row: any) => {
    if (selectionMode === 'none') return
    
    const index = selectedRows.value.findIndex(r => r === row)
    if (index === -1) {
      selectRow(row)
    } else {
      selectedRows.value.splice(index, 1)
    }
  }
  
  // 操作方法
  const handleView = (row: any) => {
    context.openInfo(row)
  }
  
  const handleEdit = (row: any) => {
    context.openForm('edit', row)
  }
  
  const handleDelete = async (row: any) => {
    if (!context.config.api?.delete) return
    
    try {
      loading.value = true
      await context.config.api.delete(row)
      refresh()
    } catch (error) {
      console.error('Delete failed:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 工具方法
  const isRowSelected = (row: any): boolean => {
    return selectedRows.value.includes(row)
  }
  
  const getSelectedCount = (): number => {
    return selectedRows.value.length
  }
  
  const isColumnVisible = (field: string): boolean => {
    const fieldConfig = tableFields.value[field]
    if (!fieldConfig) return false
    
    // 检查 hidden 配置
    if (fieldConfig.hidden) {
      if (typeof fieldConfig.hidden === 'function') {
        return !fieldConfig.hidden('table', {})
      }
      if (typeof fieldConfig.hidden === 'boolean') {
        return !fieldConfig.hidden
      }
      if (Array.isArray(fieldConfig.hidden)) {
        return !fieldConfig.hidden.includes('table')
      }
    }
    
    return true
  }
  
  const getColumnConfig = (field: string): FieldSchema | undefined => {
    return tableFields.value[field]
  }
  
  return {
    // 状态
    tableData,
    tableFields,
    selectedRows,
    loading,
    
    // 方法
    refresh,
    setData,
    selectRow,
    selectRows,
    clearSelection,
    toggleRowSelection,
    
    // 操作方法
    handleView,
    handleEdit,
    handleDelete,
    
    // 工具方法
    isRowSelected,
    getSelectedCount,
    isColumnVisible,
    getColumnConfig
  }
}
