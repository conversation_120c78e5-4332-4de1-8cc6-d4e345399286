/**
 * useI18n Composable
 * 国际化支持
 */

import { ref, computed, type Ref } from 'vue'

/**
 * 语言包类型
 */
export interface LocaleMessages {
  [key: string]: string | LocaleMessages
}

/**
 * 支持的语言
 */
export type SupportedLocale = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR'

/**
 * useI18n 选项
 */
export interface UseI18nOptions {
  // 默认语言
  defaultLocale?: SupportedLocale
  // 语言包
  messages?: Record<SupportedLocale, LocaleMessages>
  // 回退语言
  fallbackLocale?: SupportedLocale
}

/**
 * useI18n 返回值
 */
export interface UseI18nReturn {
  // 状态
  locale: Ref<SupportedLocale>
  messages: Ref<Record<SupportedLocale, LocaleMessages>>
  
  // 方法
  t(key: string, params?: Record<string, any>): string
  setLocale(locale: SupportedLocale): void
  addMessages(locale: SupportedLocale, messages: LocaleMessages): void
  
  // 工具方法
  hasKey(key: string): boolean
  getAvailableLocales(): SupportedLocale[]
}

/**
 * 默认语言包
 */
const defaultMessages: Record<SupportedLocale, LocaleMessages> = {
  'zh-CN': {
    common: {
      search: '搜索',
      reset: '重置',
      add: '新增',
      edit: '编辑',
      delete: '删除',
      view: '查看',
      refresh: '刷新',
      export: '导出',
      import: '导入',
      confirm: '确定',
      cancel: '取消',
      close: '关闭',
      save: '保存',
      loading: '加载中...',
      noData: '暂无数据',
      total: '共 {total} 条',
      selected: '已选择 {count} 项'
    },
    form: {
      required: '{field} 是必填项',
      invalid: '{field} 格式不正确',
      minLength: '{field} 最小长度为 {min}',
      maxLength: '{field} 最大长度为 {max}',
      min: '{field} 最小值为 {min}',
      max: '{field} 最大值为 {max}'
    },
    table: {
      index: '序号',
      action: '操作',
      empty: '暂无数据'
    },
    pager: {
      total: '共 {total} 条',
      pageSize: '每页 {size} 条',
      goto: '跳至',
      page: '页'
    },
    error: {
      network: '网络错误',
      timeout: '请求超时',
      permission: '权限不足',
      validation: '数据验证失败',
      unknown: '未知错误'
    }
  },
  'en-US': {
    common: {
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      view: 'View',
      refresh: 'Refresh',
      export: 'Export',
      import: 'Import',
      confirm: 'Confirm',
      cancel: 'Cancel',
      close: 'Close',
      save: 'Save',
      loading: 'Loading...',
      noData: 'No Data',
      total: 'Total {total} items',
      selected: '{count} items selected'
    },
    form: {
      required: '{field} is required',
      invalid: '{field} format is invalid',
      minLength: '{field} minimum length is {min}',
      maxLength: '{field} maximum length is {max}',
      min: '{field} minimum value is {min}',
      max: '{field} maximum value is {max}'
    },
    table: {
      index: 'Index',
      action: 'Action',
      empty: 'No Data'
    },
    pager: {
      total: 'Total {total} items',
      pageSize: '{size} items per page',
      goto: 'Go to',
      page: 'page'
    },
    error: {
      network: 'Network Error',
      timeout: 'Request Timeout',
      permission: 'Permission Denied',
      validation: 'Validation Failed',
      unknown: 'Unknown Error'
    }
  },
  'ja-JP': {
    common: {
      search: '検索',
      reset: 'リセット',
      add: '追加',
      edit: '編集',
      delete: '削除',
      view: '表示',
      refresh: '更新',
      export: 'エクスポート',
      import: 'インポート',
      confirm: '確認',
      cancel: 'キャンセル',
      close: '閉じる',
      save: '保存',
      loading: '読み込み中...',
      noData: 'データなし',
      total: '合計 {total} 件',
      selected: '{count} 件選択済み'
    },
    form: {
      required: '{field} は必須項目です',
      invalid: '{field} の形式が正しくありません',
      minLength: '{field} の最小文字数は {min} です',
      maxLength: '{field} の最大文字数は {max} です',
      min: '{field} の最小値は {min} です',
      max: '{field} の最大値は {max} です'
    },
    table: {
      index: '番号',
      action: '操作',
      empty: 'データなし'
    },
    pager: {
      total: '合計 {total} 件',
      pageSize: '1ページ {size} 件',
      goto: 'ページへ移動',
      page: 'ページ'
    },
    error: {
      network: 'ネットワークエラー',
      timeout: 'タイムアウト',
      permission: '権限不足',
      validation: 'バリデーションエラー',
      unknown: '不明なエラー'
    }
  },
  'ko-KR': {
    common: {
      search: '검색',
      reset: '초기화',
      add: '추가',
      edit: '편집',
      delete: '삭제',
      view: '보기',
      refresh: '새로고침',
      export: '내보내기',
      import: '가져오기',
      confirm: '확인',
      cancel: '취소',
      close: '닫기',
      save: '저장',
      loading: '로딩 중...',
      noData: '데이터 없음',
      total: '총 {total}개',
      selected: '{count}개 선택됨'
    },
    form: {
      required: '{field}은(는) 필수 항목입니다',
      invalid: '{field} 형식이 올바르지 않습니다',
      minLength: '{field} 최소 길이는 {min}입니다',
      maxLength: '{field} 최대 길이는 {max}입니다',
      min: '{field} 최소값은 {min}입니다',
      max: '{field} 최대값은 {max}입니다'
    },
    table: {
      index: '번호',
      action: '작업',
      empty: '데이터 없음'
    },
    pager: {
      total: '총 {total}개',
      pageSize: '페이지당 {size}개',
      goto: '이동',
      page: '페이지'
    },
    error: {
      network: '네트워크 오류',
      timeout: '요청 시간 초과',
      permission: '권한 부족',
      validation: '유효성 검사 실패',
      unknown: '알 수 없는 오류'
    }
  }
}

/**
 * useI18n Composable
 */
export function useI18n(options: UseI18nOptions = {}): UseI18nReturn {
  const {
    defaultLocale = 'zh-CN',
    messages: customMessages = {},
    fallbackLocale = 'zh-CN'
  } = options
  
  // 状态
  const locale = ref<SupportedLocale>(defaultLocale)
  const messages = ref<Record<SupportedLocale, LocaleMessages>>({
    ...defaultMessages,
    ...customMessages
  })
  
  // 翻译函数
  const t = (key: string, params: Record<string, any> = {}): string => {
    const keys = key.split('.')
    let message: any = messages.value[locale.value]
    
    // 查找翻译
    for (const k of keys) {
      if (message && typeof message === 'object' && k in message) {
        message = message[k]
      } else {
        // 回退到默认语言
        message = messages.value[fallbackLocale]
        for (const k of keys) {
          if (message && typeof message === 'object' && k in message) {
            message = message[k]
          } else {
            return key // 找不到翻译，返回原始key
          }
        }
        break
      }
    }
    
    if (typeof message !== 'string') {
      return key
    }
    
    // 参数替换
    return message.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey] !== undefined ? String(params[paramKey]) : match
    })
  }
  
  // 设置语言
  const setLocale = (newLocale: SupportedLocale) => {
    locale.value = newLocale
  }
  
  // 添加语言包
  const addMessages = (targetLocale: SupportedLocale, newMessages: LocaleMessages) => {
    messages.value[targetLocale] = {
      ...messages.value[targetLocale],
      ...newMessages
    }
  }
  
  // 检查key是否存在
  const hasKey = (key: string): boolean => {
    const keys = key.split('.')
    let message: any = messages.value[locale.value]
    
    for (const k of keys) {
      if (message && typeof message === 'object' && k in message) {
        message = message[k]
      } else {
        return false
      }
    }
    
    return typeof message === 'string'
  }
  
  // 获取可用语言列表
  const getAvailableLocales = (): SupportedLocale[] => {
    return Object.keys(messages.value) as SupportedLocale[]
  }
  
  return {
    // 状态
    locale,
    messages,
    
    // 方法
    t,
    setLocale,
    addMessages,
    
    // 工具方法
    hasKey,
    getAvailableLocales
  }
}

/**
 * 全局国际化实例
 */
let globalI18n: UseI18nReturn | null = null

/**
 * 创建全局国际化实例
 */
export function createGlobalI18n(options?: UseI18nOptions): UseI18nReturn {
  if (!globalI18n) {
    globalI18n = useI18n(options)
  }
  return globalI18n
}

/**
 * 获取全局国际化实例
 */
export function getGlobalI18n(): UseI18nReturn {
  if (!globalI18n) {
    globalI18n = useI18n()
  }
  return globalI18n
}
