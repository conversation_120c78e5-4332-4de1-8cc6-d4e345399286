/**
 * useAdapter Composable
 * 适配器相关逻辑
 */

import { ref, computed, onMounted, type Ref } from 'vue'
import { createAdapter, getBestAdapter, type BaseAdapter } from '@via-grid/core'
import { detectVueVersion, detectUILibrary, type UILibrary, type VueVersion } from '@via-grid/shared'

/**
 * useAdapter 选项
 */
export interface UseAdapterOptions {
  // 指定 UI 库
  uiLibrary?: UILibrary
  // 指定 Vue 版本
  vueVersion?: VueVersion
  // 是否自动检测
  autoDetect?: boolean
}

/**
 * useAdapter 返回值
 */
export interface UseAdapterReturn {
  // 状态
  adapter: Ref<BaseAdapter | null>
  uiLibrary: Ref<UILibrary | null>
  vueVersion: Ref<VueVersion>
  loading: Ref<boolean>
  error: Ref<string | null>
  
  // 方法
  createAdapter(uiLibrary?: UILibrary, vueVersion?: VueVersion): Promise<BaseAdapter>
  switchAdapter(uiLibrary: UILibrary, vueVersion?: VueVersion): Promise<void>
  
  // 工具方法
  isSupported(uiLibrary: UILibrary, vueVersion?: VueVersion): boolean
  getSupportedFieldTypes(): string[]
  renderField(field: any, scope: string, value: any, context?: any): any
}

/**
 * useAdapter Composable
 */
export function useAdapter(options: UseAdapterOptions = {}): UseAdapterReturn {
  const {
    uiLibrary: specifiedUILibrary,
    vueVersion: specifiedVueVersion,
    autoDetect = true
  } = options
  
  // 状态
  const adapter = ref<BaseAdapter | null>(null)
  const uiLibrary = ref<UILibrary | null>(null)
  const vueVersion = ref<VueVersion>('vue3')
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const supportedFieldTypes = computed(() => {
    return adapter.value?.getSupportedFieldTypes() || []
  })
  
  // 创建适配器
  const createAdapterInstance = async (
    targetUILibrary?: UILibrary,
    targetVueVersion?: VueVersion
  ): Promise<BaseAdapter> => {
    loading.value = true
    error.value = null
    
    try {
      let detectedVueVersion = targetVueVersion || specifiedVueVersion
      let detectedUILibrary = targetUILibrary || specifiedUILibrary
      
      // 自动检测
      if (autoDetect) {
        if (!detectedVueVersion) {
          detectedVueVersion = detectVueVersion()
        }
        if (!detectedUILibrary) {
          detectedUILibrary = detectUILibrary()
        }
      }
      
      // 如果仍然没有检测到 UI 库，使用最佳适配器
      let adapterInstance: BaseAdapter
      if (detectedUILibrary && detectedVueVersion) {
        adapterInstance = createAdapter(detectedUILibrary, detectedVueVersion)
      } else {
        adapterInstance = getBestAdapter()
        detectedVueVersion = adapterInstance['vueVersion'] || 'vue3'
        detectedUILibrary = adapterInstance['uiLibrary'] || null
      }
      
      // 更新状态
      adapter.value = adapterInstance
      vueVersion.value = detectedVueVersion
      uiLibrary.value = detectedUILibrary
      
      return adapterInstance
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create adapter'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }
  
  // 切换适配器
  const switchAdapter = async (targetUILibrary: UILibrary, targetVueVersion?: VueVersion) => {
    await createAdapterInstance(targetUILibrary, targetVueVersion)
  }
  
  // 检查是否支持
  const isSupported = (targetUILibrary: UILibrary, targetVueVersion?: VueVersion): boolean => {
    try {
      const testAdapter = createAdapter(targetUILibrary, targetVueVersion || vueVersion.value)
      return !!testAdapter
    } catch {
      return false
    }
  }
  
  // 获取支持的字段类型
  const getSupportedFieldTypes = (): string[] => {
    return supportedFieldTypes.value
  }
  
  // 渲染字段
  const renderField = (field: any, scope: string, value: any, context: any = {}) => {
    if (!adapter.value) {
      console.warn('No adapter available for field rendering')
      return null
    }
    
    try {
      return adapter.value.renderField(field, scope as any, value, context)
    } catch (err) {
      console.error('Field rendering failed:', err)
      return null
    }
  }
  
  // 初始化
  onMounted(async () => {
    try {
      await createAdapterInstance()
    } catch (err) {
      console.error('Failed to initialize adapter:', err)
    }
  })
  
  return {
    // 状态
    adapter,
    uiLibrary,
    vueVersion,
    loading,
    error,
    
    // 方法
    createAdapter: createAdapterInstance,
    switchAdapter,
    
    // 工具方法
    isSupported,
    getSupportedFieldTypes,
    renderField
  }
}
