/**
 * useLoading Composable
 * 统一的加载状态管理
 */

import { ref, computed, type Ref } from 'vue'

/**
 * 加载任务
 */
export interface LoadingTask {
  id: string
  name: string
  startTime: number
  timeout?: number
}

/**
 * useLoading 选项
 */
export interface UseLoadingOptions {
  // 默认超时时间（毫秒）
  defaultTimeout?: number
  // 最小加载时间（毫秒）
  minDuration?: number
  // 加载开始回调
  onStart?: (task: LoadingTask) => void
  // 加载结束回调
  onEnd?: (task: LoadingTask, duration: number) => void
  // 超时回调
  onTimeout?: (task: LoadingTask) => void
}

/**
 * useLoading 返回值
 */
export interface UseLoadingReturn {
  // 状态
  loading: Ref<boolean>
  loadingTasks: Ref<LoadingTask[]>
  loadingCount: Ref<number>
  
  // 方法
  startLoading(name?: string, timeout?: number): string
  endLoading(taskId: string): Promise<void>
  clearAllLoading(): void
  
  // 工具方法
  withLoading<T>(fn: () => Promise<T>, name?: string): Promise<T>
  isTaskLoading(taskId: string): boolean
  getTaskDuration(taskId: string): number
}

/**
 * useLoading Composable
 */
export function useLoading(options: UseLoadingOptions = {}): UseLoadingReturn {
  const {
    defaultTimeout = 30000, // 30秒
    minDuration = 300, // 300毫秒
    onStart,
    onEnd,
    onTimeout
  } = options
  
  // 状态
  const loadingTasks = ref<LoadingTask[]>([])
  
  // 计算属性
  const loading = computed(() => loadingTasks.value.length > 0)
  const loadingCount = computed(() => loadingTasks.value.length)
  
  // 生成任务ID
  const generateTaskId = () => {
    return `loading-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }
  
  // 开始加载
  const startLoading = (name = 'Loading', timeout = defaultTimeout): string => {
    const taskId = generateTaskId()
    const task: LoadingTask = {
      id: taskId,
      name,
      startTime: Date.now(),
      timeout
    }
    
    loadingTasks.value.push(task)
    
    // 调用开始回调
    if (onStart) {
      onStart(task)
    }
    
    // 设置超时
    if (timeout > 0) {
      setTimeout(() => {
        const existingTask = loadingTasks.value.find(t => t.id === taskId)
        if (existingTask) {
          // 任务仍在进行，触发超时
          if (onTimeout) {
            onTimeout(existingTask)
          }
          // 自动结束超时任务
          endLoading(taskId)
        }
      }, timeout)
    }
    
    return taskId
  }
  
  // 结束加载
  const endLoading = async (taskId: string): Promise<void> => {
    const taskIndex = loadingTasks.value.findIndex(t => t.id === taskId)
    if (taskIndex === -1) return
    
    const task = loadingTasks.value[taskIndex]
    const duration = Date.now() - task.startTime
    
    // 确保最小加载时间
    if (duration < minDuration) {
      await new Promise(resolve => setTimeout(resolve, minDuration - duration))
    }
    
    // 移除任务
    loadingTasks.value.splice(taskIndex, 1)
    
    // 调用结束回调
    if (onEnd) {
      onEnd(task, Date.now() - task.startTime)
    }
  }
  
  // 清除所有加载
  const clearAllLoading = () => {
    const tasks = [...loadingTasks.value]
    loadingTasks.value = []
    
    // 调用结束回调
    if (onEnd) {
      tasks.forEach(task => {
        onEnd(task, Date.now() - task.startTime)
      })
    }
  }
  
  // 包装异步函数
  const withLoading = async <T>(fn: () => Promise<T>, name = 'Loading'): Promise<T> => {
    const taskId = startLoading(name)
    try {
      const result = await fn()
      return result
    } finally {
      await endLoading(taskId)
    }
  }
  
  // 检查任务是否在加载
  const isTaskLoading = (taskId: string): boolean => {
    return loadingTasks.value.some(t => t.id === taskId)
  }
  
  // 获取任务持续时间
  const getTaskDuration = (taskId: string): number => {
    const task = loadingTasks.value.find(t => t.id === taskId)
    return task ? Date.now() - task.startTime : 0
  }
  
  return {
    // 状态
    loading,
    loadingTasks,
    loadingCount,
    
    // 方法
    startLoading,
    endLoading,
    clearAllLoading,
    
    // 工具方法
    withLoading,
    isTaskLoading,
    getTaskDuration
  }
}

/**
 * 全局加载管理器
 */
export function createGlobalLoadingManager() {
  const globalLoading = useLoading({
    defaultTimeout: 30000,
    minDuration: 300,
    onStart: (task) => {
      console.log(`[ViaGrid Loading] Started: ${task.name}`)
    },
    onEnd: (task, duration) => {
      console.log(`[ViaGrid Loading] Ended: ${task.name} (${duration}ms)`)
    },
    onTimeout: (task) => {
      console.warn(`[ViaGrid Loading] Timeout: ${task.name}`)
    }
  })
  
  return globalLoading
}
