/**
 * useError Composable
 * 统一的错误处理逻辑
 */

import { ref, computed, type Ref } from 'vue'

/**
 * 错误类型
 */
export type ErrorType = 'network' | 'validation' | 'permission' | 'business' | 'unknown'

/**
 * 错误信息
 */
export interface ErrorInfo {
  type: ErrorType
  message: string
  code?: string | number
  details?: any
  timestamp: number
}

/**
 * useError 选项
 */
export interface UseErrorOptions {
  // 是否自动清除错误
  autoClear?: boolean
  // 自动清除延迟（毫秒）
  clearDelay?: number
  // 错误处理器
  onError?: (error: ErrorInfo) => void
}

/**
 * useError 返回值
 */
export interface UseErrorReturn {
  // 状态
  error: Ref<ErrorInfo | null>
  hasError: Ref<boolean>
  errorMessage: Ref<string>
  
  // 方法
  setError(error: string | Error | ErrorInfo, type?: ErrorType): void
  clearError(): void
  handleAsyncError<T>(promise: Promise<T>): Promise<T | null>
  
  // 工具方法
  isNetworkError(): boolean
  isValidationError(): boolean
  isPermissionError(): boolean
  isBusinessError(): boolean
}

/**
 * useError Composable
 */
export function useError(options: UseErrorOptions = {}): UseErrorReturn {
  const {
    autoClear = false,
    clearDelay = 5000,
    onError
  } = options
  
  // 状态
  const error = ref<ErrorInfo | null>(null)
  
  // 计算属性
  const hasError = computed(() => !!error.value)
  const errorMessage = computed(() => error.value?.message || '')
  
  // 设置错误
  const setError = (errorInput: string | Error | ErrorInfo, type: ErrorType = 'unknown') => {
    let errorInfo: ErrorInfo
    
    if (typeof errorInput === 'string') {
      errorInfo = {
        type,
        message: errorInput,
        timestamp: Date.now()
      }
    } else if (errorInput instanceof Error) {
      errorInfo = {
        type: getErrorType(errorInput),
        message: errorInput.message,
        details: errorInput,
        timestamp: Date.now()
      }
    } else {
      errorInfo = {
        ...errorInput,
        timestamp: Date.now()
      }
    }
    
    error.value = errorInfo
    
    // 调用错误处理器
    if (onError) {
      onError(errorInfo)
    }
    
    // 自动清除
    if (autoClear) {
      setTimeout(() => {
        clearError()
      }, clearDelay)
    }
  }
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  // 处理异步错误
  const handleAsyncError = async <T>(promise: Promise<T>): Promise<T | null> => {
    try {
      return await promise
    } catch (err) {
      setError(err as Error)
      return null
    }
  }
  
  // 错误类型判断
  const isNetworkError = () => error.value?.type === 'network'
  const isValidationError = () => error.value?.type === 'validation'
  const isPermissionError = () => error.value?.type === 'permission'
  const isBusinessError = () => error.value?.type === 'business'
  
  return {
    // 状态
    error,
    hasError,
    errorMessage,
    
    // 方法
    setError,
    clearError,
    handleAsyncError,
    
    // 工具方法
    isNetworkError,
    isValidationError,
    isPermissionError,
    isBusinessError
  }
}

/**
 * 根据错误对象推断错误类型
 */
function getErrorType(error: Error): ErrorType {
  const message = error.message.toLowerCase()
  
  if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
    return 'network'
  }
  
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return 'validation'
  }
  
  if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
    return 'permission'
  }
  
  if (message.includes('business') || message.includes('rule')) {
    return 'business'
  }
  
  return 'unknown'
}

/**
 * 全局错误处理器
 */
export function createGlobalErrorHandler() {
  const globalError = useError({
    autoClear: true,
    clearDelay: 5000,
    onError: (error) => {
      console.error('[ViaGrid Error]', error)
      
      // 可以在这里集成错误上报服务
      // reportError(error)
    }
  })
  
  // 监听全局错误
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      globalError.setError(event.error || event.message, 'unknown')
    })
    
    window.addEventListener('unhandledrejection', (event) => {
      globalError.setError(event.reason, 'unknown')
    })
  }
  
  return globalError
}
