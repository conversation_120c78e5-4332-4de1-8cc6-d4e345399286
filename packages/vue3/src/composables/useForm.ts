/**
 * useForm Composable
 * 表单功能相关逻辑
 */

import { ref, computed, watch, type Ref } from 'vue'
import { useGridContext } from './useGrid'
import type { FieldSchema } from '@via-grid/core'

/**
 * 表单验证规则
 */
export interface FormValidationRule {
  required?: boolean
  message?: string
  validator?: (value: any, formData: any) => boolean | string
}

/**
 * useForm 选项
 */
export interface UseFormOptions {
  // 表单模式
  mode?: 'add' | 'edit'
  // 初始数据
  initialData?: any
  // 验证规则
  rules?: Record<string, FormValidationRule[]>
  // 是否自动验证
  autoValidate?: boolean
}

/**
 * useForm 返回值
 */
export interface UseFormReturn {
  // 状态
  formData: Ref<any>
  formFields: Ref<Record<string, FieldSchema>>
  formVisible: Ref<boolean>
  formMode: Ref<'add' | 'edit'>
  loading: Ref<boolean>
  errors: Ref<Record<string, string>>
  
  // 方法
  open(mode: 'add' | 'edit', data?: any): void
  close(): void
  submit(): Promise<void>
  reset(): void
  validate(): Promise<boolean>
  validateField(field: string): Promise<boolean>
  clearValidation(): void
  
  // 字段操作
  setFieldValue(field: string, value: any): void
  getFieldValue(field: string): any
  
  // 工具方法
  isFieldVisible(field: string): boolean
  isFieldRequired(field: string): boolean
  getFieldConfig(field: string): FieldSchema | undefined
}

/**
 * useForm Composable
 */
export function useForm(options: UseFormOptions = {}): UseFormReturn {
  const {
    mode = 'add',
    initialData = {},
    rules = {},
    autoValidate = true
  } = options
  
  const context = useGridContext()
  const { formData, formFields, formVisible, formMode, loading } = context
  
  // 验证错误
  const errors = ref<Record<string, string>>({})
  
  // 表单字段
  const computedFormFields = computed(() => context.transformer.getFieldsForScope('form'))
  
  // 监听表单数据变化进行自动验证
  if (autoValidate) {
    watch(
      formData,
      () => {
        clearValidation()
      },
      { deep: true }
    )
  }
  
  // 打开表单
  const open = (formMode: 'add' | 'edit', data?: any) => {
    context.openForm(formMode, data)
    clearValidation()
  }
  
  // 关闭表单
  const close = () => {
    context.closeForm()
    clearValidation()
  }
  
  // 提交表单
  const submit = async () => {
    const isValid = await validate()
    if (!isValid) {
      throw new Error('Form validation failed')
    }
    
    await context.submitForm()
  }
  
  // 重置表单
  const reset = () => {
    formData.value = formMode.value === 'edit' ? { ...initialData } : {}
    clearValidation()
  }
  
  // 验证整个表单
  const validate = async (): Promise<boolean> => {
    clearValidation()
    
    const fieldNames = Object.keys(computedFormFields.value)
    const validationPromises = fieldNames.map(field => validateField(field))
    
    const results = await Promise.all(validationPromises)
    return results.every(result => result)
  }
  
  // 验证单个字段
  const validateField = async (field: string): Promise<boolean> => {
    const fieldRules = rules[field]
    if (!fieldRules || fieldRules.length === 0) return true
    
    const value = formData.value[field]
    const fieldConfig = computedFormFields.value[field]
    
    for (const rule of fieldRules) {
      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.value[field] = rule.message || `${fieldConfig?.label || field} 是必填项`
        return false
      }
      
      // 自定义验证
      if (rule.validator) {
        const result = rule.validator(value, formData.value)
        if (result !== true) {
          errors.value[field] = typeof result === 'string' ? result : (rule.message || '验证失败')
          return false
        }
      }
    }
    
    // 清除该字段的错误
    delete errors.value[field]
    return true
  }
  
  // 清除验证
  const clearValidation = () => {
    errors.value = {}
  }
  
  // 设置字段值
  const setFieldValue = (field: string, value: any) => {
    formData.value[field] = value
    
    // 自动验证该字段
    if (autoValidate) {
      validateField(field)
    }
  }
  
  // 获取字段值
  const getFieldValue = (field: string) => {
    return formData.value[field]
  }
  
  // 检查字段是否可见
  const isFieldVisible = (field: string): boolean => {
    const fieldConfig = computedFormFields.value[field]
    if (!fieldConfig) return false
    
    // 检查 hidden 配置
    if (fieldConfig.hidden) {
      if (typeof fieldConfig.hidden === 'function') {
        return !fieldConfig.hidden('form', formData.value)
      }
      if (typeof fieldConfig.hidden === 'boolean') {
        return !fieldConfig.hidden
      }
      if (Array.isArray(fieldConfig.hidden)) {
        return !fieldConfig.hidden.includes('form')
      }
    }
    
    return true
  }
  
  // 检查字段是否必填
  const isFieldRequired = (field: string): boolean => {
    const fieldRules = rules[field]
    if (!fieldRules) return false
    
    return fieldRules.some(rule => rule.required)
  }
  
  // 获取字段配置
  const getFieldConfig = (field: string): FieldSchema | undefined => {
    return computedFormFields.value[field]
  }
  
  return {
    // 状态
    formData,
    formFields: computedFormFields,
    formVisible,
    formMode,
    loading,
    errors,
    
    // 方法
    open,
    close,
    submit,
    reset,
    validate,
    validateField,
    clearValidation,
    
    // 字段操作
    setFieldValue,
    getFieldValue,
    
    // 工具方法
    isFieldVisible,
    isFieldRequired,
    getFieldConfig
  }
}
