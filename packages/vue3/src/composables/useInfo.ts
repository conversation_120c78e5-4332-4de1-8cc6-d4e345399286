/**
 * useInfo Composable
 * 详情展示功能相关逻辑
 */

import { computed, type Ref } from 'vue'
import { useGridContext } from './useGrid'
import type { FieldSchema } from '@via-grid/core'

/**
 * useInfo 选项
 */
export interface UseInfoOptions {
  // 显示模式
  mode?: 'modal' | 'drawer' | 'inline'
  // 字段分组
  groups?: Array<{
    title: string
    fields: string[]
    span?: number
  }>
  // 列数
  columns?: number
}

/**
 * useInfo 返回值
 */
export interface UseInfoReturn {
  // 状态
  infoData: Ref<any>
  infoFields: Ref<Record<string, FieldSchema>>
  infoVisible: Ref<boolean>
  
  // 方法
  open(data: any): void
  close(): void
  
  // 字段操作
  getFieldValue(field: string): any
  getDisplayValue(field: string): string
  
  // 工具方法
  isFieldVisible(field: string): boolean
  getFieldConfig(field: string): FieldSchema | undefined
  getFieldsByGroup(): Array<{
    title: string
    fields: Array<{
      key: string
      config: FieldSchema
      value: any
      displayValue: string
    }>
  }>
}

/**
 * useInfo Composable
 */
export function useInfo(options: UseInfoOptions = {}): UseInfoReturn {
  const {
    mode = 'modal',
    groups = [],
    columns = 2
  } = options
  
  const context = useGridContext()
  const { infoData, infoVisible } = context
  
  // 详情字段
  const infoFields = computed(() => context.transformer.getFieldsForScope('info'))
  
  // 打开详情
  const open = (data: any) => {
    context.openInfo(data)
  }
  
  // 关闭详情
  const close = () => {
    context.closeInfo()
  }
  
  // 获取字段值
  const getFieldValue = (field: string) => {
    return infoData.value?.[field]
  }
  
  // 获取显示值
  const getDisplayValue = (field: string): string => {
    const value = getFieldValue(field)
    const fieldConfig = infoFields.value[field]
    
    if (value === null || value === undefined) {
      return '-'
    }
    
    // 如果有格式化函数
    if (fieldConfig?.formatter) {
      if (typeof fieldConfig.formatter === 'function') {
        return fieldConfig.formatter(value, infoData.value)
      }
    }
    
    // 如果是字典类型
    if (fieldConfig?.dict) {
      if (Array.isArray(fieldConfig.dict)) {
        const option = fieldConfig.dict.find(item => item.value === value)
        return option?.label || String(value)
      }
      if (typeof fieldConfig.dict === 'function') {
        // 异步字典需要特殊处理
        return String(value)
      }
    }
    
    // 根据字段类型格式化
    switch (fieldConfig?.type) {
      case 'date':
        if (value instanceof Date) {
          return value.toLocaleDateString()
        }
        return String(value)
      
      case 'datetime':
        if (value instanceof Date) {
          return value.toLocaleString()
        }
        return String(value)
      
      case 'number':
        if (typeof value === 'number') {
          return value.toLocaleString()
        }
        return String(value)
      
      case 'switch':
        return value ? '是' : '否'
      
      case 'tag':
        if (Array.isArray(value)) {
          return value.join(', ')
        }
        return String(value)
      
      default:
        return String(value)
    }
  }
  
  // 检查字段是否可见
  const isFieldVisible = (field: string): boolean => {
    const fieldConfig = infoFields.value[field]
    if (!fieldConfig) return false
    
    // 检查 hidden 配置
    if (fieldConfig.hidden) {
      if (typeof fieldConfig.hidden === 'function') {
        return !fieldConfig.hidden('info', infoData.value)
      }
      if (typeof fieldConfig.hidden === 'boolean') {
        return !fieldConfig.hidden
      }
      if (Array.isArray(fieldConfig.hidden)) {
        return !fieldConfig.hidden.includes('info')
      }
    }
    
    return true
  }
  
  // 获取字段配置
  const getFieldConfig = (field: string): FieldSchema | undefined => {
    return infoFields.value[field]
  }
  
  // 按分组获取字段
  const getFieldsByGroup = () => {
    if (groups.length === 0) {
      // 如果没有分组，返回所有可见字段作为一个默认分组
      const visibleFields = Object.keys(infoFields.value)
        .filter(field => isFieldVisible(field))
        .map(field => ({
          key: field,
          config: infoFields.value[field],
          value: getFieldValue(field),
          displayValue: getDisplayValue(field)
        }))
      
      return [{
        title: '基本信息',
        fields: visibleFields
      }]
    }
    
    return groups.map(group => ({
      title: group.title,
      fields: group.fields
        .filter(field => isFieldVisible(field))
        .map(field => ({
          key: field,
          config: infoFields.value[field],
          value: getFieldValue(field),
          displayValue: getDisplayValue(field)
        }))
    }))
  }
  
  return {
    // 状态
    infoData,
    infoFields,
    infoVisible,
    
    // 方法
    open,
    close,
    
    // 字段操作
    getFieldValue,
    getDisplayValue,
    
    // 工具方法
    isFieldVisible,
    getFieldConfig,
    getFieldsByGroup
  }
}
