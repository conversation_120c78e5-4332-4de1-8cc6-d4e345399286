/**
 * Via Grid Vue3 插件安装
 */

import type { App } from 'vue'
import type { ViaGridInstallOptions } from './types'

// 导入所有组件
import ViaGrid from './components/ViaGrid.vue'
import ViaSearch from './components/ViaSearch.vue'
import ViaToolbar from './components/ViaToolbar.vue'
import ViaTable from './components/ViaTable.vue'
import ViaPager from './components/ViaPager.vue'
import ViaForm from './components/ViaForm.vue'
import ViaInfo from './components/ViaInfo.vue'

// 组件列表
const components = [
  ViaGrid,
  ViaSearch,
  ViaToolbar,
  ViaTable,
  ViaPager,
  ViaForm,
  ViaInfo
]

/**
 * 安装插件
 */
function install(app: App, options: ViaGridInstallOptions = {}) {
  const { prefix = 'Via' } = options
  
  // 注册所有组件
  components.forEach(component => {
    const name = component.name || component.__name
    if (name) {
      // 使用指定前缀或默认前缀
      const componentName = name.startsWith(prefix) ? name : `${prefix}${name.replace('Via', '')}`
      app.component(componentName, component)
    }
  })
  
  // 全局配置
  if (options.config) {
    app.config.globalProperties.$viaGridConfig = options.config
  }
  
  // 提供全局配置
  app.provide('viaGridGlobalConfig', options.config || {})
}

// 默认导出
export default {
  install,
  version: '0.0.0'
}

// 具名导出
export { install }

// 自动安装（如果在浏览器环境中通过 script 标签引入）
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}
