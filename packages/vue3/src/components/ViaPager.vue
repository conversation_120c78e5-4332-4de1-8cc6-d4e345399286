<template>
  <div class="via-pager">
    <vxe-pager
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      v-bind="pagerConfig"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <!-- 左侧插槽 -->
      <template v-if="$slots['pager-left']" #left>
        <slot name="pager-left" />
      </template>
      
      <!-- 右侧插槽 -->
      <template v-if="$slots['pager-right']" #right>
        <slot name="pager-right" />
      </template>
    </vxe-pager>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePager, useGridContext } from '../composables'

// Props
interface ViaPagerProps {
  currentPage?: number
  pageSize?: number
  total?: number
  pageSizes?: number[]
  layout?: string
  showTotal?: boolean
  showJumper?: boolean
  showSizer?: boolean
}

const props = withDefaults(defineProps<ViaPagerProps>(), {
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  showTotal: true,
  showJumper: true,
  showSizer: true
})

// Emits
interface ViaPagerEmits {
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
}

const emit = defineEmits<ViaPagerEmits>()

// 获取上下文
const context = useGridContext()

// 使用分页逻辑
const pager = usePager({
  pageSizes: props.pageSizes,
  layout: props.layout,
  showTotal: props.showTotal,
  showJumper: props.showJumper,
  showSizer: props.showSizer
})

// 解构分页状态和方法
const {
  currentPage,
  pageSize,
  total,
  setPage,
  setPageSize
} = pager

// 计算属性
const pagerConfig = computed(() => {
  return {
    layouts: props.layout.split(', '),
    pageSizes: props.pageSizes,
    ...context.config.pager?.pagerConfig
  }
})

// 事件处理
const handlePageChange = ({ currentPage: page }: { currentPage: number }) => {
  setPage(page)
  emit('page-change', page)
}

const handlePageSizeChange = ({ pageSize: size }: { pageSize: number }) => {
  setPageSize(size)
  emit('page-size-change', size)
}

// 暴露方法
defineExpose({
  setPage,
  setPageSize,
  nextPage: pager.nextPage,
  prevPage: pager.prevPage,
  firstPage: pager.firstPage,
  lastPage: pager.lastPage,
  getPageInfo: pager.getPageInfo
})
</script>

<style scoped>
.via-pager {
  @apply w-full;
}
</style>
