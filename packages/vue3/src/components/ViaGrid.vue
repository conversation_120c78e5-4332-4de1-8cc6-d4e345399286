<template>
  <div 
    :id="gridId" 
    class="via-grid"
    :style="gridStyle"
  >
    <!-- 搜索区域 -->
    <div v-if="showSearch" class="via-grid-search">
      <ViaSearch 
        v-bind="searchProps"
        @search="handleSearch"
        @reset="handleReset"
      >
        <!-- 透传搜索相关插槽 -->
        <template 
          v-for="(_, name) in searchSlots" 
          :key="name" 
          #[name]="slotProps"
        >
          <slot :name="name" v-bind="slotProps" />
        </template>
      </ViaSearch>
    </div>

    <!-- 搜索到工具栏之间的插槽 -->
    <div v-if="$slots['search-to-toolbar']" class="via-grid-between">
      <slot name="search-to-toolbar" />
    </div>

    <!-- 工具栏区域 -->
    <div v-if="showToolbar" class="via-grid-toolbar">
      <ViaToolbar 
        v-bind="toolbarProps"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
        @refresh="handleRefresh"
      >
        <!-- 透传工具栏相关插槽 -->
        <template 
          v-for="(_, name) in toolbarSlots" 
          :key="name" 
          #[name]="slotProps"
        >
          <slot :name="name" v-bind="slotProps" />
        </template>
      </ViaToolbar>
    </div>

    <!-- 工具栏到表格之间的插槽 -->
    <div v-if="$slots['toolbar-to-table']" class="via-grid-between">
      <slot name="toolbar-to-table" />
    </div>

    <!-- 表格区域 -->
    <div class="via-grid-table">
      <ViaTable 
        v-bind="tableProps"
        v-model:selection="selectedRows"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
      >
        <!-- 透传表格相关插槽 -->
        <template 
          v-for="(_, name) in tableSlots" 
          :key="name" 
          #[name]="slotProps"
        >
          <slot :name="name" v-bind="slotProps" />
        </template>
      </ViaTable>
    </div>

    <!-- 表格到分页之间的插槽 -->
    <div v-if="$slots['table-to-pager']" class="via-grid-between">
      <slot name="table-to-pager" />
    </div>

    <!-- 分页区域 -->
    <div v-if="showPager" class="via-grid-pager">
      <ViaPager 
        v-bind="pagerProps"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 透传分页相关插槽 -->
        <template 
          v-for="(_, name) in pagerSlots" 
          :key="name" 
          #[name]="slotProps"
        >
          <slot :name="name" v-bind="slotProps" />
        </template>
      </ViaPager>
    </div>

    <!-- 表单弹窗 -->
    <ViaForm 
      v-if="formVisible"
      v-bind="formProps"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    >
      <!-- 透传表单相关插槽 -->
      <template 
        v-for="(_, name) in formSlots" 
        :key="name" 
        #[name]="slotProps"
      >
        <slot :name="name" v-bind="slotProps" />
      </template>
    </ViaForm>

    <!-- 详情弹窗 -->
    <ViaInfo 
      v-if="infoVisible"
      v-bind="infoProps"
      @close="handleInfoClose"
    >
      <!-- 透传详情相关插槽 -->
      <template 
        v-for="(_, name) in infoSlots" 
        :key="name" 
        #[name]="slotProps"
      >
        <slot :name="name" v-bind="slotProps" />
      </template>
    </ViaInfo>
  </div>
</template>

<script setup lang="ts">
import { computed, provide } from 'vue'
import { useGrid, useSlots } from '../composables'
import { mergeClass, mergeStyle } from '../utils'
import type { ViaGridProps, ViaGridEmits } from '../types'

import ViaSearch from './ViaSearch.vue'
import ViaToolbar from './ViaToolbar.vue'
import ViaTable from './ViaTable.vue'
import ViaPager from './ViaPager.vue'
import ViaForm from './ViaForm.vue'
import ViaInfo from './ViaInfo.vue'

// Props
const props = withDefaults(defineProps<ViaGridProps>(), {
  loading: false,
  height: 'auto',
  maxHeight: 'none'
})

// Emits
const emit = defineEmits<ViaGridEmits>()

// 使用 Grid 逻辑
const grid = useGrid({
  config: props.config,
  immediate: true
})

// 使用插槽逻辑
const { getSlotsByScope } = useSlots()

// 解构 Grid 状态和方法
const {
  loading,
  searchParams,
  tableData,
  currentPage,
  pageSize,
  total,
  formVisible,
  formMode,
  formData,
  infoVisible,
  infoData,
  selectedRows,
  context
} = grid

// 提供上下文给子组件
provide('viaGridContext', context)

// 计算属性
const gridId = computed(() => context.gridId)

const gridStyle = computed(() => {
  return mergeStyle(
    {
      height: typeof props.height === 'number' ? `${props.height}px` : props.height,
      maxHeight: typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
    }
  )
})

// 显示控制
const showSearch = computed(() => {
  return Object.keys(grid.searchFields.value).length > 0
})

const showToolbar = computed(() => {
  return props.config.toolbar !== false
})

const showPager = computed(() => {
  return props.config.pager !== false
})

// 插槽分组
const searchSlots = computed(() => getSlotsByScope('search'))
const toolbarSlots = computed(() => getSlotsByScope('toolbar'))
const tableSlots = computed(() => getSlotsByScope('table'))
const pagerSlots = computed(() => getSlotsByScope('pager'))
const formSlots = computed(() => getSlotsByScope('form'))
const infoSlots = computed(() => getSlotsByScope('info'))

// 子组件 Props
const searchProps = computed(() => ({
  loading: loading.value,
  ...props.config.search
}))

const toolbarProps = computed(() => ({
  loading: loading.value,
  selectedCount: selectedRows.value?.length || 0,
  ...props.config.toolbar
}))

const tableProps = computed(() => ({
  data: tableData.value,
  loading: loading.value,
  ...props.config.table
}))

const pagerProps = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value,
  ...props.config.pager
}))

const formProps = computed(() => ({
  visible: formVisible.value,
  mode: formMode.value,
  data: formData.value,
  loading: loading.value,
  ...props.config.form
}))

const infoProps = computed(() => ({
  visible: infoVisible.value,
  data: infoData.value,
  ...props.config.info
}))

// 事件处理
const handleSearch = () => {
  grid.search()
  emit('search', searchParams.value)
}

const handleReset = () => {
  grid.reset()
  emit('reset')
}

const handleAdd = () => {
  grid.openForm('add')
  emit('add')
}

const handleEdit = (row?: any) => {
  if (row) {
    grid.openForm('edit', row)
  } else if (selectedRows.value?.length === 1) {
    grid.openForm('edit', selectedRows.value[0])
  }
  emit('edit', row || selectedRows.value?.[0])
}

const handleDelete = (row?: any) => {
  const targetRow = row || selectedRows.value?.[0]
  if (targetRow) {
    emit('delete', targetRow)
  }
}

const handleView = (row: any) => {
  grid.openInfo(row)
  emit('view', row)
}

const handleRefresh = () => {
  grid.refresh()
  emit('refresh')
}

const handlePageChange = (page: number) => {
  grid.setPage(page)
  emit('page-change', page)
}

const handlePageSizeChange = (size: number) => {
  grid.setPageSize(size)
  emit('page-size-change', size)
}

const handleFormSubmit = async () => {
  await grid.submitForm()
}

const handleFormCancel = () => {
  grid.closeForm()
}

const handleInfoClose = () => {
  grid.closeInfo()
}

// 暴露实例方法
defineExpose(grid)
</script>

<style scoped>
.via-grid {
  @apply w-full h-full flex flex-col;
}

.via-grid-search {
  @apply mb-4 p-4 bg-gray-50 rounded-lg;
}

.via-grid-toolbar {
  @apply mb-4 flex items-center justify-between;
}

.via-grid-table {
  @apply flex-1 min-h-0;
}

.via-grid-pager {
  @apply mt-4 flex justify-end;
}

.via-grid-between {
  @apply my-2;
}
</style>
