<template>
  <vxe-toolbar v-bind="toolbarConfig">
    <!-- 左侧按钮区域 -->
    <template #buttons>
      <!-- 默认左侧按钮 -->
      <template v-for="button in leftButtons" :key="button.key">
        <vxe-button
          v-if="isButtonVisible(button)"
          :type="button.type"
          :status="button.status"
          :icon="button.icon"
          :disabled="isButtonDisabled(button)"
          :loading="loading"
          @click="handleButtonClick(button)"
        >
          {{ button.label }}
        </vxe-button>
      </template>

      <!-- 左侧自定义插槽 -->
      <slot name="toolbar-buttons" />
      <slot name="toolbar-left" />
    </template>

    <!-- 右侧工具区域 -->
    <template #tools>
      <!-- 右侧自定义插槽 -->
      <slot name="toolbar-tools" />
      <slot name="toolbar-right" />

      <!-- 默认右侧按钮 -->
      <template v-for="button in rightButtons" :key="button.key">
        <vxe-button
          v-if="isButtonVisible(button)"
          :type="button.type"
          :status="button.status"
          :icon="button.icon"
          :disabled="isButtonDisabled(button)"
          :loading="loading"
          @click="handleButtonClick(button)"
        >
          {{ button.label }}
        </vxe-button>
      </template>
    </template>
  </vxe-toolbar>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToolbar, useGridContext } from '../composables'
import type { ToolbarButton } from '../composables/useToolbar'

// Props
interface ViaToolbarProps {
  loading?: boolean
  selectedCount?: number
  buttons?: ToolbarButton[]
  showDefaultButtons?: boolean
  // vxe-toolbar 原生配置
  perfect?: boolean
  custom?: boolean
  import?: boolean
  export?: boolean
  print?: boolean
  zoom?: boolean
  setting?: boolean
}

const props = withDefaults(defineProps<ViaToolbarProps>(), {
  loading: false,
  selectedCount: 0,
  buttons: () => [],
  showDefaultButtons: true,
  perfect: false,
  custom: false,
  import: false,
  export: false,
  print: false,
  zoom: false,
  setting: false
})

// Emits
interface ViaToolbarEmits {
  add: []
  edit: []
  delete: []
  refresh: []
  export: []
  import: []
}

const emit = defineEmits<ViaToolbarEmits>()

// 获取上下文
const context = useGridContext()

// 使用工具栏逻辑
const toolbar = useToolbar({
  buttons: props.buttons,
  showDefaultButtons: props.showDefaultButtons
})

// 解构工具栏状态和方法
const {
  buttons,
  isButtonVisible,
  isButtonDisabled,
  handleAdd,
  handleEdit,
  handleDelete,
  handleRefresh,
  handleExport,
  handleImport
} = toolbar

// vxe-toolbar 配置
const toolbarConfig = computed(() => {
  return {
    perfect: props.perfect,
    custom: props.custom,
    import: props.import,
    export: props.export,
    print: props.print,
    zoom: props.zoom,
    setting: props.setting,
    ...context.config.toolbar?.toolbarConfig
  }
})

// 计算属性
const leftButtons = computed(() => {
  return buttons.value.filter(button =>
    ['add', 'edit', 'delete'].includes(button.key)
  )
})

const rightButtons = computed(() => {
  return buttons.value.filter(button =>
    ['refresh', 'export', 'import'].includes(button.key)
  )
})

// 事件处理
const handleButtonClick = async (button: ToolbarButton) => {
  try {
    await button.handler()
    
    // 发射对应事件
    switch (button.key) {
      case 'add':
        emit('add')
        break
      case 'edit':
        emit('edit')
        break
      case 'delete':
        emit('delete')
        break
      case 'refresh':
        emit('refresh')
        break
      case 'export':
        emit('export')
        break
      case 'import':
        emit('import')
        break
    }
  } catch (error) {
    console.error('Button action failed:', error)
  }
}

// 暴露方法
defineExpose({
  addButton: toolbar.addButton,
  removeButton: toolbar.removeButton,
  updateButton: toolbar.updateButton,
  getButton: toolbar.getButton
})
</script>

<style scoped>
/* vxe-toolbar 已经提供了完整的样式，这里只需要少量自定义 */
.vxe-toolbar {
  /* 可以在这里添加自定义样式 */
}
</style>
