<template>
  <div class="via-table">
    <vxe-table
      ref="tableRef"
      v-model:data="tableData"
      :loading="loading"
      v-bind="tableConfig"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
    >
      <!-- 选择列 -->
      <vxe-column 
        v-if="showSelection" 
        type="checkbox" 
        width="60" 
        fixed="left"
      />
      
      <!-- 序号列 -->
      <vxe-column 
        v-if="showIndex" 
        type="seq" 
        title="序号" 
        width="80" 
        fixed="left"
      />
      
      <!-- 动态列 -->
      <template v-for="(field, fieldName) in visibleFields" :key="fieldName">
        <vxe-column
          :field="fieldName"
          :title="field.label"
          v-bind="getColumnProps(field)"
        >
          <!-- 列头插槽 -->
          <template v-if="hasFieldSlot('table', fieldName, 'header')" #header="{ column }">
            <slot 
              :name="formatSlotName('table', fieldName, 'header')"
              :field="field"
              :column="column"
            />
          </template>
          
          <!-- 单元格插槽 -->
          <template v-if="hasFieldSlot('table', fieldName)" #default="{ row, column }">
            <slot 
              :name="formatSlotName('table', fieldName)"
              :field="field"
              :value="row[fieldName]"
              :row="row"
              :column="column"
            />
          </template>
          
          <!-- 默认单元格渲染 -->
          <template v-else #default="{ row }">
            <component
              :is="getCellComponent(field, row[fieldName], row)"
              v-bind="getCellProps(field, row[fieldName], row)"
            />
          </template>
        </vxe-column>
      </template>
      
      <!-- 操作列 -->
      <vxe-column 
        v-if="showActions" 
        title="操作" 
        :width="actionWidth"
        fixed="right"
      >
        <template #default="{ row }">
          <div class="via-table-actions">
            <vxe-button 
              v-if="showViewAction"
              type="text" 
              @click="handleView(row)"
            >
              查看
            </vxe-button>
            <vxe-button 
              v-if="showEditAction"
              type="text" 
              @click="handleEdit(row)"
            >
              编辑
            </vxe-button>
            <vxe-button 
              v-if="showDeleteAction"
              type="text" 
              status="danger"
              @click="handleDelete(row)"
            >
              删除
            </vxe-button>
            
            <!-- 操作列后置插槽 -->
            <slot name="table-actions-after" :row="row" />
          </div>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useTable, useSlots, useAdapter, useGridContext } from '../composables'
import type { FieldSchema } from '@via-grid/core'

// Props
interface ViaTableProps {
  data?: any[]
  loading?: boolean
  selection?: any[]
  showSelection?: boolean
  showIndex?: boolean
  showActions?: boolean
  actionWidth?: number
  showViewAction?: boolean
  showEditAction?: boolean
  showDeleteAction?: boolean
}

const props = withDefaults(defineProps<ViaTableProps>(), {
  data: () => [],
  loading: false,
  selection: () => [],
  showSelection: true,
  showIndex: true,
  showActions: true,
  actionWidth: 150,
  showViewAction: true,
  showEditAction: true,
  showDeleteAction: true
})

// Emits
interface ViaTableEmits {
  'update:selection': [selection: any[]]
  view: [row: any]
  edit: [row: any]
  delete: [row: any]
  'selection-change': [selection: any[]]
}

const emit = defineEmits<ViaTableEmits>()

// 获取上下文
const context = useGridContext()

// 使用表格逻辑
const table = useTable({
  selectionMode: props.showSelection ? 'multiple' : 'none',
  showIndex: props.showIndex,
  showActions: props.showActions,
  actionWidth: props.actionWidth
})

// 使用插槽逻辑
const { hasFieldSlot, formatSlotName } = useSlots()

// 使用适配器
const { adapter } = useAdapter()

// 表格引用
const tableRef = ref()

// 解构表格状态和方法
const {
  tableData,
  tableFields,
  selectedRows,
  isColumnVisible,
  getColumnConfig
} = table

// 监听选中行变化
watch(selectedRows, (newSelection) => {
  emit('update:selection', newSelection)
  emit('selection-change', newSelection)
}, { deep: true })

// 计算属性
const visibleFields = computed(() => {
  const fields: Record<string, FieldSchema> = {}
  
  for (const [fieldName, field] of Object.entries(tableFields.value)) {
    if (isColumnVisible(fieldName)) {
      fields[fieldName] = field
    }
  }
  
  return fields
})

const tableConfig = computed(() => {
  return {
    border: true,
    stripe: true,
    resizable: true,
    showOverflow: 'tooltip',
    ...context.config.table?.tableConfig
  }
})

// 获取列属性
const getColumnProps = (field: FieldSchema) => {
  return {
    width: field.table?.width,
    minWidth: field.table?.minWidth || 100,
    fixed: field.table?.fixed,
    sortable: field.table?.sortable,
    align: field.table?.align || 'left',
    headerAlign: field.table?.headerAlign || field.table?.align || 'center',
    ...field.table?.columnProps
  }
}

// 获取单元格组件
const getCellComponent = (field: FieldSchema, value: any, row: any) => {
  if (!adapter.value) return 'span'
  
  const renderConfig = adapter.value.renderField(field, 'table', value, { row })
  return renderConfig?.name || 'span'
}

// 获取单元格属性
const getCellProps = (field: FieldSchema, value: any, row: any) => {
  if (!adapter.value) return { textContent: value }
  
  const renderConfig = adapter.value.renderField(field, 'table', value, { row })
  return renderConfig?.props || { textContent: value }
}

// 事件处理
const handleSelectionChange = () => {
  const selection = tableRef.value?.getCheckboxRecords() || []
  selectedRows.value = selection
}

const handleView = (row: any) => {
  table.handleView(row)
  emit('view', row)
}

const handleEdit = (row: any) => {
  table.handleEdit(row)
  emit('edit', row)
}

const handleDelete = (row: any) => {
  table.handleDelete(row)
  emit('delete', row)
}

// 暴露方法
defineExpose({
  getTableData: table.getTableData,
  setTableData: table.setData,
  getSelection: () => selectedRows.value,
  clearSelection: table.clearSelection,
  refresh: table.refresh
})
</script>

<style scoped>
.via-table {
  @apply w-full h-full;
}

.via-table-actions {
  @apply flex items-center gap-2;
}
</style>
