<template>
  <component
    :is="modalComponent"
    v-model="visible"
    :title="modalTitle"
    v-bind="modalConfig"
    @close="handleClose"
  >
    <div class="via-info">
      <!-- 按分组显示字段 -->
      <template v-for="group in fieldGroups" :key="group.title">
        <div v-if="group.fields.length > 0" class="via-info-group">
          <h3 class="via-info-group-title">{{ group.title }}</h3>
          <div class="via-info-group-content">
            <template v-for="fieldInfo in group.fields" :key="fieldInfo.key">
              <div class="via-info-field">
                <div class="via-info-field-label">
                  {{ fieldInfo.config.label }}:
                </div>
                <div class="via-info-field-value">
                  <!-- 字段插槽 -->
                  <slot 
                    v-if="hasFieldSlot('info', fieldInfo.key)"
                    :name="formatSlotName('info', fieldInfo.key)"
                    :field="fieldInfo.config"
                    :value="fieldInfo.value"
                    :displayValue="fieldInfo.displayValue"
                    :data="infoData"
                  />
                  
                  <!-- 默认显示 -->
                  <span v-else>{{ fieldInfo.displayValue }}</span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>
      
      <!-- 自定义内容插槽 -->
      <slot name="info-content" :data="infoData" />
    </div>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useInfo, useSlots, useGridContext } from '../composables'

// Props
interface ViaInfoProps {
  visible?: boolean
  data?: any
  title?: string
  width?: string | number
  modalType?: 'modal' | 'drawer'
  groups?: Array<{
    title: string
    fields: string[]
  }>
  columns?: number
}

const props = withDefaults(defineProps<ViaInfoProps>(), {
  visible: false,
  data: () => ({}),
  title: '详情',
  width: '800px',
  modalType: 'modal',
  groups: () => [],
  columns: 2
})

// Emits
interface ViaInfoEmits {
  'update:visible': [visible: boolean]
  close: []
}

const emit = defineEmits<ViaInfoEmits>()

// 获取上下文
const context = useGridContext()

// 使用详情逻辑
const info = useInfo({
  mode: props.modalType,
  groups: props.groups,
  columns: props.columns
})

// 使用插槽逻辑
const { hasFieldSlot, formatSlotName } = useSlots()

// 解构详情状态和方法
const {
  infoData,
  infoVisible,
  getFieldsByGroup
} = info

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const modalComponent = computed(() => {
  return props.modalType === 'drawer' ? 'vxe-drawer' : 'vxe-modal'
})

const modalTitle = computed(() => props.title)

const modalConfig = computed(() => {
  const baseConfig = {
    width: props.width,
    destroyOnClose: true,
    showConfirmButton: false,
    showCancelButton: false
  }
  
  if (props.modalType === 'drawer') {
    return {
      ...baseConfig,
      ...context.config.info?.drawerConfig
    }
  } else {
    return {
      ...baseConfig,
      ...context.config.info?.modalConfig
    }
  }
})

const fieldGroups = computed(() => {
  return getFieldsByGroup()
})

// 事件处理
const handleClose = () => {
  info.close()
  emit('close')
}

// 暴露方法
defineExpose({
  getFieldValue: info.getFieldValue,
  getDisplayValue: info.getDisplayValue
})
</script>

<style scoped>
.via-info {
  @apply space-y-6;
}

.via-info-group {
  @apply space-y-4;
}

.via-info-group-title {
  @apply text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2;
}

.via-info-group-content {
  @apply grid grid-cols-2 gap-4;
}

.via-info-field {
  @apply flex flex-col space-y-1;
}

.via-info-field-label {
  @apply text-sm font-medium text-gray-600;
}

.via-info-field-value {
  @apply text-sm text-gray-900;
}
</style>
