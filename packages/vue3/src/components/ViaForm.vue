<template>
  <component
    :is="modalComponent"
    v-model="visible"
    :title="modalTitle"
    v-bind="modalConfig"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <vxe-form
      ref="formRef"
      v-model="formData"
      :data="formData"
      :loading="loading"
      :items="formItems"
      v-bind="formConfig"
    >
      <!-- 动态插槽渲染 -->
      <template v-for="slotName in dynamicSlots" :key="slotName" #[slotName]="slotProps">
        <slot
          :name="slotName"
          v-bind="slotProps"
          :mode="mode"
          :onChange="(value: any) => setFieldValue(getFieldNameFromSlot(slotName), value)"
        />
      </template>

      <!-- 操作按钮插槽 -->
      <template #action>
        <slot name="form-action">
          <!-- 默认操作按钮由模态框处理 -->
        </slot>
      </template>
    </vxe-form>
  </component>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useForm, useSlots, useAdapter, useGridContext } from '../composables'
import type { FieldSchema } from '@via-grid/core'

// Props
interface ViaFormProps {
  visible?: boolean
  mode?: 'add' | 'edit'
  data?: any
  loading?: boolean
  title?: string
  width?: string | number
  modalType?: 'modal' | 'drawer'
}

const props = withDefaults(defineProps<ViaFormProps>(), {
  visible: false,
  mode: 'add',
  data: () => ({}),
  loading: false,
  title: '',
  width: '800px',
  modalType: 'modal'
})

// Emits
interface ViaFormEmits {
  'update:visible': [visible: boolean]
  submit: []
  cancel: []
  'field-change': [field: string, value: any]
}

const emit = defineEmits<ViaFormEmits>()

// 获取上下文
const context = useGridContext()

// 使用表单逻辑
const form = useForm({
  mode: props.mode,
  initialData: props.data,
  autoValidate: true
})

// 使用插槽逻辑
const { hasFieldSlot, formatSlotName } = useSlots()

// 使用适配器
const { adapter } = useAdapter()

// 表单引用
const formRef = ref()

// 解构表单状态和方法
const {
  formData,
  formFields,
  formVisible,
  formMode,
  loading,
  errors,
  setFieldValue,
  validate,
  isFieldVisible,
  isFieldRequired,
  getFieldConfig
} = form

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const modalComponent = computed(() => {
  return props.modalType === 'drawer' ? 'vxe-drawer' : 'vxe-modal'
})

const modalTitle = computed(() => {
  if (props.title) return props.title
  return props.mode === 'add' ? '新增' : '编辑'
})

const modalConfig = computed(() => {
  const baseConfig = {
    width: props.width,
    destroyOnClose: true,
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }
  
  if (props.modalType === 'drawer') {
    return {
      ...baseConfig,
      ...context.config.form?.drawerConfig
    }
  } else {
    return {
      ...baseConfig,
      ...context.config.form?.modalConfig
    }
  }
})

const formConfig = computed(() => {
  return {
    titleAlign: 'right',
    titleWidth: '100px',
    ...context.config.form?.formConfig
  }
})

// 构建 vxe-form 的 items 配置
const formItems = computed(() => {
  const items: any[] = []

  for (const [fieldName, field] of Object.entries(formFields.value)) {
    if (!isFieldVisible(fieldName)) continue

    const item: any = {
      field: fieldName,
      title: field.label,
      span: field.form?.span || 12,
      required: isFieldRequired(fieldName),
      ...field.form?.itemProps
    }

    // 检查是否有自定义插槽
    if (hasFieldSlot('form', fieldName)) {
      item.slots = { default: formatSlotName('form', fieldName) }
    } else {
      // 使用适配器渲染器
      const renderConfig = getFieldRenderConfig(field)
      if (renderConfig) {
        item.itemRender = renderConfig
      }
    }

    items.push(item)
  }

  // 添加操作按钮项
  items.push({
    align: 'center',
    span: 24,
    slots: { default: 'action' }
  })

  return items
})

// 获取字段渲染配置
const getFieldRenderConfig = (field: FieldSchema) => {
  if (!adapter.value) return null

  const renderConfig = adapter.value.renderField(field, 'form', formData.value[field.prop], {
    onChange: (value: any) => setFieldValue(field.prop, value)
  })

  if (!renderConfig) return null

  return {
    name: renderConfig.name,
    props: {
      ...renderConfig.props,
      placeholder: field.placeholder || `请输入${field.label}`,
      ...field.form?.props
    },
    events: {
      change: ({ value }: any) => handleFieldChange(field.prop, value),
      ...renderConfig.events
    }
  }
}

// 动态插槽列表
const dynamicSlots = computed(() => {
  const slots: string[] = []

  for (const [fieldName, field] of Object.entries(formFields.value)) {
    if (isFieldVisible(fieldName) && hasFieldSlot('form', fieldName)) {
      slots.push(formatSlotName('form', fieldName))
    }
  }

  return slots
})

// 从插槽名获取字段名
const getFieldNameFromSlot = (slotName: string) => {
  // 假设插槽名格式为 'form-fieldName'
  return slotName.replace('form-', '')
}

// 事件处理
const handleSubmit = async () => {
  try {
    await form.submit()
    emit('submit')
  } catch (error) {
    console.error('Form submit failed:', error)
  }
}

const handleCancel = () => {
  form.close()
  emit('cancel')
}

const handleFieldChange = (field: string, value: any) => {
  setFieldValue(field, value)
  emit('field-change', field, value)
}

// 暴露方法
defineExpose({
  validate,
  reset: form.reset,
  getFieldValue: form.getFieldValue,
  setFieldValue: form.setFieldValue
})
</script>

<style scoped>
.via-form-error {
  @apply text-red-500 text-sm mt-1;
}
</style>