<template>
  <div class="via-search">
    <vxe-form
      ref="formRef"
      v-model="searchParams"
      :data="searchParams"
      :loading="loading"
      v-bind="formConfig"
    >
      <!-- 动态渲染搜索字段 -->
      <template v-for="(field, fieldName) in visibleFields" :key="fieldName">
        <vxe-form-item
          :field="fieldName"
          :title="field.label"
          v-bind="getFieldItemProps(field)"
        >
          <!-- 字段插槽 -->
          <template v-if="hasFieldSlot('search', fieldName)" #default="{ data }">
            <slot 
              :name="formatSlotName('search', fieldName)"
              :field="field"
              :value="data[fieldName]"
              :data="data"
              :onChange="(value: any) => setFieldValue(fieldName, value)"
            />
          </template>
          
          <!-- 默认字段渲染 -->
          <component
            v-else
            :is="getFieldComponent(field)"
            v-model="searchParams[fieldName]"
            v-bind="getFieldProps(field)"
            @change="handleFieldChange(fieldName, $event)"
          />
          
          <!-- 字段标题插槽 -->
          <template v-if="hasFieldSlot('search', fieldName, 'title')" #title>
            <slot 
              :name="formatSlotName('search', fieldName, 'title')"
              :field="field"
              :title="field.label"
            />
          </template>
          
          <!-- 字段前缀插槽 -->
          <template v-if="hasFieldSlot('search', fieldName, 'prefix')" #prefix>
            <slot 
              :name="formatSlotName('search', fieldName, 'prefix')"
              :field="field"
            />
          </template>
          
          <!-- 字段后缀插槽 -->
          <template v-if="hasFieldSlot('search', fieldName, 'suffix')" #suffix>
            <slot 
              :name="formatSlotName('search', fieldName, 'suffix')"
              :field="field"
            />
          </template>
        </vxe-form-item>
      </template>
      
      <!-- 操作按钮 -->
      <vxe-form-item>
        <template #default>
          <div class="via-search-actions">
            <vxe-button 
              type="primary" 
              :loading="loading"
              @click="handleSearch"
            >
              搜索
            </vxe-button>
            <vxe-button 
              :loading="loading"
              @click="handleReset"
            >
              重置
            </vxe-button>
            
            <!-- 操作按钮后置插槽 -->
            <slot name="search-actions-after" />
          </div>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useSearch, useSlots, useAdapter, useGridContext } from '../composables'
import type { FieldSchema } from '@via-grid/core'

// Props
interface ViaSearchProps {
  loading?: boolean
  autoSearch?: boolean
  autoSearchDelay?: number
  collapsed?: boolean
  collapsible?: boolean
}

const props = withDefaults(defineProps<ViaSearchProps>(), {
  loading: false,
  autoSearch: false,
  autoSearchDelay: 300,
  collapsed: false,
  collapsible: true
})

// Emits
interface ViaSearchEmits {
  search: []
  reset: []
  'field-change': [field: string, value: any]
}

const emit = defineEmits<ViaSearchEmits>()

// 获取上下文
const context = useGridContext()

// 使用搜索逻辑
const search = useSearch({
  autoSearch: props.autoSearch,
  autoSearchDelay: props.autoSearchDelay
})

// 使用插槽逻辑
const { hasFieldSlot, formatSlotName } = useSlots()

// 使用适配器
const { adapter } = useAdapter()

// 表单引用
const formRef = ref()

// 解构搜索状态和方法
const {
  searchParams,
  searchFields,
  setFieldValue,
  isFieldVisible,
  getFieldConfig
} = search

// 计算属性
const visibleFields = computed(() => {
  const fields: Record<string, FieldSchema> = {}
  
  for (const [fieldName, field] of Object.entries(searchFields.value)) {
    if (isFieldVisible(fieldName)) {
      fields[fieldName] = field
    }
  }
  
  return fields
})

const formConfig = computed(() => {
  return {
    titleAlign: 'right',
    titleWidth: '100px',
    ...context.config.search?.formConfig
  }
})

// 获取字段组件
const getFieldComponent = (field: FieldSchema) => {
  if (!adapter.value) return 'input'
  
  const renderConfig = adapter.value.renderField(field, 'search', searchParams.value[field.prop], {
    onChange: (value: any) => setFieldValue(field.prop, value)
  })
  
  return renderConfig?.name || 'input'
}

// 获取字段属性
const getFieldProps = (field: FieldSchema) => {
  if (!adapter.value) return {}
  
  const renderConfig = adapter.value.renderField(field, 'search', searchParams.value[field.prop], {
    onChange: (value: any) => setFieldValue(field.prop, value)
  })
  
  return {
    ...renderConfig?.props,
    placeholder: field.placeholder || `请输入${field.label}`,
    clearable: true,
    ...field.search?.props
  }
}

// 获取字段项属性
const getFieldItemProps = (field: FieldSchema) => {
  return {
    span: field.search?.span || 6,
    ...field.search?.itemProps
  }
}

// 事件处理
const handleSearch = () => {
  search.search()
  emit('search')
}

const handleReset = () => {
  search.reset()
  emit('reset')
}

const handleFieldChange = (field: string, value: any) => {
  setFieldValue(field, value)
  emit('field-change', field, value)
}

// 暴露方法
defineExpose({
  search: handleSearch,
  reset: handleReset,
  validate: () => formRef.value?.validate(),
  clearValidate: () => formRef.value?.clearValidate(),
  getFieldValue: search.getFieldValue,
  setFieldValue: search.setFieldValue
})
</script>

<style scoped>
.via-search {
  @apply w-full;
}

.via-search-actions {
  @apply flex items-center gap-2;
}
</style>
