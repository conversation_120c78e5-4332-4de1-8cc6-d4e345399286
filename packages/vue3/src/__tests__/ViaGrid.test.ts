/**
 * ViaGrid 组件测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import ViaGrid from '../components/ViaGrid.vue'
import type { ViaGridConfig } from '@via-grid/core'

// Mock vxe-table 组件
const mockVxeComponents = {
  'vxe-form': {
    template: '<div class="mock-vxe-form"><slot /></div>',
    props: ['data', 'loading']
  },
  'vxe-form-item': {
    template: '<div class="mock-vxe-form-item"><slot /></div>',
    props: ['field', 'title']
  },
  'vxe-table': {
    template: '<div class="mock-vxe-table"><slot /></div>',
    props: ['data', 'loading']
  },
  'vxe-column': {
    template: '<div class="mock-vxe-column"><slot /></div>',
    props: ['field', 'title', 'width']
  },
  'vxe-button': {
    template: '<button class="mock-vxe-button"><slot /></button>',
    props: ['type', 'loading', 'disabled']
  },
  'vxe-pager': {
    template: '<div class="mock-vxe-pager"><slot /></div>',
    props: ['currentPage', 'pageSize', 'total']
  },
  'vxe-modal': {
    template: '<div class="mock-vxe-modal" v-if="modelValue"><slot /></div>',
    props: ['modelValue', 'title', 'width']
  },
  'vxe-drawer': {
    template: '<div class="mock-vxe-drawer" v-if="modelValue"><slot /></div>',
    props: ['modelValue', 'title', 'width']
  }
}

describe('ViaGrid', () => {
  let config: ViaGridConfig

  beforeEach(() => {
    config = {
      schema: {
        name: {
          type: 'text',
          label: '姓名',
          prop: 'name'
        },
        age: {
          type: 'number',
          label: '年龄',
          prop: 'age'
        },
        email: {
          type: 'text',
          label: '邮箱',
          prop: 'email'
        }
      },
      api: {
        search: async (params) => {
          return {
            data: [
              { id: 1, name: '张三', age: 25, email: '<EMAIL>' },
              { id: 2, name: '李四', age: 30, email: '<EMAIL>' }
            ],
            total: 2
          }
        }
      }
    }
  })

  it('应该正确渲染基本结构', () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      global: {
        components: mockVxeComponents
      }
    })

    expect(wrapper.find('.via-grid').exists()).toBe(true)
    expect(wrapper.find('.via-grid-search').exists()).toBe(true)
    expect(wrapper.find('.via-grid-toolbar').exists()).toBe(true)
    expect(wrapper.find('.via-grid-table').exists()).toBe(true)
    expect(wrapper.find('.via-grid-pager').exists()).toBe(true)
  })

  it('应该支持插槽', () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      slots: {
        'search-to-toolbar': '<div class="custom-search-to-toolbar">自定义内容</div>',
        'toolbar-to-table': '<div class="custom-toolbar-to-table">自定义内容</div>',
        'table-to-pager': '<div class="custom-table-to-pager">自定义内容</div>'
      },
      global: {
        components: mockVxeComponents
      }
    })

    expect(wrapper.find('.custom-search-to-toolbar').exists()).toBe(true)
    expect(wrapper.find('.custom-toolbar-to-table').exists()).toBe(true)
    expect(wrapper.find('.custom-table-to-pager').exists()).toBe(true)
  })

  it('应该正确处理配置', () => {
    const configWithoutSearch = {
      ...config,
      schema: {}
    }

    const wrapper = mount(ViaGrid, {
      props: { config: configWithoutSearch },
      global: {
        components: mockVxeComponents
      }
    })

    // 没有搜索字段时不应该显示搜索区域
    expect(wrapper.find('.via-grid-search').exists()).toBe(false)
  })

  it('应该支持高度设置', () => {
    const wrapper = mount(ViaGrid, {
      props: { 
        config,
        height: 500,
        maxHeight: 800
      },
      global: {
        components: mockVxeComponents
      }
    })

    const gridElement = wrapper.find('.via-grid')
    expect(gridElement.attributes('style')).toContain('height: 500px')
    expect(gridElement.attributes('style')).toContain('max-height: 800px')
  })

  it('应该暴露正确的方法', () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      global: {
        components: mockVxeComponents
      }
    })

    const vm = wrapper.vm as any

    // 检查暴露的方法
    expect(typeof vm.search).toBe('function')
    expect(typeof vm.reset).toBe('function')
    expect(typeof vm.refresh).toBe('function')
    expect(typeof vm.openForm).toBe('function')
    expect(typeof vm.closeForm).toBe('function')
    expect(typeof vm.openInfo).toBe('function')
    expect(typeof vm.closeInfo).toBe('function')
    expect(typeof vm.clearError).toBe('function')
    expect(typeof vm.setError).toBe('function')
  })

  it('应该正确处理错误状态', async () => {
    const configWithError = {
      ...config,
      api: {
        search: async () => {
          throw new Error('Network error')
        }
      }
    }

    const wrapper = mount(ViaGrid, {
      props: { config: configWithError },
      global: {
        components: mockVxeComponents
      }
    })

    const vm = wrapper.vm as any

    // 触发搜索错误
    await vm.search()

    // 检查错误状态
    expect(vm.hasError.value).toBe(true)
    expect(vm.errorMessage.value).toContain('Network error')
  })

  it('应该支持国际化', () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      global: {
        components: mockVxeComponents
      }
    })

    const vm = wrapper.vm as any
    const context = vm.context

    // 检查国际化实例
    expect(context.i18n).toBeDefined()
    expect(typeof context.i18n.t).toBe('function')

    // 测试翻译
    expect(context.i18n.t('common.search')).toBe('搜索')
  })

  it('应该支持加载状态管理', async () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      global: {
        components: mockVxeComponents
      }
    })

    const vm = wrapper.vm as any
    const context = vm.context

    // 检查加载管理器
    expect(context.loadingManager).toBeDefined()
    expect(typeof context.loadingManager.startLoading).toBe('function')
    expect(typeof context.loadingManager.endLoading).toBe('function')
  })

  it('应该支持数据验证', async () => {
    const wrapper = mount(ViaGrid, {
      props: { config },
      global: {
        components: mockVxeComponents
      }
    })

    const vm = wrapper.vm as any
    const context = vm.context

    // 检查验证实例
    expect(context.validation).toBeDefined()
    expect(typeof context.validation.validate).toBe('function')
    expect(typeof context.validation.validateField).toBe('function')
  })
})
