/**
 * useError Composable 测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useError } from '../../composables/useError'

describe('useError', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确初始化', () => {
    const error = useError()
    
    expect(error.error.value).toBeNull()
    expect(error.hasError.value).toBe(false)
    expect(error.errorMessage.value).toBe('')
  })

  it('应该正确设置字符串错误', () => {
    const error = useError()
    
    error.setError('Test error message')
    
    expect(error.hasError.value).toBe(true)
    expect(error.errorMessage.value).toBe('Test error message')
    expect(error.error.value?.type).toBe('unknown')
  })

  it('应该正确设置Error对象', () => {
    const error = useError()
    const testError = new Error('Network error')
    
    error.setError(testError)
    
    expect(error.hasError.value).toBe(true)
    expect(error.errorMessage.value).toBe('Network error')
    expect(error.error.value?.type).toBe('network')
  })

  it('应该正确设置ErrorInfo对象', () => {
    const error = useError()
    const errorInfo = {
      type: 'validation' as const,
      message: 'Validation failed',
      code: 'INVALID_DATA',
      timestamp: Date.now()
    }
    
    error.setError(errorInfo)
    
    expect(error.hasError.value).toBe(true)
    expect(error.errorMessage.value).toBe('Validation failed')
    expect(error.error.value?.type).toBe('validation')
    expect(error.error.value?.code).toBe('INVALID_DATA')
  })

  it('应该正确清除错误', () => {
    const error = useError()
    
    error.setError('Test error')
    expect(error.hasError.value).toBe(true)
    
    error.clearError()
    expect(error.hasError.value).toBe(false)
    expect(error.error.value).toBeNull()
  })

  it('应该正确处理异步错误', async () => {
    const error = useError()
    
    const successPromise = Promise.resolve('success')
    const result = await error.handleAsyncError(successPromise)
    
    expect(result).toBe('success')
    expect(error.hasError.value).toBe(false)
  })

  it('应该正确处理异步错误失败', async () => {
    const error = useError()
    
    const failPromise = Promise.reject(new Error('Async error'))
    const result = await error.handleAsyncError(failPromise)
    
    expect(result).toBeNull()
    expect(error.hasError.value).toBe(true)
    expect(error.errorMessage.value).toBe('Async error')
  })

  it('应该正确判断错误类型', () => {
    const error = useError()
    
    error.setError('Network error', 'network')
    expect(error.isNetworkError()).toBe(true)
    expect(error.isValidationError()).toBe(false)
    
    error.setError('Validation error', 'validation')
    expect(error.isValidationError()).toBe(true)
    expect(error.isNetworkError()).toBe(false)
    
    error.setError('Permission error', 'permission')
    expect(error.isPermissionError()).toBe(true)
    
    error.setError('Business error', 'business')
    expect(error.isBusinessError()).toBe(true)
  })

  it('应该调用错误处理器', () => {
    const onError = vi.fn()
    const error = useError({ onError })
    
    error.setError('Test error')
    
    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Test error',
        type: 'unknown'
      })
    )
  })

  it('应该支持自动清除', async () => {
    vi.useFakeTimers()
    
    const error = useError({
      autoClear: true,
      clearDelay: 1000
    })
    
    error.setError('Test error')
    expect(error.hasError.value).toBe(true)
    
    // 快进时间
    vi.advanceTimersByTime(1000)
    
    expect(error.hasError.value).toBe(false)
    
    vi.useRealTimers()
  })
})
