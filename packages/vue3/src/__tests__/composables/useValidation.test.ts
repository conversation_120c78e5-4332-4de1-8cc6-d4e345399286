/**
 * useValidation Composable 测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { useValidation, commonRules } from '../../composables/useValidation'

describe('useValidation', () => {
  beforeEach(() => {
    // 清理状态
  })

  it('应该正确初始化', () => {
    const validation = useValidation()
    
    expect(validation.errors.value).toEqual({})
    expect(validation.isValid.value).toBe(true)
    expect(validation.isValidating.value).toBe(false)
  })

  it('应该正确验证必填字段', async () => {
    const validation = useValidation({
      rules: {
        name: [{ required: true, message: '姓名是必填项' }]
      }
    })
    
    // 验证空值
    const result1 = await validation.validateField('name', '')
    expect(result1).toBe(false)
    expect(validation.hasError('name')).toBe(true)
    expect(validation.getFieldError('name')).toBe('姓名是必填项')
    
    // 验证有值
    const result2 = await validation.validateField('name', '张三')
    expect(result2).toBe(true)
    expect(validation.hasError('name')).toBe(false)
  })

  it('应该正确验证长度', async () => {
    const validation = useValidation({
      rules: {
        password: [
          { minLength: 6, message: '密码最少6位' },
          { maxLength: 20, message: '密码最多20位' }
        ]
      }
    })
    
    // 验证最小长度
    const result1 = await validation.validateField('password', '123')
    expect(result1).toBe(false)
    expect(validation.getFieldError('password')).toBe('密码最少6位')
    
    // 验证最大长度
    const result2 = await validation.validateField('password', '123456789012345678901')
    expect(result2).toBe(false)
    expect(validation.getFieldError('password')).toBe('密码最多20位')
    
    // 验证正确长度
    const result3 = await validation.validateField('password', '123456')
    expect(result3).toBe(true)
  })

  it('应该正确验证数值范围', async () => {
    const validation = useValidation({
      rules: {
        age: [
          { min: 18, message: '年龄不能小于18' },
          { max: 65, message: '年龄不能大于65' }
        ]
      }
    })
    
    // 验证最小值
    const result1 = await validation.validateField('age', 16)
    expect(result1).toBe(false)
    expect(validation.getFieldError('age')).toBe('年龄不能小于18')
    
    // 验证最大值
    const result2 = await validation.validateField('age', 70)
    expect(result2).toBe(false)
    expect(validation.getFieldError('age')).toBe('年龄不能大于65')
    
    // 验证正确值
    const result3 = await validation.validateField('age', 25)
    expect(result3).toBe(true)
  })

  it('应该正确验证正则表达式', async () => {
    const validation = useValidation({
      rules: {
        email: [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }]
      }
    })
    
    // 验证错误格式
    const result1 = await validation.validateField('email', 'invalid-email')
    expect(result1).toBe(false)
    expect(validation.getFieldError('email')).toBe('邮箱格式不正确')
    
    // 验证正确格式
    const result2 = await validation.validateField('email', '<EMAIL>')
    expect(result2).toBe(true)
  })

  it('应该正确处理自定义验证器', async () => {
    const validation = useValidation({
      rules: {
        username: [{
          validator: (value) => {
            if (value === 'admin') {
              return '用户名不能是admin'
            }
            return true
          }
        }]
      }
    })
    
    // 验证失败
    const result1 = await validation.validateField('username', 'admin')
    expect(result1).toBe(false)
    expect(validation.getFieldError('username')).toBe('用户名不能是admin')
    
    // 验证成功
    const result2 = await validation.validateField('username', 'user')
    expect(result2).toBe(true)
  })

  it('应该正确验证整个表单', async () => {
    const validation = useValidation({
      rules: {
        name: [{ required: true }],
        email: [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }],
        age: [{ min: 18, max: 65 }]
      }
    })
    
    // 验证失败的数据
    const invalidData = {
      name: '',
      email: 'invalid-email',
      age: 16
    }
    
    const result1 = await validation.validate(invalidData)
    expect(result1).toBe(false)
    expect(validation.getErrorCount()).toBe(3)
    
    // 验证成功的数据
    const validData = {
      name: '张三',
      email: '<EMAIL>',
      age: 25
    }
    
    const result2 = await validation.validate(validData)
    expect(result2).toBe(true)
    expect(validation.getErrorCount()).toBe(0)
  })

  it('应该正确清除错误', () => {
    const validation = useValidation()
    
    validation.setFieldError('name', '错误信息')
    validation.setFieldError('email', '邮箱错误')
    
    expect(validation.getErrorCount()).toBe(2)
    
    // 清除单个字段错误
    validation.clearFieldError('name')
    expect(validation.hasError('name')).toBe(false)
    expect(validation.hasError('email')).toBe(true)
    
    // 清除所有错误
    validation.clearErrors()
    expect(validation.getErrorCount()).toBe(0)
  })

  it('应该正确使用常用验证规则', async () => {
    const validation = useValidation({
      rules: {
        email: [commonRules.email],
        phone: [commonRules.phone],
        idCard: [commonRules.idCard]
      }
    })
    
    // 测试邮箱验证
    const emailResult = await validation.validateField('email', 'invalid-email')
    expect(emailResult).toBe(false)
    expect(validation.getFieldError('email')).toBe('请输入有效的邮箱地址')
    
    // 测试手机号验证
    const phoneResult = await validation.validateField('phone', '123456')
    expect(phoneResult).toBe(false)
    expect(validation.getFieldError('phone')).toBe('请输入有效的手机号码')
  })
})
