/**
 * Via Grid Vue3 版本类型定义
 */

import type { App } from 'vue'
import type { ViaGridConfig } from '@via-grid/core'

// 导出核心类型
export * from '@via-grid/core'
export * from '@via-grid/shared'

/**
 * Vue3 特定的组件 Props 类型
 */
export interface ViaGridProps {
  config: ViaGridConfig
  modelValue?: any[]
  loading?: boolean
  height?: string | number
  maxHeight?: string | number
}

/**
 * Vue3 特定的组件 Emits 类型
 */
export interface ViaGridEmits {
  'update:modelValue': [value: any[]]
  'search': [params: Record<string, any>]
  'reset': []
  'add': []
  'edit': [row: any]
  'delete': [row: any]
  'view': [row: any]
  'refresh': []
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
}

/**
 * 插件安装选项
 */
export interface ViaGridInstallOptions {
  // 全局配置
  config?: Partial<ViaGridConfig>
  // 组件前缀
  prefix?: string
}

/**
 * Vue 插件类型
 */
export interface ViaGridPlugin {
  install(app: App, options?: ViaGridInstallOptions): void
}

/**
 * 组件实例类型
 */
export interface ViaGridInstance {
  // 搜索相关
  search(): void
  reset(): void
  getSearchParams(): Record<string, any>
  setSearchParams(params: Record<string, any>): void
  
  // 表格相关
  refresh(): void
  getTableData(): any[]
  setTableData(data: any[]): void
  
  // 分页相关
  getCurrentPage(): number
  getPageSize(): number
  setPage(page: number): void
  setPageSize(pageSize: number): void
  
  // 表单相关
  openForm(mode: 'add' | 'edit', row?: any): void
  closeForm(): void
  submitForm(): Promise<void>

  // 详情相关
  openInfo(row: any): void
  closeInfo(): void

  // 错误处理
  setError(error: string | Error, type?: string): void
  clearError(): void

  // 加载状态
  startLoading(name?: string): string
  endLoading(taskId: string): Promise<void>
}
