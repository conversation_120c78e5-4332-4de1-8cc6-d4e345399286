import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    dts({
      insertTypesEntry: true,
      rollupTypes: true
    })
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'ViaGridVue3',
      formats: ['es', 'cjs'],
      fileName: (format) => `index.${format === 'es' ? 'js' : 'cjs'}`
    },
    rollupOptions: {
      external: [
        'vue',
        'vxe-table',
        '@via-grid/core',
        '@via-grid/shared',
        '@vxe-ui/plugin-render-element',
        '@vxe-ui/plugin-render-antd',
        '@vxe-ui/plugin-render-naive',
        'element-plus',
        'ant-design-vue',
        'naive-ui'
      ],
      output: {
        globals: {
          'vue': 'Vue',
          'vxe-table': 'VXETable',
          '@via-grid/core': 'ViaGridCore',
          '@via-grid/shared': 'ViaGridShared'
        }
      }
    }
  }
})
