<template>
  <div class="example-container">
    <h1>Via Grid Vue3 基础使用示例</h1>
    
    <!-- 基础用法 -->
    <section class="example-section">
      <h2>基础用法</h2>
      <ViaGrid :config="basicConfig" />
    </section>
    
    <!-- 自定义插槽 -->
    <section class="example-section">
      <h2>自定义插槽</h2>
      <ViaGrid :config="slotConfig">
        <!-- 搜索字段插槽 -->
        <template #search-status="{ value, onChange }">
          <select :value="value" @change="onChange($event.target.value)">
            <option value="">全部</option>
            <option value="active">激活</option>
            <option value="inactive">禁用</option>
          </select>
        </template>
        
        <!-- 表格字段插槽 -->
        <template #table-status="{ value }">
          <span 
            :class="value === 'active' ? 'status-active' : 'status-inactive'"
          >
            {{ value === 'active' ? '激活' : '禁用' }}
          </span>
        </template>
        
        <!-- 表格操作列插槽 -->
        <template #table-actions-after="{ row }">
          <button @click="handleCustomAction(row)">
            自定义操作
          </button>
        </template>
        
        <!-- 工具栏插槽 -->
        <template #toolbar-right>
          <button @click="handleExport">
            导出数据
          </button>
        </template>
      </ViaGrid>
    </section>
    
    <!-- 表单验证 -->
    <section class="example-section">
      <h2>表单验证</h2>
      <ViaGrid :config="validationConfig" />
    </section>
    
    <!-- 错误处理 -->
    <section class="example-section">
      <h2>错误处理</h2>
      <ViaGrid :config="errorConfig" />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ViaGrid, type ViaGridConfig } from '@via-grid/vue3'
import { commonRules } from '@via-grid/vue3'

// 模拟API
const mockApi = {
  search: async (params: any) => {
    console.log('Search params:', params)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return {
      data: [
        { id: 1, name: '张三', email: '<EMAIL>', status: 'active', age: 25 },
        { id: 2, name: '李四', email: '<EMAIL>', status: 'inactive', age: 30 },
        { id: 3, name: '王五', email: '<EMAIL>', status: 'active', age: 28 }
      ],
      total: 3
    }
  },
  
  create: async (data: any) => {
    console.log('Create data:', data)
    await new Promise(resolve => setTimeout(resolve, 500))
    return { success: true }
  },
  
  update: async (data: any) => {
    console.log('Update data:', data)
    await new Promise(resolve => setTimeout(resolve, 500))
    return { success: true }
  },
  
  delete: async (data: any) => {
    console.log('Delete data:', data)
    await new Promise(resolve => setTimeout(resolve, 500))
    return { success: true }
  }
}

// 基础配置
const basicConfig = ref<ViaGridConfig>({
  schema: {
    name: {
      type: 'text',
      label: '姓名',
      prop: 'name'
    },
    email: {
      type: 'text',
      label: '邮箱',
      prop: 'email'
    },
    age: {
      type: 'number',
      label: '年龄',
      prop: 'age'
    }
  },
  api: mockApi
})

// 插槽配置
const slotConfig = ref<ViaGridConfig>({
  schema: {
    name: {
      type: 'text',
      label: '姓名',
      prop: 'name'
    },
    email: {
      type: 'text',
      label: '邮箱',
      prop: 'email'
    },
    status: {
      type: 'select',
      label: '状态',
      prop: 'status',
      dict: [
        { label: '激活', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ]
    }
  },
  api: mockApi
})

// 验证配置
const validationConfig = ref<ViaGridConfig>({
  schema: {
    name: {
      type: 'text',
      label: '姓名',
      prop: 'name',
      form: {
        rules: [
          { required: true, message: '姓名是必填项' },
          { minLength: 2, message: '姓名至少2个字符' }
        ]
      }
    },
    email: {
      type: 'text',
      label: '邮箱',
      prop: 'email',
      form: {
        rules: [
          { required: true, message: '邮箱是必填项' },
          commonRules.email
        ]
      }
    },
    phone: {
      type: 'text',
      label: '手机号',
      prop: 'phone',
      form: {
        rules: [
          { required: true, message: '手机号是必填项' },
          commonRules.phone
        ]
      }
    }
  },
  api: mockApi
})

// 错误处理配置
const errorConfig = ref<ViaGridConfig>({
  schema: {
    name: {
      type: 'text',
      label: '姓名',
      prop: 'name'
    }
  },
  api: {
    search: async () => {
      throw new Error('模拟网络错误')
    }
  }
})

// 事件处理
const handleCustomAction = (row: any) => {
  console.log('Custom action for:', row)
  alert(`自定义操作: ${row.name}`)
}

const handleExport = () => {
  console.log('Export data')
  alert('导出功能')
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.example-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.status-active {
  color: #52c41a;
  font-weight: bold;
}

.status-inactive {
  color: #ff4d4f;
  font-weight: bold;
}

button {
  padding: 4px 8px;
  margin: 0 2px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
}

button:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
}
</style>
