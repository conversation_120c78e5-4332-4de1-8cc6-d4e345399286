<template>
  <div class="advanced-example">
    <h1>Via Grid Vue3 高级功能示例</h1>
    
    <!-- 使用 Composables -->
    <section class="example-section">
      <h2>使用 Composables</h2>
      <div class="composable-demo">
        <div class="controls">
          <button @click="handleSearch">搜索</button>
          <button @click="handleReset">重置</button>
          <button @click="handleAdd">新增</button>
          <button @click="handleRefresh">刷新</button>
          <button @click="triggerError">触发错误</button>
          <button @click="clearError">清除错误</button>
        </div>
        
        <div class="status">
          <p>加载状态: {{ loading ? '加载中' : '空闲' }}</p>
          <p>错误状态: {{ hasError ? errorMessage : '无错误' }}</p>
          <p>数据条数: {{ tableData.length }}</p>
          <p>当前页: {{ currentPage }} / 每页: {{ pageSize }}</p>
        </div>
        
        <ViaGrid :config="composableConfig" />
      </div>
    </section>
    
    <!-- 国际化 -->
    <section class="example-section">
      <h2>国际化支持</h2>
      <div class="i18n-demo">
        <div class="language-switcher">
          <label>选择语言:</label>
          <select v-model="currentLocale" @change="handleLocaleChange">
            <option value="zh-CN">中文</option>
            <option value="en-US">English</option>
            <option value="ja-JP">日本語</option>
            <option value="ko-KR">한국어</option>
          </select>
        </div>
        
        <ViaGrid :config="i18nConfig" />
      </div>
    </section>
    
    <!-- 性能优化 -->
    <section class="example-section">
      <h2>性能优化 - 虚拟滚动</h2>
      <div class="performance-demo">
        <p>大数据量表格 ({{ largeDataset.length }} 条记录)</p>
        <ViaGrid :config="performanceConfig" />
      </div>
    </section>
    
    <!-- 表单验证 -->
    <section class="example-section">
      <h2>高级表单验证</h2>
      <div class="validation-demo">
        <ViaGrid :config="advancedValidationConfig" />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ViaGrid, 
  useGrid, 
  useI18n, 
  useValidation,
  commonRules,
  type ViaGridConfig,
  type SupportedLocale 
} from '@via-grid/vue3'

// 使用 Composables 示例
const grid = useGrid({
  config: {
    schema: {
      name: { type: 'text', label: '姓名', prop: 'name' },
      email: { type: 'text', label: '邮箱', prop: 'email' },
      status: { type: 'select', label: '状态', prop: 'status' }
    },
    api: {
      search: async (params) => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        return {
          data: [
            { id: 1, name: '张三', email: '<EMAIL>', status: 'active' },
            { id: 2, name: '李四', email: '<EMAIL>', status: 'inactive' }
          ],
          total: 2
        }
      }
    }
  }
})

const {
  loading,
  tableData,
  currentPage,
  pageSize,
  hasError,
  errorMessage,
  search,
  reset,
  refresh,
  openForm,
  setError,
  clearError
} = grid

// 国际化示例
const i18n = useI18n()
const currentLocale = ref<SupportedLocale>('zh-CN')

const handleLocaleChange = () => {
  i18n.setLocale(currentLocale.value)
}

// 大数据集
const largeDataset = ref(
  Array.from({ length: 10000 }, (_, index) => ({
    id: index + 1,
    name: `用户${index + 1}`,
    email: `user${index + 1}@example.com`,
    status: index % 2 === 0 ? 'active' : 'inactive'
  }))
)

// 高级验证
const advancedValidation = useValidation({
  rules: {
    username: [
      { required: true, message: '用户名是必填项' },
      { minLength: 3, message: '用户名至少3个字符' },
      { 
        validator: async (value) => {
          // 模拟异步验证
          await new Promise(resolve => setTimeout(resolve, 500))
          if (value === 'admin') {
            return '用户名不能是admin'
          }
          return true
        }
      }
    ],
    password: [
      { required: true, message: '密码是必填项' },
      commonRules.strongPassword
    ],
    confirmPassword: [
      { required: true, message: '确认密码是必填项' },
      {
        validator: (value, data) => {
          if (value !== data.password) {
            return '两次密码输入不一致'
          }
          return true
        }
      }
    ]
  }
})

// 配置对象
const composableConfig = computed<ViaGridConfig>(() => ({
  schema: {
    name: { type: 'text', label: '姓名', prop: 'name' },
    email: { type: 'text', label: '邮箱', prop: 'email' },
    status: { type: 'select', label: '状态', prop: 'status' }
  },
  api: {
    search: async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return { data: tableData.value, total: tableData.value.length }
    }
  }
}))

const i18nConfig = ref<ViaGridConfig>({
  schema: {
    name: { type: 'text', label: i18n.t('form.name'), prop: 'name' },
    email: { type: 'text', label: i18n.t('form.email'), prop: 'email' }
  },
  api: {
    search: async () => ({
      data: [
        { id: 1, name: '张三', email: '<EMAIL>' }
      ],
      total: 1
    })
  }
})

const performanceConfig = ref<ViaGridConfig>({
  schema: {
    id: { type: 'number', label: 'ID', prop: 'id' },
    name: { type: 'text', label: '姓名', prop: 'name' },
    email: { type: 'text', label: '邮箱', prop: 'email' },
    status: { type: 'select', label: '状态', prop: 'status' }
  },
  api: {
    search: async () => ({
      data: largeDataset.value.slice(0, 100), // 分页加载
      total: largeDataset.value.length
    })
  },
  table: {
    virtualScroll: true,
    height: 400
  }
})

const advancedValidationConfig = ref<ViaGridConfig>({
  schema: {
    username: {
      type: 'text',
      label: '用户名',
      prop: 'username',
      form: { rules: advancedValidation.rules.username }
    },
    password: {
      type: 'password',
      label: '密码',
      prop: 'password',
      form: { rules: advancedValidation.rules.password }
    },
    confirmPassword: {
      type: 'password',
      label: '确认密码',
      prop: 'confirmPassword',
      form: { rules: advancedValidation.rules.confirmPassword }
    }
  },
  api: {
    search: async () => ({ data: [], total: 0 }),
    create: async (data) => {
      const isValid = await advancedValidation.validate(data)
      if (!isValid) {
        throw new Error('表单验证失败')
      }
      return { success: true }
    }
  }
})

// 事件处理
const handleSearch = () => search()
const handleReset = () => reset()
const handleAdd = () => openForm('add')
const handleRefresh = () => refresh()
const triggerError = () => setError('这是一个测试错误', 'business')
</script>

<style scoped>
.advanced-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.example-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.controls {
  margin-bottom: 20px;
}

.controls button {
  margin-right: 10px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
}

.controls button:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.status {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.status p {
  margin: 5px 0;
}

.language-switcher {
  margin-bottom: 20px;
}

.language-switcher label {
  margin-right: 10px;
}

.language-switcher select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.performance-demo p {
  margin-bottom: 10px;
  font-weight: bold;
}
</style>
