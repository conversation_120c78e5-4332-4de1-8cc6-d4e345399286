# @via-grid/vue3

基于 vxe-table v4.x 和 Vue 3 Composition API 的现代化表格组件库。

## 🚀 特性

- ✅ **Vue 3 Composition API** - 完全基于 Composition API 设计
- ✅ **TypeScript 支持** - 完整的类型定义和智能提示
- ✅ **多 UI 库支持** - Element Plus、Ant Design Vue 4.x、Naive UI
- ✅ **插槽系统** - 基于连字符命名的灵活插槽系统
- ✅ **约定优于配置** - 最小配置即可使用
- ✅ **响应式设计** - 完全响应式的数据流

## 📦 安装

```bash
npm install @via-grid/vue3
# 或
yarn add @via-grid/vue3
# 或
pnpm add @via-grid/vue3
```

## 🔧 依赖

```bash
# 核心依赖
npm install vue@^3.2.0 vxe-table@^4.0.0

# UI 库依赖（选择其一）
npm install element-plus  # Element Plus
npm install ant-design-vue@^4.0.0  # Ant Design Vue 4.x
npm install naive-ui  # Naive UI

# 可选的 vxe-table 渲染插件
npm install @vxe-ui/plugin-render-element  # Element Plus 支持
npm install @vxe-ui/plugin-render-antd     # Ant Design Vue 支持
npm install @vxe-ui/plugin-render-naive    # Naive UI 支持
```

## 🎯 快速开始

### 1. 全局注册

```typescript
import { createApp } from 'vue'
import ViaGrid from '@via-grid/vue3'
import App from './App.vue'

const app = createApp(App)

// 注册插件
app.use(ViaGrid)

app.mount('#app')
```

### 2. 基础使用

```vue
<template>
  <ViaGrid :config="gridConfig" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ViaGridConfig } from '@via-grid/vue3'

const gridConfig = ref<ViaGridConfig>({
  schema: {
    name: {
      type: 'text',
      label: '姓名',
      prop: 'name'
    },
    age: {
      type: 'number',
      label: '年龄',
      prop: 'age'
    },
    email: {
      type: 'text',
      label: '邮箱',
      prop: 'email'
    }
  },
  api: {
    search: async (params) => {
      const response = await fetch('/api/users', {
        method: 'POST',
        body: JSON.stringify(params)
      })
      return response.json()
    },
    create: async (data) => {
      const response = await fetch('/api/users', {
        method: 'POST',
        body: JSON.stringify(data)
      })
      return response.json()
    },
    update: async (data) => {
      const response = await fetch(`/api/users/${data.id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
      return response.json()
    },
    delete: async (data) => {
      const response = await fetch(`/api/users/${data.id}`, {
        method: 'DELETE'
      })
      return response.json()
    }
  }
})
</script>
```

### 3. 使用 Composables

```vue
<template>
  <div>
    <ViaSearch 
      @search="handleSearch"
      @reset="handleReset"
    />
    <ViaTable 
      :data="tableData"
      :loading="loading"
      @view="handleView"
      @edit="handleEdit"
      @delete="handleDelete"
    />
    <ViaPager 
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { useGrid } from '@via-grid/vue3'

const grid = useGrid({
  config: gridConfig,
  immediate: true
})

const {
  // 状态
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  
  // 方法
  search,
  reset,
  refresh,
  openForm,
  openInfo
} = grid

const handleSearch = () => search()
const handleReset = () => reset()
const handleView = (row: any) => openInfo(row)
const handleEdit = (row: any) => openForm('edit', row)
const handleDelete = (row: any) => {
  // 自定义删除逻辑
}
const handlePageChange = (page: number) => grid.setPage(page)
const handlePageSizeChange = (size: number) => grid.setPageSize(size)
</script>
```

## 🎨 插槽系统

Via Grid 提供了灵活的插槽系统，支持连字符命名规范：

### 基础插槽

```vue
<template>
  <ViaGrid :config="config">
    <!-- 区域插槽 -->
    <template #search-to-toolbar>
      <div>搜索和工具栏之间的内容</div>
    </template>
    
    <template #toolbar-to-table>
      <div>工具栏和表格之间的内容</div>
    </template>
    
    <template #table-to-pager>
      <div>表格和分页之间的内容</div>
    </template>
    
    <!-- 工具栏插槽 -->
    <template #toolbar-left>
      <button>自定义按钮</button>
    </template>
    
    <template #toolbar-right>
      <button>右侧按钮</button>
    </template>
  </ViaGrid>
</template>
```

### 字段插槽

```vue
<template>
  <ViaGrid :config="config">
    <!-- 搜索字段插槽 -->
    <template #search-name="{ field, value, onChange }">
      <input 
        :value="value" 
        @input="onChange($event.target.value)"
        placeholder="自定义搜索框"
      />
    </template>
    
    <!-- 表格字段插槽 -->
    <template #table-name="{ field, value, row }">
      <strong>{{ value }}</strong>
    </template>
    
    <!-- 表格字段头部插槽 -->
    <template #table-name-header="{ field, column }">
      <span style="color: red;">{{ field.label }}</span>
    </template>
    
    <!-- 表单字段插槽 -->
    <template #form-name="{ field, value, onChange, mode }">
      <input 
        :value="value" 
        @input="onChange($event.target.value)"
        :disabled="mode === 'view'"
      />
    </template>
    
    <!-- 详情字段插槽 -->
    <template #info-name="{ field, value, displayValue, data }">
      <span class="custom-info">{{ displayValue }}</span>
    </template>
  </ViaGrid>
</template>
```

## 📚 API 文档

### ViaGrid Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| config | `ViaGridConfig` | - | 网格配置 |
| modelValue | `any[]` | `[]` | 表格数据 |
| loading | `boolean` | `false` | 加载状态 |
| height | `string \| number` | `'auto'` | 高度 |
| maxHeight | `string \| number` | `'none'` | 最大高度 |

### ViaGrid Events

| 事件 | 参数 | 说明 |
|------|------|------|
| search | `params: Record<string, any>` | 搜索事件 |
| reset | - | 重置事件 |
| add | - | 新增事件 |
| edit | `row: any` | 编辑事件 |
| delete | `row: any` | 删除事件 |
| view | `row: any` | 查看事件 |
| refresh | - | 刷新事件 |
| page-change | `page: number` | 页码变化 |
| page-size-change | `pageSize: number` | 页大小变化 |

### 核心 Composables

#### useGrid
主要的网格管理逻辑，提供完整的状态管理和操作方法。

```typescript
const grid = useGrid({
  config: gridConfig,
  immediate: true
})

const {
  // 状态
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  hasError,
  errorMessage,

  // 方法
  search,
  reset,
  refresh,
  openForm,
  closeForm,
  openInfo,
  closeInfo,
  setError,
  clearError
} = grid
```

#### useSearch
搜索功能逻辑，支持自动搜索和防抖。

```typescript
const search = useSearch({
  autoSearch: true,
  autoSearchDelay: 300
})

const {
  searchParams,
  searchFields,
  search,
  reset,
  setFieldValue,
  getFieldValue
} = search
```

#### useTable
表格功能逻辑，支持选择、操作等。

```typescript
const table = useTable({
  selectionMode: 'multiple',
  showActions: true
})

const {
  tableData,
  selectedRows,
  selectRow,
  clearSelection,
  handleView,
  handleEdit,
  handleDelete
} = table
```

#### useForm
表单功能逻辑，支持验证和数据绑定。

```typescript
const form = useForm({
  mode: 'add',
  autoValidate: true
})

const {
  formData,
  formVisible,
  errors,
  validate,
  submit,
  reset
} = form
```

### 增强功能 Composables

#### useError
统一的错误处理逻辑。

```typescript
const error = useError({
  autoClear: true,
  clearDelay: 5000
})

const {
  error,
  hasError,
  errorMessage,
  setError,
  clearError,
  handleAsyncError
} = error
```

#### useLoading
统一的加载状态管理。

```typescript
const loading = useLoading({
  defaultTimeout: 30000,
  minDuration: 300
})

const {
  loading,
  startLoading,
  endLoading,
  withLoading
} = loading
```

#### useValidation
数据验证逻辑。

```typescript
const validation = useValidation({
  rules: {
    name: [{ required: true, message: '姓名是必填项' }],
    email: [commonRules.email]
  }
})

const {
  errors,
  isValid,
  validate,
  validateField,
  clearErrors
} = validation
```

#### useI18n
国际化支持。

```typescript
const i18n = useI18n({
  defaultLocale: 'zh-CN'
})

const {
  locale,
  t,
  setLocale,
  addMessages
} = i18n
```

#### useVirtualScroll
虚拟滚动性能优化。

```typescript
const virtualScroll = useVirtualScroll({
  containerHeight: 400,
  itemHeight: 40,
  bufferSize: 5
})

const {
  visibleItems,
  scrollToIndex,
  onScroll
} = virtualScroll
```

## 🔗 相关链接

- [Via Grid 文档](https://github.com/viarotel/via-grid)
- [Vue 3 文档](https://vuejs.org/)
- [VXE Table 文档](https://vxetable.cn/)

## 📄 许可证

[MIT](../../LICENSE) © 2025 ViaGrid
