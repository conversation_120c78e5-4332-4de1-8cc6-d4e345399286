{"name": "@via-grid/vue3", "version": "0.0.0", "description": "Via Grid Vue3 实现包", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "pnpm run clean && vite build", "build:watch": "vite build --watch", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "type-check": "vue-tsc --noEmit", "preview": "vite preview", "prepublishOnly": "pnpm run build"}, "dependencies": {"@via-grid/core": "workspace:*", "@via-grid/shared": "workspace:*"}, "peerDependencies": {"vue": "^3.2.0", "vxe-table": "^4.0.0"}, "peerDependenciesMeta": {"@vxe-ui/plugin-render-element": {"optional": true}, "@vxe-ui/plugin-render-antd": {"optional": true}, "@vxe-ui/plugin-render-naive": {"optional": true}, "element-plus": {"optional": true}, "ant-design-vue": {"optional": true}, "naive-ui": {"optional": true}}, "keywords": ["via-grid", "vue3", "vxe-table", "element-plus", "ant-design-vue", "naive-ui"], "author": "ViaGrid Team", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "@vue/test-utils": "^2.4.4", "eslint": "^8.56.0", "jsdom": "^24.0.0", "rimraf": "^5.0.5", "typescript": "^5.3.3", "vite": "^5.0.12", "vite-plugin-dts": "^3.7.2", "vitest": "^1.2.2", "vue": "^3.4.15", "vue-tsc": "^1.8.27"}}