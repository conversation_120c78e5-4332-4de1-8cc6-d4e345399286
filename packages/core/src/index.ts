// 导出所有核心模块
export * from './types'
export * from './schema'
export * from './adapters'
export * from './utils'
export * from './config'

// 注册默认适配器
import { AdapterFactory } from './adapters/factory'
import { ElementAdapter } from './adapters/element'
import { AntdAdapter } from './adapters/antd'
import { NaiveAdapter } from './adapters/naive'

// 注册 Element UI/Plus 适配器
AdapterFactory.register('element-ui', 'vue2', ElementAdapter)
AdapterFactory.register('element-plus', 'vue3', ElementAdapter)

// 注册 Ant Design Vue 适配器
AdapterFactory.register('ant-design-vue', 'vue2', AntdAdapter)
AdapterFactory.register('ant-design-vue', 'vue3', AntdAdapter)

// 注册 Naive UI 适配器
AdapterFactory.register('naive-ui', 'vue3', NaiveAdapter)
