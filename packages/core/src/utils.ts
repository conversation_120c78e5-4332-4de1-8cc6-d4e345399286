/**
 * Via Grid 核心工具函数
 */

import { merge as deepMerge, isFunction, isString, isArray, cloneDeep } from 'es-toolkit'
import type { ViaGridConfig, FieldSchema } from './types'
import type { Recordable } from '@via-grid/shared'

/**
 * 合并配置
 */
export function mergeConfig(defaultConfig: Partial<ViaGridConfig>, userConfig: Partial<ViaGridConfig>): ViaGridConfig {
  return deepMerge({}, defaultConfig, userConfig) as ViaGridConfig
}

/**
 * 解析 API 配置
 */
export function parseApiConfig(api: string | Function, params?: Recordable): Promise<any> {
  if (isString(api)) {
    // 如果是字符串，替换参数占位符
    let url = api as string
    if (params) {
      Object.keys(params).forEach(key => {
        url = url.replace(`:${key}`, params[key])
      })
    }
    
    // 这里应该调用实际的 HTTP 客户端
    // 暂时返回一个 Promise
    return Promise.resolve({ url, params })
  }

  if (isFunction(api)) {
    return Promise.resolve((api as Function)(params))
  }

  return Promise.reject(new Error('Invalid API configuration'))
}

/**
 * 处理表单数据
 */
export function processFormData(data: Recordable, schema: Record<string, FieldSchema>): Recordable {
  const processedData = cloneDeep(data)

  Object.keys(schema).forEach(fieldName => {
    const field = schema[fieldName]
    const value = processedData[fieldName]

    // 应用参数转换函数
    if (field.parameter && value !== undefined) {
      processedData[fieldName] = field.parameter(value, processedData)
    }

    // 处理空值
    if (value === '' || value === null) {
      delete processedData[fieldName]
    }
  })

  return processedData
}

/**
 * 处理响应数据
 */
export function processResponseData(data: any[], schema: Record<string, FieldSchema>): any[] {
  return data.map(row => {
    const processedRow = cloneDeep(row)

    Object.keys(schema).forEach(fieldName => {
      const field = schema[fieldName]
      const value = processedRow[fieldName]

      // 应用格式化函数
      if (field.formatter && value !== undefined) {
        processedRow[`${fieldName}_formatted`] = field.formatter(value, processedRow)
      }
    })

    return processedRow
  })
}

/**
 * 验证表单数据
 */
export function validateFormData(data: Recordable, schema: Record<string, FieldSchema>): { valid: boolean; errors: Record<string, string[]> } {
  const errors: Record<string, string[]> = {}

  Object.keys(schema).forEach(fieldName => {
    const field = schema[fieldName]
    const value = data[fieldName]
    const fieldErrors: string[] = []

    if (field.rules) {
      field.rules.forEach(rule => {
        // 必填验证
        if (rule.required && (value === undefined || value === null || value === '')) {
          fieldErrors.push(rule.message || `${field.label}不能为空`)
        }

        // 长度验证
        if (value && rule.min !== undefined && String(value).length < rule.min) {
          fieldErrors.push(rule.message || `${field.label}长度不能少于${rule.min}个字符`)
        }

        if (value && rule.max !== undefined && String(value).length > rule.max) {
          fieldErrors.push(rule.message || `${field.label}长度不能超过${rule.max}个字符`)
        }

        // 正则验证
        if (value && rule.pattern && !rule.pattern.test(String(value))) {
          fieldErrors.push(rule.message || `${field.label}格式不正确`)
        }

        // 自定义验证
        if (rule.validator && isFunction(rule.validator)) {
          try {
            rule.validator(rule, value, (error?: Error) => {
              if (error) {
                fieldErrors.push(error.message || `${field.label}验证失败`)
              }
            })
          } catch (error) {
            fieldErrors.push(`${field.label}验证失败`)
          }
        }
      })
    }

    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors
    }
  })

  return {
    valid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 生成表格列配置
 */
export function generateTableColumns(schema: Record<string, FieldSchema>): any[] {
  const columns: any[] = []

  Object.keys(schema).forEach(fieldName => {
    const field = schema[fieldName]
    const tableConfig = field.table || {}

    const column = {
      field: fieldName,
      title: field.label,
      width: tableConfig.width,
      minWidth: tableConfig.minWidth,
      fixed: tableConfig.fixed,
      sortable: tableConfig.sortable,
      align: tableConfig.align,
      headerAlign: tableConfig.headerAlign,
      ...tableConfig.columnProps
    }

    // 如果有自定义渲染配置
    if (tableConfig.cellRender) {
      column.cellRender = tableConfig.cellRender
    }

    columns.push(column)
  })

  return columns
}

/**
 * 生成表单项配置
 */
export function generateFormItems(schema: Record<string, FieldSchema>, mode: 'add' | 'edit' | 'form' = 'form'): any[] {
  const items: any[] = []

  Object.keys(schema).forEach(fieldName => {
    const field = schema[fieldName]
    const formConfig = field.form || {}
    const modeConfig = mode !== 'form' ? (field[mode] || {}) : {}

    const item = {
      field: fieldName,
      title: field.label,
      span: formConfig.span || modeConfig.span || 24,
      itemRender: {
        name: field.type,
        props: {
          placeholder: field.placeholder || `请输入${field.label}`,
          ...field.fieldProps,
          ...formConfig.fieldProps,
          ...modeConfig.fieldProps
        }
      },
      ...formConfig.itemProps,
      ...modeConfig.itemProps
    }

    items.push(item)
  })

  return items
}

/**
 * 处理权限检查
 */
export function checkPermission(permission: any, context?: Recordable): boolean {
  if (typeof permission === 'boolean') {
    return permission
  }

  if (isFunction(permission)) {
    return permission(context)
  }

  return true
}

/**
 * 处理行级权限检查
 */
export function checkRowPermission(permission: any, row: any, context?: Recordable): boolean {
  if (typeof permission === 'boolean') {
    return permission
  }

  if (isFunction(permission)) {
    return permission(row, context)
  }

  return true
}

/**
 * 生成唯一键
 */
export function generateKey(prefix = 'via-grid'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 下载文件
 */
export function downloadFile(data: Blob | string, filename: string): void {
  const blob = typeof data === 'string' ? new Blob([data], { type: 'text/plain' }) : data
  const url = window.URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 导出数据为 CSV
 */
export function exportToCSV(data: any[], columns: any[], filename = 'export.csv'): void {
  const headers = columns.map(col => col.title || col.field).join(',')
  const rows = data.map(row => 
    columns.map(col => {
      const value = row[col.field] || ''
      return `"${String(value).replace(/"/g, '""')}"`
    }).join(',')
  )
  
  const csvContent = [headers, ...rows].join('\n')
  downloadFile(csvContent, filename)
}
