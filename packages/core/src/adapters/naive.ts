/**
 * Naive UI 适配器
 * 仅支持 Vue3
 */

import type { VueVersion, UILibrary, FieldType, Recordable } from '@via-grid/shared'
import { FIELD_TYPES } from '@via-grid/shared'
import { BaseAdapter, createDefaultFieldRenderer, type FieldRenderer, type ComponentRenderConfig } from './base'
import type { FieldSchema } from '../types'

/**
 * Naive UI 字段渲染器
 */
class NaiveFieldRenderer implements FieldRenderer {
  renderSearchField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: 'NInput',
          props: {
            value,
            placeholder: field.placeholder || `请输入${field.label}`,
            clearable: true,
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: 'NSelect',
          props: {
            value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            filterable: true,
            options: field.options || [],
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      case FIELD_TYPES.DATE:
        return {
          name: 'NDatePicker',
          props: {
            value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderSearchField(field, value, onChange)
    }
  }

  renderTableCell(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    switch (field.type) {
      case FIELD_TYPES.TAG:
        return {
          name: 'NTag',
          props: {
            type: 'info',
            size: 'small',
            ...field.fieldProps
          },
          slots: {
            default: () => value
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: 'NSwitch',
          props: {
            value,
            disabled: true,
            ...field.fieldProps
          }
        }

      default:
        return {
          name: 'span',
          props: {
            textContent: value || '-'
          }
        }
    }
  }

  renderFormField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: 'NInput',
          props: {
            value,
            placeholder: field.placeholder || `请输入${field.label}`,
            clearable: true,
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      case FIELD_TYPES.NUMBER:
        return {
          name: 'NInputNumber',
          props: {
            value,
            placeholder: field.placeholder || `请输入${field.label}`,
            clearable: true,
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: 'NSelect',
          props: {
            value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            filterable: true,
            options: field.options || [],
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: 'NSwitch',
          props: {
            value,
            ...field.fieldProps
          },
          events: {
            'update:value': onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderFormField(field, value, onChange)
    }
  }

  renderInfoField(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    return {
      name: 'span',
      props: {
        textContent: value || '-'
      }
    }
  }
}

/**
 * Naive UI 适配器
 */
export class NaiveAdapter extends BaseAdapter {
  private fieldRenderer: FieldRenderer

  constructor(vueVersion: VueVersion, uiLibrary: UILibrary) {
    super(vueVersion, uiLibrary)
    this.fieldRenderer = new NaiveFieldRenderer()
  }

  getSupportedFieldTypes(): FieldType[] {
    return [
      FIELD_TYPES.TEXT,
      FIELD_TYPES.NUMBER,
      FIELD_TYPES.SELECT,
      FIELD_TYPES.DATE,
      FIELD_TYPES.SWITCH,
      FIELD_TYPES.TAG,
      FIELD_TYPES.RADIO,
      FIELD_TYPES.CHECKBOX
    ]
  }

  getFieldRenderer(fieldType: FieldType): FieldRenderer | null {
    if (this.supportsFieldType(fieldType)) {
      return this.fieldRenderer
    }
    return null
  }

  getDefaultConfig(): Recordable {
    return {
      form: {
        labelWidth: 100,
        labelPlacement: 'left',
        size: 'medium'
      },
      table: {
        bordered: true,
        striped: true,
        size: 'medium'
      },
      modal: {
        style: { width: '800px' },
        maskClosable: false,
        closeOnEsc: true,
        autoFocus: true,
        transformOrigin: 'center'
      }
    }
  }

  protected isVueVersionSupported(): boolean {
    return this.vueVersion === 'vue3'
  }

  protected isUILibrarySupported(): boolean {
    return this.uiLibrary === 'naive-ui'
  }
}
