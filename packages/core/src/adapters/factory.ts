/**
 * Via Grid 适配器工厂
 * 负责创建和管理适配器实例
 */

import { detectVueVersion, detectUILibrary } from '@via-grid/shared'
import type { VueVersion, UILibrary } from '@via-grid/shared'
import type { BaseAdapter } from './base'

/**
 * 适配器构造函数类型
 */
export type AdapterConstructor = new (vueVersion: VueVersion, uiLibrary: UILibrary) => BaseAdapter

/**
 * 适配器工厂类
 */
export class AdapterFactory {
  private static adapters = new Map<string, AdapterConstructor>()
  private static instances = new Map<string, BaseAdapter>()

  /**
   * 注册适配器
   */
  static register(uiLibrary: UILibrary, vueVersion: VueVersion, adapter: AdapterConstructor): void {
    const key = this.getAdapterKey(uiLibrary, vueVersion)
    this.adapters.set(key, adapter)
  }

  /**
   * 创建适配器实例
   */
  static create(uiLibrary?: UILibrary, vueVersion?: VueVersion): BaseAdapter {
    const detectedVueVersion = vueVersion || detectVueVersion()
    const detectedUILibrary = uiLibrary || detectUILibrary()

    if (!detectedUILibrary) {
      throw new Error('No UI library detected. Please specify a UI library or ensure one is available.')
    }

    const key = this.getAdapterKey(detectedUILibrary, detectedVueVersion)

    // 如果已有实例，直接返回
    if (this.instances.has(key)) {
      return this.instances.get(key)!
    }

    // 查找适配器构造函数
    const AdapterClass = this.adapters.get(key)
    if (!AdapterClass) {
      throw new Error(`No adapter found for ${detectedUILibrary} with ${detectedVueVersion}`)
    }

    // 创建实例
    const instance = new AdapterClass(detectedVueVersion, detectedUILibrary)
    
    // 验证兼容性
    const { compatible, issues } = instance.validateCompatibility()
    if (!compatible) {
      throw new Error(`Adapter compatibility issues: ${issues.join(', ')}`)
    }

    // 缓存实例
    this.instances.set(key, instance)
    return instance
  }

  /**
   * 获取适配器键名
   */
  private static getAdapterKey(uiLibrary: UILibrary, vueVersion: VueVersion): string {
    return `${uiLibrary}-${vueVersion}`
  }

  /**
   * 获取所有已注册的适配器
   */
  static getRegisteredAdapters(): Array<{ uiLibrary: UILibrary; vueVersion: VueVersion }> {
    return Array.from(this.adapters.keys()).map(key => {
      const [uiLibrary, vueVersion] = key.split('-') as [UILibrary, VueVersion]
      return { uiLibrary, vueVersion }
    })
  }

  /**
   * 检查是否支持指定的组合
   */
  static isSupported(uiLibrary: UILibrary, vueVersion: VueVersion): boolean {
    const key = this.getAdapterKey(uiLibrary, vueVersion)
    return this.adapters.has(key)
  }

  /**
   * 清除所有实例缓存
   */
  static clearCache(): void {
    this.instances.clear()
  }

  /**
   * 获取当前环境的最佳适配器
   */
  static getBestAdapter(): BaseAdapter {
    const vueVersion = detectVueVersion()
    const uiLibrary = detectUILibrary()

    if (uiLibrary) {
      return this.create(uiLibrary, vueVersion)
    }

    // 如果没有检测到 UI 库，尝试按优先级创建
    const priorities: UILibrary[] = vueVersion === 'vue3' 
      ? ['element-plus', 'ant-design-vue', 'naive-ui']
      : ['element-ui', 'ant-design-vue']

    for (const lib of priorities) {
      if (this.isSupported(lib, vueVersion)) {
        return this.create(lib, vueVersion)
      }
    }

    throw new Error(`No compatible adapter found for Vue ${vueVersion}`)
  }
}

/**
 * 创建适配器实例的便捷函数
 */
export function createAdapter(uiLibrary?: UILibrary, vueVersion?: VueVersion): BaseAdapter {
  return AdapterFactory.create(uiLibrary, vueVersion)
}

/**
 * 获取最佳适配器的便捷函数
 */
export function getBestAdapter(): BaseAdapter {
  return AdapterFactory.getBestAdapter()
}
