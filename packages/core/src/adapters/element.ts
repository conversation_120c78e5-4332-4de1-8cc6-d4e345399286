/**
 * Element UI/Plus 适配器
 * 支持 Element UI (Vue2) 和 Element Plus (Vue3)
 */

import type { VueVersion, UILibrary, FieldType, Recordable } from '@via-grid/shared'
import { FIELD_TYPES } from '@via-grid/shared'
import { BaseAdapter, createDefaultFieldRenderer, type FieldRenderer, type ComponentRenderConfig } from './base'
import type { FieldSchema } from '../types'

/**
 * Element 字段渲染器
 */
class ElementFieldRenderer implements FieldRenderer {
  private isVue3: boolean

  constructor(isVue3: boolean) {
    this.isVue3 = isVue3
  }

  private getComponentPrefix(): string {
    return this.isVue3 ? 'El' : 'el-'
  }

  renderSearchField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: `${prefix}input`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            clearable: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'input']: onChange
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: `${prefix}select`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            filterable: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'change']: onChange
          },
          slots: {
            default: () => {
              return field.options?.map(option => ({
                name: `${prefix}option`,
                props: {
                  label: option.label,
                  value: option.value,
                  disabled: option.disabled
                }
              })) || []
            }
          }
        }

      case FIELD_TYPES.DATE:
        return {
          name: `${prefix}date-picker`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'change']: onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderSearchField(field, value, onChange)
    }
  }

  renderTableCell(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TAG:
        return {
          name: `${prefix}tag`,
          props: {
            type: 'info',
            size: 'small',
            ...field.fieldProps
          },
          slots: {
            default: () => value
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: `${prefix}switch`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            disabled: true,
            ...field.fieldProps
          }
        }

      default:
        return {
          name: 'span',
          props: {
            textContent: value || '-'
          }
        }
    }
  }

  renderFormField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: `${prefix}input`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            clearable: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'input']: onChange
          }
        }

      case FIELD_TYPES.NUMBER:
        return {
          name: `${prefix}input-number`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            controlsPosition: 'right',
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'change']: onChange
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: `${prefix}select`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            clearable: true,
            filterable: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'change']: onChange
          },
          slots: {
            default: () => {
              return field.options?.map(option => ({
                name: `${prefix}option`,
                props: {
                  label: option.label,
                  value: option.value,
                  disabled: option.disabled
                }
              })) || []
            }
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: `${prefix}switch`,
          props: {
            [this.isVue3 ? 'modelValue' : 'value']: value,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:modelValue' : 'change']: onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderFormField(field, value, onChange)
    }
  }

  renderInfoField(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    return {
      name: 'span',
      props: {
        textContent: value || '-'
      }
    }
  }
}

/**
 * Element 适配器
 */
export class ElementAdapter extends BaseAdapter {
  private fieldRenderer: FieldRenderer

  constructor(vueVersion: VueVersion, uiLibrary: UILibrary) {
    super(vueVersion, uiLibrary)
    this.fieldRenderer = new ElementFieldRenderer(vueVersion === 'vue3')
  }

  getSupportedFieldTypes(): FieldType[] {
    return [
      FIELD_TYPES.TEXT,
      FIELD_TYPES.NUMBER,
      FIELD_TYPES.SELECT,
      FIELD_TYPES.DATE,
      FIELD_TYPES.SWITCH,
      FIELD_TYPES.TAG,
      FIELD_TYPES.RADIO,
      FIELD_TYPES.CHECKBOX
    ]
  }

  getFieldRenderer(fieldType: FieldType): FieldRenderer | null {
    if (this.supportsFieldType(fieldType)) {
      return this.fieldRenderer
    }
    return null
  }

  getDefaultConfig(): Recordable {
    return {
      form: {
        labelWidth: '100px',
        labelPosition: 'right',
        size: 'medium'
      },
      table: {
        border: true,
        stripe: true,
        size: 'medium'
      },
      dialog: {
        width: '800px',
        modal: true,
        lockScroll: true,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        showClose: true,
        destroyOnClose: true
      }
    }
  }

  protected isVueVersionSupported(): boolean {
    if (this.uiLibrary === 'element-ui') {
      return this.vueVersion === 'vue2'
    }
    if (this.uiLibrary === 'element-plus') {
      return this.vueVersion === 'vue3'
    }
    return false
  }

  protected isUILibrarySupported(): boolean {
    return ['element-ui', 'element-plus'].includes(this.uiLibrary)
  }
}
