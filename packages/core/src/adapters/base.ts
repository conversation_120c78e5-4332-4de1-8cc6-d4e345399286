/**
 * Via Grid 适配器基类
 * 定义所有适配器必须实现的接口
 */

import type { VueVersion, UILibrary, FieldType, Recordable } from '@via-grid/shared'
import type { FieldSchema } from '../types'

/**
 * 组件渲染配置
 */
export interface ComponentRenderConfig {
  name: string
  props?: Recordable
  attrs?: Recordable
  events?: Recordable
  slots?: Recordable
}

/**
 * 字段渲染器接口
 */
export interface FieldRenderer {
  /**
   * 渲染搜索字段
   */
  renderSearchField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig

  /**
   * 渲染表格单元格
   */
  renderTableCell(field: FieldSchema, value: any, row: any): ComponentRenderConfig

  /**
   * 渲染表单字段
   */
  renderFormField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig

  /**
   * 渲染详情字段
   */
  renderInfoField(field: FieldSchema, value: any, row: any): ComponentRenderConfig
}

/**
 * 适配器基类
 */
export abstract class BaseAdapter {
  protected vueVersion: VueVersion
  protected uiLibrary: UILibrary

  constructor(vueVersion: VueVersion, uiLibrary: UILibrary) {
    this.vueVersion = vueVersion
    this.uiLibrary = uiLibrary
  }

  /**
   * 获取支持的字段类型
   */
  abstract getSupportedFieldTypes(): FieldType[]

  /**
   * 获取字段渲染器
   */
  abstract getFieldRenderer(fieldType: FieldType): FieldRenderer | null

  /**
   * 获取默认配置
   */
  abstract getDefaultConfig(): Recordable

  /**
   * 检查是否支持指定的字段类型
   */
  supportsFieldType(fieldType: FieldType): boolean {
    return this.getSupportedFieldTypes().includes(fieldType)
  }

  /**
   * 渲染字段
   */
  renderField(
    field: FieldSchema,
    scope: 'search' | 'table' | 'form' | 'info',
    value: any,
    context: Recordable = {}
  ): ComponentRenderConfig | null {
    const renderer = this.getFieldRenderer(field.type)
    if (!renderer) return null

    const { onChange, row } = context

    switch (scope) {
      case 'search':
        return renderer.renderSearchField(field, value, onChange)
      case 'table':
        return renderer.renderTableCell(field, value, row)
      case 'form':
        return renderer.renderFormField(field, value, onChange)
      case 'info':
        return renderer.renderInfoField(field, value, row)
      default:
        return null
    }
  }

  /**
   * 获取组件库特定的配置
   */
  getUILibraryConfig(): Recordable {
    return {}
  }

  /**
   * 获取 Vue 版本特定的配置
   */
  getVueVersionConfig(): Recordable {
    return {}
  }

  /**
   * 合并配置
   */
  mergeConfig(userConfig: Recordable): Recordable {
    return {
      ...this.getDefaultConfig(),
      ...this.getUILibraryConfig(),
      ...this.getVueVersionConfig(),
      ...userConfig
    }
  }

  /**
   * 验证适配器兼容性
   */
  validateCompatibility(): { compatible: boolean; issues: string[] } {
    const issues: string[] = []

    // 检查 Vue 版本兼容性
    if (!this.isVueVersionSupported()) {
      issues.push(`Vue version ${this.vueVersion} is not supported`)
    }

    // 检查 UI 库兼容性
    if (!this.isUILibrarySupported()) {
      issues.push(`UI library ${this.uiLibrary} is not supported`)
    }

    return {
      compatible: issues.length === 0,
      issues
    }
  }

  /**
   * 检查 Vue 版本是否支持
   */
  protected abstract isVueVersionSupported(): boolean

  /**
   * 检查 UI 库是否支持
   */
  protected abstract isUILibrarySupported(): boolean
}

/**
 * 创建默认的字段渲染器
 */
export function createDefaultFieldRenderer(): FieldRenderer {
  return {
    renderSearchField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
      return {
        name: 'input',
        props: {
          value,
          placeholder: field.placeholder || `请输入${field.label}`,
          ...field.fieldProps
        },
        events: {
          input: onChange
        }
      }
    },

    renderTableCell(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
      return {
        name: 'span',
        props: {
          textContent: value
        }
      }
    },

    renderFormField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
      return {
        name: 'input',
        props: {
          value,
          placeholder: field.placeholder || `请输入${field.label}`,
          ...field.fieldProps
        },
        events: {
          input: onChange
        }
      }
    },

    renderInfoField(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
      return {
        name: 'span',
        props: {
          textContent: value || '-'
        }
      }
    }
  }
}
