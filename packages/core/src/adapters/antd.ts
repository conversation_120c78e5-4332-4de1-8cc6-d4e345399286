/**
 * Ant Design Vue 适配器
 * 支持 Ant Design Vue 1.x (Vue2) 和 4.x (Vue3)
 */

import type { VueVersion, UILibrary, FieldType, Recordable } from '@via-grid/shared'
import { FIELD_TYPES } from '@via-grid/shared'
import { BaseAdapter, createDefaultFieldRenderer, type FieldRenderer, type ComponentRenderConfig } from './base'
import type { FieldSchema } from '../types'

/**
 * Ant Design Vue 字段渲染器
 */
class AntdFieldRenderer implements FieldRenderer {
  private isVue3: boolean

  constructor(isVue3: boolean) {
    this.isVue3 = isVue3
  }

  private getComponentPrefix(): string {
    return this.isVue3 ? 'A' : 'a-'
  }

  renderSearchField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: `${prefix}input`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            allowClear: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: (e: any) => {
              const val = e?.target?.value ?? e
              onChange(val)
            }
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: `${prefix}select`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            allowClear: true,
            showSearch: true,
            filterOption: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: onChange
          },
          slots: {
            default: () => {
              return field.options?.map(option => ({
                name: `${prefix}select-option`,
                props: {
                  value: option.value,
                  disabled: option.disabled
                },
                slots: {
                  default: () => option.label
                }
              })) || []
            }
          }
        }

      case FIELD_TYPES.DATE:
        return {
          name: `${prefix}date-picker`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            allowClear: true,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderSearchField(field, value, onChange)
    }
  }

  renderTableCell(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TAG:
        return {
          name: `${prefix}tag`,
          props: {
            color: 'blue',
            ...field.fieldProps
          },
          slots: {
            default: () => value
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: `${prefix}switch`,
          props: {
            [this.isVue3 ? 'checked' : 'checked']: value,
            disabled: true,
            ...field.fieldProps
          }
        }

      default:
        return {
          name: 'span',
          props: {
            textContent: value || '-'
          }
        }
    }
  }

  renderFormField(field: FieldSchema, value: any, onChange: (value: any) => void): ComponentRenderConfig {
    const prefix = this.getComponentPrefix()

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return {
          name: `${prefix}input`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            allowClear: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: (e: any) => {
              const val = e?.target?.value ?? e
              onChange(val)
            }
          }
        }

      case FIELD_TYPES.NUMBER:
        return {
          name: `${prefix}input-number`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请输入${field.label}`,
            style: { width: '100%' },
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: onChange
          }
        }

      case FIELD_TYPES.SELECT:
        return {
          name: `${prefix}select`,
          props: {
            [this.isVue3 ? 'value' : 'value']: value,
            placeholder: field.placeholder || `请选择${field.label}`,
            allowClear: true,
            showSearch: true,
            filterOption: true,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:value' : 'change']: onChange
          },
          slots: {
            default: () => {
              return field.options?.map(option => ({
                name: `${prefix}select-option`,
                props: {
                  value: option.value,
                  disabled: option.disabled
                },
                slots: {
                  default: () => option.label
                }
              })) || []
            }
          }
        }

      case FIELD_TYPES.SWITCH:
        return {
          name: `${prefix}switch`,
          props: {
            [this.isVue3 ? 'checked' : 'checked']: value,
            ...field.fieldProps
          },
          events: {
            [this.isVue3 ? 'update:checked' : 'change']: onChange
          }
        }

      default:
        return createDefaultFieldRenderer().renderFormField(field, value, onChange)
    }
  }

  renderInfoField(field: FieldSchema, value: any, row: any): ComponentRenderConfig {
    return {
      name: 'span',
      props: {
        textContent: value || '-'
      }
    }
  }
}

/**
 * Ant Design Vue 适配器
 */
export class AntdAdapter extends BaseAdapter {
  private fieldRenderer: FieldRenderer

  constructor(vueVersion: VueVersion, uiLibrary: UILibrary) {
    super(vueVersion, uiLibrary)
    this.fieldRenderer = new AntdFieldRenderer(vueVersion === 'vue3')
  }

  getSupportedFieldTypes(): FieldType[] {
    return [
      FIELD_TYPES.TEXT,
      FIELD_TYPES.NUMBER,
      FIELD_TYPES.SELECT,
      FIELD_TYPES.DATE,
      FIELD_TYPES.SWITCH,
      FIELD_TYPES.TAG,
      FIELD_TYPES.RADIO,
      FIELD_TYPES.CHECKBOX
    ]
  }

  getFieldRenderer(fieldType: FieldType): FieldRenderer | null {
    if (this.supportsFieldType(fieldType)) {
      return this.fieldRenderer
    }
    return null
  }

  getDefaultConfig(): Recordable {
    return {
      form: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        layout: 'horizontal'
      },
      table: {
        bordered: true,
        size: 'middle'
      },
      modal: {
        width: 800,
        maskClosable: false,
        keyboard: true,
        destroyOnClose: true
      }
    }
  }

  protected isVueVersionSupported(): boolean {
    return ['vue2', 'vue3'].includes(this.vueVersion)
  }

  protected isUILibrarySupported(): boolean {
    return this.uiLibrary === 'ant-design-vue'
  }
}
