/**
 * Via Grid Schema 转换逻辑
 * 负责将配置转换为各个模块可用的格式
 */

import { isFunction, isArray, isString, merge as deepMerge } from 'es-toolkit'
import { formatSlotName } from '@via-grid/shared'
import type { FieldSchema, ViaGridConfig, Scope } from './types'

/**
 * Schema 转换器类
 */
export class SchemaTransformer {
  private config: ViaGridConfig

  constructor(config: ViaGridConfig) {
    this.config = config
  }

  /**
   * 获取指定作用域的字段配置
   */
  getFieldsForScope(scope: Scope): Record<string, FieldSchema> {
    const fields: Record<string, FieldSchema> = {}
    const sortOrder = this.config.sort?.[scope] || Object.keys(this.config.schema)

    // 按照排序配置处理字段
    for (const fieldName of sortOrder) {
      const fieldSchema = this.config.schema[fieldName]
      if (!fieldSchema) continue

      // 检查字段是否在当前作用域中隐藏
      if (this.isFieldHidden(fieldSchema, scope)) continue

      // 合并作用域特定配置
      const scopeConfig = fieldSchema[scope] || {}
      const mergedField = deepMerge({}, fieldSchema, scopeConfig)

      fields[fieldName] = mergedField
    }

    return fields
  }

  /**
   * 检查字段是否在指定作用域中隐藏
   */
  private isFieldHidden(field: FieldSchema, scope: Scope): boolean {
    const { hidden } = field

    if (!hidden) return false

    // 如果是布尔值
    if (typeof hidden === 'boolean') return hidden

    // 如果是字符串数组
    if (isArray(hidden)) {
      return (hidden as string[]).includes(scope)
    }

    // 如果是函数
    if (isFunction(hidden)) {
      return (hidden as Function)({ scope })
    }

    return false
  }

  /**
   * 获取搜索字段配置
   */
  getSearchFields(): Record<string, FieldSchema> {
    return this.getFieldsForScope('search')
  }

  /**
   * 获取表格列配置
   */
  getTableColumns(): Record<string, FieldSchema> {
    return this.getFieldsForScope('table')
  }

  /**
   * 获取表单字段配置
   */
  getFormFields(mode: 'add' | 'edit' | 'form' = 'form'): Record<string, FieldSchema> {
    const fields = this.getFieldsForScope('form')
    
    // 如果是特定模式，再次合并模式特定配置
    if (mode !== 'form') {
      const modeFields: Record<string, FieldSchema> = {}
      for (const [fieldName, fieldSchema] of Object.entries(fields)) {
        const originalField = this.config.schema[fieldName]
        const modeConfig = originalField[mode] || {}
        modeFields[fieldName] = deepMerge({}, fieldSchema, modeConfig)
      }
      return modeFields
    }

    return fields
  }

  /**
   * 获取详情字段配置
   */
  getInfoFields(): Record<string, FieldSchema> {
    return this.getFieldsForScope('info')
  }

  /**
   * 转换字段值（使用 formatter）
   */
  formatFieldValue(fieldName: string, value: any, row?: any, scope?: Scope): any {
    const field = this.config.schema[fieldName]
    if (!field?.formatter) return value

    const context = { scope, row, field }
    return field.formatter(value, row, context)
  }

  /**
   * 转换参数值（使用 parameter）
   */
  transformParameter(fieldName: string, value: any, row?: any, scope?: Scope): any {
    const field = this.config.schema[fieldName]
    if (!field?.parameter) return value

    const context = { scope, row, field }
    return field.parameter(value, row, context)
  }

  /**
   * 获取字段的字典数据
   */
  async getFieldDict(fieldName: string): Promise<any[]> {
    const field = this.config.schema[fieldName]
    if (!field?.dict) return []

    const { dict } = field

    // 如果是函数
    if (isFunction(dict)) {
      return await (dict as Function)()
    }

    // 如果是配置对象
    if (typeof dict === 'object') {
      // 如果有静态数据
      if (dict.data) return dict.data

      // 如果有远程数据源
      if (dict.remote) {
        if (isString(dict.remote)) {
          // TODO: 实现远程数据获取
          console.warn('Remote dict fetching not implemented yet')
          return []
        }
        if (isFunction(dict.remote)) {
          const data = await (dict.remote as Function)()
          return dict.transform ? dict.transform(data) : data
        }
      }
    }

    return []
  }

  /**
   * 生成插槽名称
   */
  generateSlotName(scope: Scope, fieldName?: string, type?: string): string {
    return formatSlotName(scope, fieldName, type)
  }

  /**
   * 获取所有可能的插槽名称
   */
  getAllSlotNames(): string[] {
    const slots: string[] = []
    const scopes: Scope[] = ['search', 'table', 'form', 'info']
    const types = ['title', 'prefix', 'suffix', 'before', 'after']

    // 字段插槽
    for (const fieldName of Object.keys(this.config.schema)) {
      for (const scope of scopes) {
        // 基础字段插槽
        slots.push(this.generateSlotName(scope, fieldName))
        
        // 扩展插槽
        for (const type of types) {
          slots.push(this.generateSlotName(scope, fieldName, type))
        }
      }
    }

    // 区域插槽
    slots.push('search-to-toolbar', 'toolbar-to-table', 'table-to-pager')
    
    // 工具栏插槽
    slots.push('toolbar-buttons-before', 'toolbar-buttons-after')
    slots.push('toolbar-tools-before', 'toolbar-tools-after')
    
    // 表格插槽
    slots.push('table-action-before', 'table-action-after')

    return slots
  }

  /**
   * 验证配置的完整性
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 检查必填配置
    if (!this.config.api?.list) {
      errors.push('api.list is required')
    }

    if (!this.config.schema || Object.keys(this.config.schema).length === 0) {
      errors.push('schema is required and cannot be empty')
    }

    // 检查字段配置
    for (const [fieldName, field] of Object.entries(this.config.schema)) {
      if (!field.type) {
        errors.push(`Field "${fieldName}" is missing type`)
      }
      if (!field.label) {
        errors.push(`Field "${fieldName}" is missing label`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

/**
 * 创建 Schema 转换器实例
 */
export function createSchemaTransformer(config: ViaGridConfig): SchemaTransformer {
  return new SchemaTransformer(config)
}
