/**
 * Via Grid 默认配置
 * 实现"约定优于配置"的设计原则
 */

import { DEFAULT_CONFIG } from '@via-grid/shared'
import type { ViaGridConfig } from './types'

/**
 * 默认的 Via Grid 配置
 */
export const defaultViaGridConfig: Partial<ViaGridConfig> = {
  // ==================== 基础配置 ====================
  title: '',
  height: 'auto',
  loading: false,

  // ==================== 布局配置 ====================
  layout: {
    search: true,
    toolbar: true,
    table: true,
    pager: true
  },

  // ==================== 权限配置 ====================
  permissions: {
    add: true,
    edit: true,
    remove: true,
    info: true,
    import: true,
    export: true,
    batchRemove: true,
    batchUpdate: false
  },

  // ==================== 模块配置 ====================
  form: {
    labelWidth: DEFAULT_CONFIG.FORM.LABEL_WIDTH,
    labelPosition: DEFAULT_CONFIG.FORM.LABEL_POSITION,
    size: DEFAULT_CONFIG.FORM.SIZE,
    disabled: false,
    validateOnRuleChange: true,
    hideRequiredAsterisk: false,
    showMessage: true,
    inlineMessage: false
  },

  dialog: {
    width: DEFAULT_CONFIG.DIALOG.WIDTH,
    fullscreen: false,
    modal: DEFAULT_CONFIG.DIALOG.MODAL,
    lockScroll: DEFAULT_CONFIG.DIALOG.LOCK_SCROLL,
    closeOnClickModal: DEFAULT_CONFIG.DIALOG.CLOSE_ON_CLICK_MODAL,
    closeOnPressEscape: DEFAULT_CONFIG.DIALOG.CLOSE_ON_PRESS_ESCAPE,
    showClose: DEFAULT_CONFIG.DIALOG.SHOW_CLOSE,
    destroyOnClose: DEFAULT_CONFIG.DIALOG.DESTROY_ON_CLOSE
  },

  search: {
    resetToDefaultValue: true,
    submitOnReset: true,
    submitOnEnter: true,
    trimValues: true,
    immediate: true,
    collapsed: false,
    collapseRows: 1,
    labelWidth: '80px',
    itemProps: { labelPosition: 'right' }
  },

  toolbar: {
    buttons: ['add', 'import', 'export'],
    tools: ['refresh', 'fullscreen', 'columns'],
    custom: true,
    import: {
      accept: '.xlsx,.xls,.csv',
      multiple: false,
      showProgress: true,
      autoUpload: true
    },
    export: {
      filename: '数据列表',
      format: 'xlsx',
      includeHeader: true,
      onlySelected: false
    },
    refresh: {
      autoRefresh: false,
      interval: 30000
    }
  },

  table: {
    rowKey: DEFAULT_CONFIG.TABLE.ROW_KEY,
    border: DEFAULT_CONFIG.TABLE.BORDER,
    stripe: DEFAULT_CONFIG.TABLE.STRIPE,
    height: 'auto',
    maxHeight: 600,
    size: DEFAULT_CONFIG.TABLE.SIZE,
    emptyText: '暂无数据',
    defaultSort: { prop: 'createTime', order: 'descending' },
    highlightCurrentRow: true,
    showHeader: true,
    showSummary: false,
    sumText: '合计',
    lazy: false
  },

  pager: {
    pageSize: DEFAULT_CONFIG.PAGER.PAGE_SIZE,
    pageSizes: DEFAULT_CONFIG.PAGER.PAGE_SIZES,
    layout: DEFAULT_CONFIG.PAGER.LAYOUT,
    background: true,
    align: 'right',
    perfect: false,
    pageCount: 7,
    pagerCount: 7,
    autoHidden: false
  },

  data: {
    transformRequest: (params: any) => ({
      ...params,
      page: params.current,
      size: params.pageSize
    }),
    transformResponse: (response: any) => ({
      data: response.data?.records || response.data || [],
      total: response.data?.total || response.total || 0,
      current: response.data?.current || response.current || 1,
      pageSize: response.data?.size || response.pageSize || 10
    }),
    validateData: (data: any) => Array.isArray(data)
  },

  i18n: {
    search: '搜索',
    reset: '重置',
    add: '新增',
    edit: '编辑',
    remove: '删除',
    info: '详情',
    import: '导入',
    export: '导出',
    refresh: '刷新',
    confirm: '确定',
    cancel: '取消'
  }
}

/**
 * 创建配置
 */
export function createConfig(userConfig: Partial<ViaGridConfig>): ViaGridConfig {
  // 验证必填配置
  if (!userConfig.api?.list) {
    throw new Error('api.list is required')
  }

  if (!userConfig.schema || Object.keys(userConfig.schema).length === 0) {
    throw new Error('schema is required and cannot be empty')
  }

  // 合并默认配置和用户配置
  return {
    ...defaultViaGridConfig,
    ...userConfig,
    api: {
      ...userConfig.api
    },
    schema: {
      ...userConfig.schema
    },
    layout: {
      ...defaultViaGridConfig.layout,
      ...userConfig.layout
    },
    permissions: {
      ...defaultViaGridConfig.permissions,
      ...userConfig.permissions
    },
    form: {
      ...defaultViaGridConfig.form,
      ...userConfig.form
    },
    dialog: {
      ...defaultViaGridConfig.dialog,
      ...userConfig.dialog
    },
    search: {
      ...defaultViaGridConfig.search,
      ...userConfig.search
    },
    toolbar: {
      ...defaultViaGridConfig.toolbar,
      ...userConfig.toolbar
    },
    table: {
      ...defaultViaGridConfig.table,
      ...userConfig.table
    },
    pager: {
      ...defaultViaGridConfig.pager,
      ...userConfig.pager
    },
    data: {
      ...defaultViaGridConfig.data,
      ...userConfig.data
    },
    i18n: {
      ...defaultViaGridConfig.i18n,
      ...userConfig.i18n
    }
  } as ViaGridConfig
}

/**
 * 获取字段默认值
 */
export function getFieldDefaultValue(fieldType: string): any {
  switch (fieldType) {
    case 'text':
    case 'textarea':
      return ''
    case 'number':
      return null
    case 'select':
      return null
    case 'checkbox':
      return []
    case 'radio':
      return null
    case 'switch':
      return false
    case 'date':
    case 'datetime':
    case 'time':
      return null
    case 'upload':
      return []
    case 'tag':
      return []
    default:
      return null
  }
}

/**
 * 获取字段验证规则
 */
export function getFieldValidationRules(field: any): any[] {
  const rules: any[] = []

  // 必填规则
  if (field.required) {
    rules.push({
      required: true,
      message: `请输入${field.label}`,
      trigger: ['blur', 'change']
    })
  }

  // 长度规则
  if (field.minLength || field.maxLength) {
    rules.push({
      min: field.minLength,
      max: field.maxLength,
      message: `${field.label}长度在 ${field.minLength || 0} 到 ${field.maxLength || 100} 个字符`,
      trigger: 'blur'
    })
  }

  // 正则规则
  if (field.pattern) {
    rules.push({
      pattern: field.pattern,
      message: field.patternMessage || `${field.label}格式不正确`,
      trigger: 'blur'
    })
  }

  return rules
}
