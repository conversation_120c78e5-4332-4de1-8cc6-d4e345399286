/**
 * Via Grid 核心类型定义
 * 基于 ROADMAP.md 第149-686行的完整 API 设计文档
 */

import type {
  VueVersion,
  UILibrary,
  FieldType,
  Scope,
  Recordable,
  ValidationRule,
  DictConfig,
  DictFunction,
  FormatterFunction,
  ParameterFunction,
  HiddenFunction,
  EventHandler,
  ApiResponse,
  PageResponse,
  PageParams
} from '@via-grid/shared'

// ==================== 字段配置接口 ====================

export interface SearchFieldConfig {
  itemProps?: Recordable
  span?: number
  order?: number
}

export interface TableFieldConfig {
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  columnProps?: Recordable
  cellRender?: Recordable
  headerRender?: Recordable
  footerRender?: Recordable
}

export interface FormFieldConfig {
  span?: number
  offset?: number
  push?: number
  pull?: number
  itemProps?: Recordable
  disabled?: boolean
  readonly?: boolean
}

export interface InfoFieldConfig {
  span?: number
  labelWidth?: string
  colon?: boolean
  itemProps?: Recordable
}

// 字段配置接口
export interface FieldSchema {
  type: FieldType
  label: string
  value?: any
  placeholder?: string
  
  // 字典支持
  dict?: DictConfig | DictFunction
  options?: Array<{ label: string; value: any; [key: string]: any }>
  
  // 验证规则
  rules?: ValidationRule[]
  
  // 显示控制（支持动态函数）
  hidden?: boolean | string[] | HiddenFunction
  
  // 字段属性
  fieldProps?: Recordable
  
  // 数据转换函数
  formatter?: FormatterFunction
  parameter?: ParameterFunction
  
  // 作用域特定配置
  search?: Partial<FieldSchema> & SearchFieldConfig
  table?: Partial<FieldSchema> & TableFieldConfig
  form?: Partial<FieldSchema> & FormFieldConfig
  add?: Partial<FieldSchema> & FormFieldConfig
  edit?: Partial<FieldSchema> & FormFieldConfig
  info?: Partial<FieldSchema> & InfoFieldConfig
}

// ==================== 权限配置 ====================

export interface PermissionConfig {
  // 基础 CRUD 权限
  add?: boolean | ((context?: any) => boolean)
  edit?: boolean | ((context?: any) => boolean)
  remove?: boolean | ((context?: any) => boolean)
  info?: boolean | ((context?: any) => boolean)
  
  // 导入导出权限
  import?: boolean | ((context?: any) => boolean)
  export?: boolean | ((context?: any) => boolean)
  
  // 批量操作权限
  batchRemove?: boolean | ((context?: any) => boolean)
  batchUpdate?: boolean | ((context?: any) => boolean)
  
  // 组件级权限
  'toolbar-add'?: boolean | ((context?: any) => boolean)
  'toolbar-remove'?: boolean | ((context?: any) => boolean)
  'table-edit'?: boolean | ((context?: any) => boolean)
  'table-remove'?: boolean | ((context?: any) => boolean)
  
  // 行级权限（动态）
  'row-edit'?: (row: any, context?: any) => boolean
  'row-remove'?: (row: any, context?: any) => boolean
  
  // 自定义权限
  [key: string]: boolean | ((context?: any) => boolean) | ((row: any, context?: any) => boolean)
}

// ==================== 事件处理器 ====================

export interface EventHandlers {
  // 数据操作事件
  onSearch?: (params: any) => void | Promise<void>
  onAfterSearch?: (result: any) => void | Promise<void>
  onAdd?: (data: any) => any | Promise<any>
  onEdit?: (data: any, row: any) => any | Promise<any>
  onRemove?: (row: any) => boolean | Promise<boolean>
  onInfo?: (row: any) => void | Promise<void>

  // 批量操作事件
  onBatchRemove?: (rows: any[]) => boolean | Promise<boolean>
  onBatchUpdate?: (rows: any[], data: any) => boolean | Promise<boolean>

  // 导入导出事件
  onImport?: (file: File) => void | Promise<void>
  onExport?: (params: any) => void | Promise<void>

  // 表格事件
  onRowClick?: (row: any, column: any, event: Event) => void
  onRowDblclick?: (row: any, column: any, event: Event) => void
  onSelectionChange?: (selection: any[]) => void
  onSortChange?: (params: { column: any; prop: string; order: string }) => void

  // 分页事件
  onPageChange?: (page: number) => void
  onSizeChange?: (size: number) => void

  // 生命周期事件
  onMounted?: (instance: any) => void
  onBeforeDestroy?: (instance: any) => void

  // 自定义事件
  [key: string]: any
}

// ==================== 模块配置接口 ====================

export interface FormConfig {
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'medium' | 'small' | 'mini'
  disabled?: boolean
  validateOnRuleChange?: boolean
  hideRequiredAsterisk?: boolean
  showMessage?: boolean
  inlineMessage?: boolean
  [key: string]: any
}

export interface DialogConfig {
  width?: string | number
  height?: string | number
  fullscreen?: boolean
  modal?: boolean
  lockScroll?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  destroyOnClose?: boolean
  [key: string]: any
}

export interface SearchConfig {
  resetToDefaultValue?: boolean
  submitOnReset?: boolean
  submitOnEnter?: boolean
  trimValues?: boolean
  immediate?: boolean
  collapsed?: boolean
  collapseRows?: number
  labelWidth?: string
  itemProps?: Recordable
  [key: string]: any
}

export interface ToolbarConfig {
  buttons?: string[]
  tools?: string[]
  custom?: boolean
  import?: {
    accept?: string
    multiple?: boolean
    showProgress?: boolean
    autoUpload?: boolean
    [key: string]: any
  }
  export?: {
    filename?: string
    format?: string
    includeHeader?: boolean
    onlySelected?: boolean
    [key: string]: any
  }
  refresh?: {
    autoRefresh?: boolean
    interval?: number
    [key: string]: any
  }
  [key: string]: any
}

export interface TableConfig {
  rowKey?: string
  border?: boolean
  stripe?: boolean
  height?: string | number
  maxHeight?: string | number
  size?: 'large' | 'medium' | 'small' | 'mini'
  emptyText?: string
  defaultSort?: { prop: string; order: 'ascending' | 'descending' }
  highlightCurrentRow?: boolean
  showHeader?: boolean
  showSummary?: boolean
  sumText?: string
  summaryMethod?: (param: any) => any[]
  spanMethod?: (param: any) => number[] | { rowspan: number; colspan: number }
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  [key: string]: any
}

export interface PagerConfig {
  pageSize?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  align?: 'left' | 'center' | 'right'
  perfect?: boolean
  pageCount?: number
  pagerCount?: number
  autoHidden?: boolean
  [key: string]: any
}

export interface DataConfig {
  transformRequest?: (params: any) => any
  transformResponse?: (response: any) => { data: any[]; total: number; current: number; pageSize: number }
  validateData?: (data: any) => boolean
  [key: string]: any
}

export interface I18nConfig {
  search?: string
  reset?: string
  add?: string
  edit?: string
  remove?: string
  info?: string
  import?: string
  export?: string
  refresh?: string
  confirm?: string
  cancel?: string
  [key: string]: any
}

// ==================== 主配置接口 ====================

export interface ViaGridConfig {
  // ==================== 基础配置 ====================
  title?: string                          // 表格标题，默认为空
  height?: number | string                // 表格高度，默认为 'auto'
  loading?: boolean                       // 加载状态，默认为 false

  // ==================== API 配置 ====================
  api: {
    // 基础 CRUD 接口
    list: string | Function               // 列表查询接口（必填）
    add?: string | Function               // 新增接口
    edit?: string | Function              // 编辑接口
    remove?: string | Function            // 删除接口
    info?: string | Function              // 详情接口

    // 导入导出接口
    import?: string | Function            // 导入接口
    export?: string | Function            // 导出接口
    template?: string | Function          // 模板下载接口

    // 批量操作接口
    batchRemove?: string | Function       // 批量删除接口
    batchUpdate?: string | Function       // 批量更新接口
  }

  // ==================== Schema 字段定义 ====================
  schema: Record<string, FieldSchema>

  // ==================== 布局配置 ====================
  // 注意：layout 专注于组件的显示/隐藏和基础布局，具体配置在各自的配置项中
  layout?: {
    search?: boolean                      // 是否显示搜索区域，默认为 true
    toolbar?: boolean                     // 是否显示工具栏，默认为 true
    table?: boolean                       // 是否显示表格，默认为 true
    pager?: boolean                       // 是否显示分页，默认为 true
  }

  // ==================== 权限配置 ====================
  permissions?: PermissionConfig

  // ==================== 排序配置 ====================
  sort?: {
    search?: string[]                     // 搜索字段排序
    table?: string[]                      // 表格列排序
    form?: string[]                       // 表单字段排序
    add?: string[]                        // 新增表单字段排序
    edit?: string[]                       // 编辑表单字段排序
    info?: string[]                       // 详情字段排序
  }

  // ==================== 事件处理 ====================
  events?: EventHandlers

  // ==================== 模块配置 ====================
  // 约定优于配置：所有配置项都有合理的默认值，可按需覆盖
  form?: FormConfig                       // 表单配置
  dialog?: DialogConfig                   // 弹窗配置
  search?: SearchConfig                   // 搜索配置
  toolbar?: ToolbarConfig                 // 工具栏配置
  table?: TableConfig                     // 表格配置
  pager?: PagerConfig                     // 分页配置
  data?: DataConfig                       // 数据处理配置
  i18n?: I18nConfig                       // 国际化配置
}

// ==================== 组件实例接口 ====================

export interface ViaGridInstance {
  // 数据操作方法
  refresh(): Promise<void>
  search(params?: any): Promise<void>
  resetSearch(): void
  clearSelection(): void
  toggleRowSelection(row: any, selected?: boolean): void

  // 表单操作方法
  openAddDialog(): void
  openEditDialog(row: any): void
  openInfoDialog(row: any): void
  closeDialog(): void

  // 数据获取方法
  getTableData(): any[]
  getSelection(): any[]
  getSearchParams(): any

  // 表格操作方法
  setCurrentRow(row: any): void
  toggleRowExpansion(row: any, expanded?: boolean): void
  clearSort(): void
  doLayout(): void

  // 内部属性
  $el: HTMLElement
  [key: string]: any
}
