{"name": "@via-grid/core", "version": "0.0.0", "description": "Via Grid 核心逻辑和类型定义", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@via-grid/shared": "workspace:*", "es-toolkit": "^1.28.1"}, "keywords": ["via-grid", "core", "types"], "author": "ViaGrid Team", "license": "MIT", "devDependencies": {"vite": "^5.0.12", "vite-plugin-dts": "^3.7.2"}}