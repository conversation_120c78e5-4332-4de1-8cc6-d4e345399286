{"name": "@via-grid/shared", "version": "0.0.0", "description": "Via Grid 共享工具和常量", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["via-grid", "shared", "utils"], "author": "ViaGrid Team", "license": "MIT", "dependencies": {"es-toolkit": "^1.28.1"}, "devDependencies": {"vite": "^5.0.12", "vite-plugin-dts": "^3.7.2"}}