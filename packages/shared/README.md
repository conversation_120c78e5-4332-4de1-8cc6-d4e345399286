# @via-grid/shared

Via Grid 共享工具和常量包。

## 📦 功能

- 🛠️ **工具函数**: 基于 es-toolkit 的高性能工具函数
- 📋 **常量定义**: 项目中使用的常量和枚举
- 🔧 **类型定义**: 共享的 TypeScript 类型定义

## 🚀 特色

### 基于 es-toolkit

本包使用 [es-toolkit](https://es-toolkit.slash.page/) 作为底层工具库，提供：

- ⚡ **高性能**: 比 lodash 更快的执行速度
- 🌳 **Tree-shaking 友好**: 更小的打包体积
- 📝 **TypeScript 原生**: 完整的类型支持
- 🔒 **类型安全**: 严格的类型检查

### 直接使用 es-toolkit

本包直接导出 es-toolkit 的函数，避免不必要的中间层：

```typescript
// 直接从 es-toolkit 导入
import {
  isEmpty,
  isNotEmpty,
  merge,
  isObject,
  isFunction,
  isString,
  isArray,
  isPromise,
  debounce,
  throttle,
  cloneDeep
} from 'es-toolkit'

// 从 @via-grid/shared 导入自定义工具函数
import {
  generateId,
  detectVueVersion,
  detectUILibrary,
  formatSlotName,
  parseSlotName
} from '@via-grid/shared'
```

### 推荐使用方式

```typescript
// 直接从 es-toolkit 导入常用工具函数
import { isEmpty, isObject, merge, cloneDeep } from 'es-toolkit'

// 从 @via-grid/shared 导入项目特定的工具函数
import { generateId, formatSlotName } from '@via-grid/shared'

// 检查空值
isEmpty(null) // true
isEmpty('') // true
isEmpty([]) // true

// 深度合并（注意：es-toolkit 中是 merge，不是 deepMerge）
const merged = merge(
  { a: 1, b: { c: 2 } },
  { b: { d: 3 }, e: 4 }
)
// { a: 1, b: { c: 2, d: 3 }, e: 4 }

// 类型检查
isObject({}) // true

// 深度克隆
const original = { a: 1, b: { c: 2 } }
const cloned = cloneDeep(original)

// 项目特定工具
const id = generateId('custom') // custom-1234567890-abc123def
const slotName = formatSlotName('table', 'name') // table-name
```

## 🔧 常量

```typescript
import { FIELD_TYPES, UI_LIBRARIES, DEFAULT_CONFIG } from '@via-grid/shared'

// 字段类型
FIELD_TYPES.TEXT // 'text'
FIELD_TYPES.SELECT // 'select'

// UI 库类型
UI_LIBRARIES.ELEMENT_PLUS // 'element-plus'
UI_LIBRARIES.ANT_DESIGN_VUE // 'ant-design-vue'

// 默认配置
DEFAULT_CONFIG.PAGER.PAGE_SIZE // 10
DEFAULT_CONFIG.TABLE.BORDER // true
```

## 🧪 测试

```bash
# 运行测试
pnpm test

# 运行特定测试
pnpm test utils.test.ts
```

## 📄 许可证

[MIT](../../LICENSE) © 2025 ViaGrid
