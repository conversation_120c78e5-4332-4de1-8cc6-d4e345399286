/**
 * Via Grid 共享类型定义
 */

import type { VUE_VERSIONS, UI_LIBRARIES, FIELD_TYPES, SCOPES } from './constants'

// 基础类型
export type VueVersion = typeof VUE_VERSIONS[keyof typeof VUE_VERSIONS]
export type UILibrary = typeof UI_LIBRARIES[keyof typeof UI_LIBRARIES]
export type FieldType = typeof FIELD_TYPES[keyof typeof FIELD_TYPES] | string
export type Scope = typeof SCOPES[keyof typeof SCOPES]

// 通用工具类型
export type Recordable<T = any> = Record<string, T>
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 函数类型
export type AnyFunction = (...args: any[]) => any
export type VoidFunction = () => void
export type PromiseFunction<T = any> = (...args: any[]) => Promise<T>

// 组件实例类型
export interface ComponentInstance {
  $el: HTMLElement
  [key: string]: any
}

// 事件处理器类型
export type EventHandler<T = any> = (event: T) => void | boolean | Promise<void | boolean>

// 验证规则类型
export interface ValidationRule {
  required?: boolean
  message?: string
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: (error?: Error) => void) => void
  trigger?: string | string[]
  type?: string
}

// 字典项类型
export interface DictItem {
  label: string
  value: any
  disabled?: boolean
  children?: DictItem[]
  [key: string]: any
}

// 字典配置类型
export interface DictConfig {
  data?: DictItem[]
  remote?: string | AnyFunction
  labelKey?: string
  valueKey?: string
  cache?: boolean
  transform?: (data: any) => DictItem[]
}

// 字典函数类型
export type DictFunction = (context?: any) => DictItem[] | Promise<DictItem[]>

// 格式化函数类型
export type FormatterFunction = (value: any, row?: any, context?: any) => any

// 参数转换函数类型
export type ParameterFunction = (value: any, row?: any, context?: any) => any

// 隐藏函数上下文
export interface HiddenContext {
  scope: Scope
  row?: any
  mode?: 'add' | 'edit' | 'info'
  formData?: any
  user?: any
  [key: string]: any
}

// 隐藏函数类型
export type HiddenFunction = (context: HiddenContext) => boolean

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页响应类型
export interface PageResponse<T = any> {
  records: T[]
  total: number
  current: number
  size: number
  pages?: number
}

// 分页参数类型
export interface PageParams {
  current: number
  pageSize: number
  [key: string]: any
}
