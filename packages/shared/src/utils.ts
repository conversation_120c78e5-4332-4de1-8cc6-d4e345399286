/**
 * Via Grid 共享工具函数
 */

import type { VueVersion, UILibrary } from './types'

// 直接导出 es-toolkit 函数，避免不必要的映射
export {
  isEmpty,
  isNotEmpty,
  merge as deepMerge,
  isObject,
  isFunction,
  isString,
  isArray,
  isPromise,
  debounce,
  throttle,
  cloneDeep
} from 'es-toolkit'

/**
 * 生成唯一 ID
 */
export function generateId(prefix = 'via-grid'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
}

/**
 * 检测 Vue 版本
 */
export function detectVueVersion(): VueVersion {
  // 在运行时检测 Vue 版本
  if (typeof window !== 'undefined') {
    // @ts-ignore
    const Vue = window.Vue
    if (Vue) {
      // Vue 3 有 createApp 方法
      if (Vue.createApp) return 'vue3'
      // Vue 2 有 version 属性且以 2 开头
      if (Vue.version && Vue.version.startsWith('2')) return 'vue2'
    }
  }
  
  // 默认返回 Vue 3
  return 'vue3'
}

/**
 * 检测 UI 库类型
 */
export function detectUILibrary(): UILibrary | null {
  if (typeof window !== 'undefined') {
    // @ts-ignore
    if (window.ElementPlus) return 'element-plus'
    // @ts-ignore
    if (window.ELEMENT) return 'element-ui'
    // @ts-ignore
    if (window.antd) return 'ant-design-vue'
    // @ts-ignore
    if (window.naive) return 'naive-ui'
  }
  
  return null
}

/**
 * 格式化插槽名称
 * 确保使用连字符分隔符
 */
export function formatSlotName(scope: string, field?: string, type?: string): string {
  const parts = [scope]
  if (field) parts.push(field)
  if (type) parts.push(type)
  return parts.join('-')
}

/**
 * 解析插槽名称
 */
export function parseSlotName(slotName: string): { scope: string; field?: string; type?: string } {
  const parts = slotName.split('-')
  return {
    scope: parts[0],
    field: parts[1],
    type: parts[2]
  }
}


