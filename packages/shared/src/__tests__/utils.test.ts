/**
 * Via Grid 共享工具函数测试
 * 验证 es-toolkit 集成是否正常工作
 */

import { describe, it, expect } from 'vitest'
import {
  isEmpty,
  isNotEmpty,
  merge as deepMerge,
  isObject,
  isFunction,
  isString,
  isArray,
  isPromise,
  debounce,
  throttle,
  cloneDeep
} from 'es-toolkit'
import {
  generateId,
  formatSlotName,
  parseSlotName
} from '../utils'

describe('Via Grid Utils', () => {
  describe('isEmpty & isNotEmpty', () => {
    it('should correctly identify empty values', () => {
      expect(isEmpty(null)).toBe(true)
      expect(isEmpty(undefined)).toBe(true)
      expect(isEmpty('')).toBe(true)
      expect(isEmpty([])).toBe(true)
      expect(isEmpty({})).toBe(true)
      
      expect(isEmpty('hello')).toBe(false)
      expect(isEmpty([1, 2, 3])).toBe(false)
      expect(isEmpty({ a: 1 })).toBe(false)
    })

    it('should correctly identify non-empty values', () => {
      expect(isNotEmpty('hello')).toBe(true)
      expect(isNotEmpty([1, 2, 3])).toBe(true)
      expect(isNotEmpty({ a: 1 })).toBe(true)
      
      expect(isNotEmpty(null)).toBe(false)
      expect(isNotEmpty(undefined)).toBe(false)
      expect(isNotEmpty('')).toBe(false)
    })
  })

  describe('deepMerge', () => {
    it('should deeply merge objects', () => {
      const target = { a: 1, b: { c: 2 } }
      const source = { b: { d: 3 }, e: 4 }
      const result = deepMerge(target, source)
      
      expect(result).toEqual({
        a: 1,
        b: { c: 2, d: 3 },
        e: 4
      })
    })
  })

  describe('type checking functions', () => {
    it('should correctly identify objects', () => {
      expect(isObject({})).toBe(true)
      expect(isObject({ a: 1 })).toBe(true)
      expect(isObject([])).toBe(false)
      expect(isObject(null)).toBe(false)
      expect(isObject('string')).toBe(false)
    })

    it('should correctly identify functions', () => {
      expect(isFunction(() => {})).toBe(true)
      expect(isFunction(function() {})).toBe(true)
      expect(isFunction('string')).toBe(false)
      expect(isFunction({})).toBe(false)
    })

    it('should correctly identify strings', () => {
      expect(isString('hello')).toBe(true)
      expect(isString('')).toBe(true)
      expect(isString(123)).toBe(false)
      expect(isString({})).toBe(false)
    })

    it('should correctly identify arrays', () => {
      expect(isArray([])).toBe(true)
      expect(isArray([1, 2, 3])).toBe(true)
      expect(isArray({})).toBe(false)
      expect(isArray('string')).toBe(false)
    })

    it('should correctly identify promises', () => {
      expect(isPromise(Promise.resolve())).toBe(true)
      expect(isPromise({ then: () => {} })).toBe(true)
      expect(isPromise({})).toBe(false)
      expect(isPromise('string')).toBe(false)
    })
  })

  describe('cloneDeep', () => {
    it('should deeply clone objects', () => {
      const original = { a: 1, b: { c: 2 }, d: [3, 4] }
      const cloned = cloneDeep(original)
      
      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.b).not.toBe(original.b)
      expect(cloned.d).not.toBe(original.d)
    })
  })

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId()
      const id2 = generateId()
      
      expect(id1).not.toBe(id2)
      expect(id1).toMatch(/^via-grid-\d+-[a-z0-9]+$/)
    })

    it('should use custom prefix', () => {
      const id = generateId('custom')
      expect(id).toMatch(/^custom-\d+-[a-z0-9]+$/)
    })
  })

  describe('slot name utilities', () => {
    it('should format slot names correctly', () => {
      expect(formatSlotName('table', 'name')).toBe('table-name')
      expect(formatSlotName('search', 'status', 'title')).toBe('search-status-title')
      expect(formatSlotName('form')).toBe('form')
    })

    it('should parse slot names correctly', () => {
      expect(parseSlotName('table-name')).toEqual({
        scope: 'table',
        field: 'name',
        type: undefined
      })
      
      expect(parseSlotName('search-status-title')).toEqual({
        scope: 'search',
        field: 'status',
        type: 'title'
      })
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', async () => {
      let count = 0
      const increment = () => count++
      const debouncedIncrement = debounce(increment, 100)
      
      debouncedIncrement()
      debouncedIncrement()
      debouncedIncrement()
      
      expect(count).toBe(0)
      
      await new Promise(resolve => setTimeout(resolve, 150))
      expect(count).toBe(1)
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', async () => {
      let count = 0
      const increment = () => count++
      const throttledIncrement = throttle(increment, 100)
      
      throttledIncrement()
      throttledIncrement()
      throttledIncrement()
      
      expect(count).toBe(1)
      
      await new Promise(resolve => setTimeout(resolve, 150))
      throttledIncrement()
      expect(count).toBe(2)
    })
  })
})
