/**
 * Via Grid 常量定义
 */

// 版本信息
export const VUE_VERSIONS = {
  VUE2: 'vue2',
  VUE3: 'vue3'
} as const

// UI 库类型
export const UI_LIBRARIES = {
  ELEMENT_UI: 'element-ui',
  ELEMENT_PLUS: 'element-plus',
  ANT_DESIGN_VUE: 'ant-design-vue',
  NAIVE_UI: 'naive-ui'
} as const

// 字段类型
export const FIELD_TYPES = {
  TEXT: 'text',
  NUMBER: 'number',
  SELECT: 'select',
  DATE: 'date',
  SWITCH: 'switch',
  UPLOAD: 'upload',
  TAG: 'tag',
  RADIO: 'radio',
  CHECKBOX: 'checkbox'
} as const

// 作用域类型
export const SCOPES = {
  SEARCH: 'search',
  TABLE: 'table',
  FORM: 'form',
  ADD: 'add',
  EDIT: 'edit',
  INFO: 'info'
} as const

// 插槽类型
export const SLOT_TYPES = {
  TITLE: 'title',
  PREFIX: 'prefix',
  SUFFIX: 'suffix',
  BEFORE: 'before',
  AFTER: 'after'
} as const

// 默认配置
export const DEFAULT_CONFIG = {
  // 分页配置
  PAGER: {
    PAGE_SIZE: 10,
    PAGE_SIZES: [10, 20, 50, 100],
    LAYOUT: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 表格配置
  TABLE: {
    ROW_KEY: 'id',
    BORDER: true,
    STRIPE: true,
    SIZE: 'medium'
  },
  
  // 表单配置
  FORM: {
    LABEL_WIDTH: '100px',
    LABEL_POSITION: 'right',
    SIZE: 'medium'
  },
  
  // 弹窗配置
  DIALOG: {
    WIDTH: '800px',
    MODAL: true,
    LOCK_SCROLL: true,
    CLOSE_ON_CLICK_MODAL: false,
    CLOSE_ON_PRESS_ESCAPE: true,
    SHOW_CLOSE: true,
    DESTROY_ON_CLOSE: true
  }
} as const

// 事件名称
export const EVENTS = {
  // 数据操作事件
  SEARCH: 'search',
  AFTER_SEARCH: 'afterSearch',
  ADD: 'add',
  EDIT: 'edit',
  REMOVE: 'remove',
  INFO: 'info',
  
  // 批量操作事件
  BATCH_REMOVE: 'batchRemove',
  BATCH_UPDATE: 'batchUpdate',
  
  // 导入导出事件
  IMPORT: 'import',
  EXPORT: 'export',
  
  // 表格事件
  ROW_CLICK: 'rowClick',
  ROW_DBLCLICK: 'rowDblclick',
  SELECTION_CHANGE: 'selectionChange',
  SORT_CHANGE: 'sortChange',
  
  // 分页事件
  PAGE_CHANGE: 'pageChange',
  SIZE_CHANGE: 'sizeChange',
  
  // 生命周期事件
  MOUNTED: 'mounted',
  BEFORE_DESTROY: 'beforeDestroy'
} as const

// 权限类型
export const PERMISSIONS = {
  ADD: 'add',
  EDIT: 'edit',
  REMOVE: 'remove',
  INFO: 'info',
  IMPORT: 'import',
  EXPORT: 'export',
  BATCH_REMOVE: 'batchRemove',
  BATCH_UPDATE: 'batchUpdate'
} as const
