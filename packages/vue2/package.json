{"name": "@via-grid/vue2", "version": "0.0.0", "description": "Via Grid Vue2 实现包", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@via-grid/core": "workspace:*", "@via-grid/shared": "workspace:*"}, "peerDependencies": {"vue": "^2.6.0", "vxe-table": "^3.0.0"}, "peerDependenciesMeta": {"@vxe-ui/plugin-render-element": {"optional": true}, "@vxe-ui/plugin-render-antd": {"optional": true}, "element-ui": {"optional": true}, "ant-design-vue": {"optional": true}}, "keywords": ["via-grid", "vue2", "vxe-table", "element-ui", "ant-design-vue"], "author": "ViaGrid Team", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue2": "^2.3.1", "vite": "^5.0.12", "vite-plugin-dts": "^3.7.2", "vue": "^2.7.16"}}