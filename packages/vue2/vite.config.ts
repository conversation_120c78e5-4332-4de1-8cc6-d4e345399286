import { defineConfig } from 'vite'
import { createVuePlugin } from 'vite-plugin-vue2'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    createVuePlugin(),
    dts({
      insertTypesEntry: true,
      rollupTypes: true
    })
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'ViaGridVue2',
      formats: ['es', 'cjs'],
      fileName: (format) => `index.${format === 'es' ? 'js' : 'cjs'}`
    },
    rollupOptions: {
      external: [
        'vue',
        'vxe-table',
        '@via-grid/core',
        '@via-grid/shared',
        '@vxe-ui/plugin-render-element',
        '@vxe-ui/plugin-render-antd',
        'element-ui',
        'ant-design-vue'
      ],
      output: {
        globals: {
          'vue': 'Vue',
          'vxe-table': 'VXETable',
          '@via-grid/core': 'ViaGridCore',
          '@via-grid/shared': 'ViaGridShared'
        }
      }
    }
  }
})
