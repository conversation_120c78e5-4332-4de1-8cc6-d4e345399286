{"name": "via-grid-playground-vue3", "version": "0.0.0", "description": "Via Grid Vue3 开发调试环境", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@via-grid/core": "workspace:*", "@via-grid/shared": "workspace:*", "@via-grid/vue3": "workspace:*", "vue": "^3.4.15", "vxe-table": "^4.6.0", "element-plus": "^2.5.0", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "vite": "^5.0.12", "typescript": "^5.3.3"}}