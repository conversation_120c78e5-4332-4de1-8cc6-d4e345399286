# Via Grid Vue3 Playground

这是 Via Grid Vue3 版本的开发调试环境，用于测试和演示组件功能。

## 🚀 快速开始

### 安装依赖

```bash
# 在项目根目录安装所有依赖
pnpm install

# 或者只安装 playground 依赖
cd playground/vue3
pnpm install
```

### 启动开发服务器

```bash
# 在 playground/vue3 目录下
pnpm dev
```

访问 http://localhost:3000 查看示例。

## 📦 示例内容

### 1. 基础表格示例 (BasicExample.vue)
- ViaTable 组件的基础用法
- 自定义列插槽
- 表格操作事件处理
- 动态数据加载

### 2. 完整网格示例 (GridExample.vue)
- ViaGrid 组件的完整功能
- 搜索、表格、分页、表单集成
- 自定义字段渲染
- API 集成示例

### 3. 表单示例 (FormExample.vue)
- ViaForm 组件的配置式 API 用法
- 自定义字段组件
- 表单验证和提交
- 模态框和抽屉模式

### 4. 工具栏示例 (ToolbarExample.vue)
- ViaToolbar 组件基于 vxe-toolbar 的用法
- 左右侧按钮配置
- 自定义按钮和工具
- 与表格的集成

### 5. 配置式 API 示例 (ConfigExample.vue)
- vxe-form 的 items 配置式 API
- vxe-toolbar 的插槽配置
- 表单字段联动
- 动态表单生成

## 🔧 技术栈

- **Vue 3.4+** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **Element Plus** - Vue 3 UI 组件库
- **VXE Table 4.x** - 功能强大的表格组件
- **VXE UI** - VXE 系列 UI 组件

## 📁 目录结构

```
playground/vue3/
├── src/
│   ├── examples/          # 示例组件
│   │   ├── BasicExample.vue
│   │   ├── GridExample.vue
│   │   ├── FormExample.vue
│   │   ├── ToolbarExample.vue
│   │   └── ConfigExample.vue
│   ├── App.vue           # 主应用组件
│   └── main.ts           # 应用入口
├── index.html            # HTML 模板
├── vite.config.ts        # Vite 配置
├── package.json          # 依赖配置
└── README.md            # 说明文档
```

## 🎯 重构亮点

### ViaForm 重构
- ✅ 从基础的 `vxe-form-item` 改为 `items` 配置式 API
- ✅ 充分利用 vxe-form 的设计初衷和能力
- ✅ 支持字段联动和动态配置
- ✅ 保持插槽系统的兼容性

### ViaToolbar 重构
- ✅ 基于 `vxe-toolbar` 而不是重新封装
- ✅ 利用 `#buttons` 和 `#tools` 插槽
- ✅ 支持 vxe-toolbar 的原生功能
- ✅ 增强与 vxe-table 的集成

### 配置式 API 优势
- **更少的代码**: 利用 vxe 的配置式 API 减少模板代码
- **更强的能力**: 充分利用 vxe 的内置功能
- **更好的性能**: 减少不必要的组件嵌套
- **更易维护**: 遵循 vxe 的设计模式

## 🔗 相关链接

- [Via Grid 项目文档](../../README.md)
- [VXE Table 官方文档](https://vxetable.cn/)
- [Element Plus 官方文档](https://element-plus.org/)
- [Vue 3 官方文档](https://vuejs.org/)

## 🤝 贡献指南

1. 在 playground 中测试新功能
2. 确保所有示例正常运行
3. 添加必要的文档和注释
4. 提交 PR 前进行充分测试

## 📝 开发说明

这个 playground 是为了验证重构后的组件功能而创建的，特别是：

1. **vxe-form 配置式 API** 的正确使用
2. **vxe-toolbar 配置式 API** 的集成效果
3. **插槽系统** 的兼容性
4. **性能优化** 的效果

通过这些示例，可以直观地看到重构带来的改进和优势。
