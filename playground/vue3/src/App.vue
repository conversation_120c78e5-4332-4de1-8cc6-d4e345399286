<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>Via Grid Vue3 Playground</h1>
        <p>基于 vxe-table 的现代化表格组件库 - Vue3 版本</p>
      </el-header>
      
      <el-main>
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="基础表格" name="basic">
            <BasicExample />
          </el-tab-pane>
          
          <el-tab-pane label="完整网格" name="grid">
            <GridExample />
          </el-tab-pane>
          
          <el-tab-pane label="表单示例" name="form">
            <FormExample />
          </el-tab-pane>
          
          <el-tab-pane label="工具栏示例" name="toolbar">
            <ToolbarExample />
          </el-tab-pane>
          
          <el-tab-pane label="配置式API" name="config">
            <ConfigExample />
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BasicExample from './examples/BasicExample.vue'
import GridExample from './examples/GridExample.vue'
import FormExample from './examples/FormExample.vue'
import ToolbarExample from './examples/ToolbarExample.vue'
import ConfigExample from './examples/ConfigExample.vue'

const activeTab = ref('basic')
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #409eff;
  color: white;
  text-align: center;
  line-height: 60px;
}

.el-header h1 {
  margin: 0;
  font-size: 24px;
}

.el-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.el-main {
  padding: 20px;
}
</style>
