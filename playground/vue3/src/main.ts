import { createApp } from 'vue'
import App from './App.vue'

// 引入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 引入 VXE Table
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

// 引入 VXE UI
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'

// 引入 Via Grid
import ViaGrid from '@via-grid/vue3'

const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(ElementPlus)
app.use(VxeUI)
app.use(VxeTable)
app.use(ViaGrid)

app.mount('#app')
