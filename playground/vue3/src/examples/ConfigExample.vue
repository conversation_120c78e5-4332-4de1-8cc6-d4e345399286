<template>
  <div class="config-example">
    <h2>配置式 API 示例</h2>
    <p>展示 vxe-form 和 vxe-toolbar 配置式 API 的强大能力</p>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="example-section">
          <h3>vxe-form 配置式表单</h3>
          <vxe-form v-bind="formOptions">
            <template #action>
              <vxe-button type="reset">重置</vxe-button>
              <vxe-button type="submit" status="primary">提交</vxe-button>
            </template>
          </vxe-form>
        </div>
      </el-col>
      
      <el-col :span="12">
        <div class="example-section">
          <h3>表单配置</h3>
          <el-card>
            <pre>{{ JSON.stringify(formOptions, null, 2) }}</pre>
          </el-card>
        </div>
      </el-col>
    </el-row>
    
    <div class="example-section">
      <h3>vxe-toolbar 配置式工具栏</h3>
      <vxe-toolbar>
        <template #buttons>
          <vxe-button status="primary" @click="addUser">新增用户</vxe-button>
          <vxe-button @click="editUser" :disabled="!selectedUser">编辑用户</vxe-button>
          <vxe-button status="danger" @click="deleteUser" :disabled="!selectedUser">删除用户</vxe-button>
        </template>
        
        <template #tools>
          <vxe-button icon="vxe-icon-refresh" @click="refreshData" content="刷新"></vxe-button>
          <vxe-button icon="vxe-icon-search" @click="toggleSearch" content="搜索"></vxe-button>
          <vxe-button icon="vxe-icon-download" @click="exportData" content="导出"></vxe-button>
        </template>
      </vxe-toolbar>
    </div>
    
    <div class="example-section">
      <h3>表单联动示例</h3>
      <vxe-form v-bind="linkedFormOptions">
        <template #action>
          <vxe-button @click="resetLinkedForm">重置</vxe-button>
          <vxe-button status="primary" @click="submitLinkedForm">提交</vxe-button>
        </template>
      </vxe-form>
    </div>
    
    <div class="example-section">
      <h3>动态表单示例</h3>
      <el-space style="margin-bottom: 15px;">
        <el-button @click="addFormField">添加字段</el-button>
        <el-button @click="removeFormField">移除字段</el-button>
        <el-button @click="toggleFieldRequired">切换必填</el-button>
      </el-space>
      
      <vxe-form v-bind="dynamicFormOptions">
        <template #action>
          <vxe-button type="reset">重置</vxe-button>
          <vxe-button type="submit" status="primary">提交</vxe-button>
        </template>
      </vxe-form>
    </div>
    
    <div class="example-section">
      <h3>表单数据</h3>
      <el-tabs>
        <el-tab-pane label="基础表单数据">
          <pre>{{ JSON.stringify(formOptions.data, null, 2) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="联动表单数据">
          <pre>{{ JSON.stringify(linkedFormOptions.data, null, 2) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="动态表单数据">
          <pre>{{ JSON.stringify(dynamicFormOptions.data, null, 2) }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import XEUtils from 'xe-utils'

const selectedUser = ref(false)

// 基础表单配置
const formOptions = reactive({
  titleWidth: 120,
  titleAlign: 'right',
  data: {
    name: 'test1',
    email: '',
    age: null,
    gender: '',
    city: '',
    skills: [],
    bio: ''
  },
  items: [
    { 
      field: 'name', 
      title: '姓名', 
      span: 12, 
      itemRender: { 
        name: 'VxeInput',
        props: { placeholder: '请输入姓名' }
      } 
    },
    { 
      field: 'email', 
      title: '邮箱', 
      span: 12, 
      itemRender: { 
        name: 'VxeInput',
        props: { placeholder: '请输入邮箱' }
      } 
    },
    { 
      field: 'age', 
      title: '年龄', 
      span: 12, 
      itemRender: { 
        name: 'VxeNumberInput',
        props: { min: 1, max: 120 }
      } 
    },
    { 
      field: 'gender', 
      title: '性别', 
      span: 12, 
      itemRender: {
        name: 'VxeSelect',
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      }
    },
    { 
      field: 'city', 
      title: '城市', 
      span: 12, 
      itemRender: {
        name: 'VxeSelect',
        options: [
          { label: '北京', value: 'beijing' },
          { label: '上海', value: 'shanghai' },
          { label: '广州', value: 'guangzhou' },
          { label: '深圳', value: 'shenzhen' }
        ]
      }
    },
    { 
      field: 'skills', 
      title: '技能', 
      span: 12, 
      itemRender: {
        name: 'VxeSelect',
        props: { multiple: true },
        options: [
          { label: 'JavaScript', value: 'js' },
          { label: 'TypeScript', value: 'ts' },
          { label: 'Vue.js', value: 'vue' },
          { label: 'React', value: 'react' }
        ]
      }
    },
    { 
      field: 'bio', 
      title: '简介', 
      span: 24, 
      itemRender: { 
        name: 'VxeTextarea',
        props: { 
          placeholder: '请输入个人简介',
          rows: 3
        }
      } 
    },
    { 
      align: 'center', 
      span: 24, 
      slots: { default: 'action' } 
    }
  ]
})

// 联动表单配置
const countryItemRender = reactive({
  name: 'VxeSelect',
  options: [
    { label: '中国', value: 'china' },
    { label: '美国', value: 'usa' },
    { label: '日本', value: 'japan' }
  ],
  events: {
    change ({ data }: any) {
      // 根据国家选择更新城市选项
      const cityOptions = getCityOptions(data.country)
      cityItemRender.options = cityOptions
      data.city = '' // 重置城市选择
    }
  }
})

const cityItemRender = reactive({
  name: 'VxeSelect',
  options: []
})

const getCityOptions = (country: string) => {
  const cityMap: Record<string, any[]> = {
    china: [
      { label: '北京', value: 'beijing' },
      { label: '上海', value: 'shanghai' },
      { label: '广州', value: 'guangzhou' }
    ],
    usa: [
      { label: '纽约', value: 'newyork' },
      { label: '洛杉矶', value: 'losangeles' },
      { label: '芝加哥', value: 'chicago' }
    ],
    japan: [
      { label: '东京', value: 'tokyo' },
      { label: '大阪', value: 'osaka' },
      { label: '京都', value: 'kyoto' }
    ]
  }
  return cityMap[country] || []
}

const linkedFormOptions = reactive({
  titleWidth: 120,
  titleAlign: 'right',
  data: {
    country: '',
    city: '',
    startTime: '',
    endTime: ''
  },
  items: [
    { 
      field: 'country', 
      title: '国家', 
      span: 12, 
      itemRender: countryItemRender
    },
    { 
      field: 'city', 
      title: '城市', 
      span: 12, 
      itemRender: cityItemRender
    },
    { 
      field: 'startTime', 
      title: '开始时间', 
      span: 12, 
      itemRender: {
        name: 'VxeDatePicker',
        props: {
          disabledMethod ({ date }: any) {
            const endTime = linkedFormOptions.data.endTime
            if (endTime) {
              const eDate = XEUtils.toStringDate(endTime)
              return date >= eDate
            }
            return false
          }
        }
      }
    },
    { 
      field: 'endTime', 
      title: '结束时间', 
      span: 12, 
      itemRender: {
        name: 'VxeDatePicker',
        props: {
          disabledMethod ({ date }: any) {
            const startTime = linkedFormOptions.data.startTime
            if (startTime) {
              const sDate = XEUtils.toStringDate(startTime)
              return date <= sDate
            }
            return false
          }
        }
      }
    },
    { 
      align: 'center', 
      span: 24, 
      slots: { default: 'action' } 
    }
  ]
})

// 动态表单配置
const dynamicFormOptions = reactive({
  titleWidth: 120,
  titleAlign: 'right',
  data: {
    field1: '',
    field2: ''
  },
  items: [
    { 
      field: 'field1', 
      title: '字段1', 
      span: 12, 
      itemRender: { 
        name: 'VxeInput',
        props: { placeholder: '请输入字段1' }
      } 
    },
    { 
      field: 'field2', 
      title: '字段2', 
      span: 12, 
      itemRender: { 
        name: 'VxeInput',
        props: { placeholder: '请输入字段2' }
      } 
    },
    { 
      align: 'center', 
      span: 24, 
      slots: { default: 'action' } 
    }
  ]
})

// 工具栏事件处理
const addUser = () => {
  ElMessage.success('新增用户')
}

const editUser = () => {
  ElMessage.success('编辑用户')
}

const deleteUser = () => {
  ElMessage.success('删除用户')
}

const refreshData = () => {
  ElMessage.success('刷新数据')
}

const toggleSearch = () => {
  ElMessage.success('切换搜索')
}

const exportData = () => {
  ElMessage.success('导出数据')
}

// 联动表单事件
const resetLinkedForm = () => {
  Object.assign(linkedFormOptions.data, {
    country: '',
    city: '',
    startTime: '',
    endTime: ''
  })
  cityItemRender.options = []
}

const submitLinkedForm = () => {
  console.log('联动表单数据:', linkedFormOptions.data)
  ElMessage.success('提交成功')
}

// 动态表单事件
let fieldCounter = 2

const addFormField = () => {
  fieldCounter++
  const fieldName = `field${fieldCounter}`
  
  // 添加数据字段
  dynamicFormOptions.data[fieldName] = ''
  
  // 添加表单项（在最后一个 action 项之前插入）
  const actionIndex = dynamicFormOptions.items.findIndex(item => item.slots?.default === 'action')
  dynamicFormOptions.items.splice(actionIndex, 0, {
    field: fieldName,
    title: `字段${fieldCounter}`,
    span: 12,
    itemRender: {
      name: 'VxeInput',
      props: { placeholder: `请输入字段${fieldCounter}` }
    }
  })
}

const removeFormField = () => {
  if (fieldCounter <= 2) {
    ElMessage.warning('至少保留两个字段')
    return
  }
  
  const fieldName = `field${fieldCounter}`
  
  // 删除数据字段
  delete dynamicFormOptions.data[fieldName]
  
  // 删除表单项
  const fieldIndex = dynamicFormOptions.items.findIndex(item => item.field === fieldName)
  if (fieldIndex > -1) {
    dynamicFormOptions.items.splice(fieldIndex, 1)
  }
  
  fieldCounter--
}

const toggleFieldRequired = () => {
  const firstField = dynamicFormOptions.items[0]
  if (firstField) {
    firstField.required = !firstField.required
    ElMessage.success(`字段1 ${firstField.required ? '设为必填' : '取消必填'}`)
  }
}
</script>

<style scoped>
.config-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
