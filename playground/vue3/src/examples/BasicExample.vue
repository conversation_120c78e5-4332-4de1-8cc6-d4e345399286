<template>
  <div class="basic-example">
    <h2>基础表格示例</h2>
    <p>展示 ViaTable 组件的基础用法</p>
    
    <div class="example-section">
      <h3>简单表格</h3>
      <ViaTable
        :data="tableData"
        :loading="loading"
        :show-selection="true"
        :show-index="true"
        :show-actions="true"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
      >
        <!-- 自定义列插槽 -->
        <template #table-name="{ row }">
          <el-tag>{{ row.name }}</el-tag>
        </template>
        
        <template #table-status="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
            {{ row.status === 'active' ? '激活' : '禁用' }}
          </el-tag>
        </template>
      </ViaTable>
    </div>
    
    <div class="example-section">
      <h3>操作按钮</h3>
      <el-space>
        <el-button @click="loadData">刷新数据</el-button>
        <el-button @click="addRow">添加行</el-button>
        <el-button @click="toggleLoading">切换加载状态</el-button>
      </el-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ViaTable } from '@via-grid/vue3'

const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 25,
    email: '<EMAIL>',
    status: 'active',
    createTime: '2024-01-01'
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    email: '<EMAIL>',
    status: 'inactive',
    createTime: '2024-01-02'
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    email: '<EMAIL>',
    status: 'active',
    createTime: '2024-01-03'
  }
])

const loadData = async () => {
  loading.value = true
  // 模拟 API 请求
  await new Promise(resolve => setTimeout(resolve, 1000))
  loading.value = false
}

const addRow = () => {
  const newId = Math.max(...tableData.value.map(item => item.id)) + 1
  tableData.value.push({
    id: newId,
    name: `用户${newId}`,
    age: Math.floor(Math.random() * 40) + 20,
    email: `user${newId}@example.com`,
    status: Math.random() > 0.5 ? 'active' : 'inactive',
    createTime: new Date().toISOString().split('T')[0]
  })
}

const toggleLoading = () => {
  loading.value = !loading.value
}

const handleView = (row: any) => {
  console.log('查看:', row)
  ElMessage.success(`查看用户: ${row.name}`)
}

const handleEdit = (row: any) => {
  console.log('编辑:', row)
  ElMessage.success(`编辑用户: ${row.name}`)
}

const handleDelete = (row: any) => {
  console.log('删除:', row)
  ElMessageBox.confirm(`确定要删除用户 ${row.name} 吗？`, '确认删除', {
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.basic-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}
</style>
