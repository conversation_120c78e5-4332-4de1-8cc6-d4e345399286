<template>
  <div class="grid-example">
    <h2>完整网格示例</h2>
    <p>展示 ViaGrid 组件的完整功能，包括搜索、表格、分页、表单等</p>
    
    <ViaGrid
      :config="gridConfig"
      :api="gridApi"
      @ready="handleGridReady"
    >
      <!-- 自定义搜索字段 -->
      <template #search-status="{ field, value, onChange }">
        <el-select
          :model-value="value"
          @update:model-value="onChange"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="激活" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </template>
      
      <!-- 自定义表格列 -->
      <template #table-avatar="{ row }">
        <el-avatar :src="row.avatar" :alt="row.name">
          {{ row.name.charAt(0) }}
        </el-avatar>
      </template>
      
      <template #table-status="{ row }">
        <el-switch
          :model-value="row.status === 'active'"
          @update:model-value="(val) => handleStatusChange(row, val)"
          active-text="激活"
          inactive-text="禁用"
        />
      </template>
      
      <!-- 自定义表单字段 -->
      <template #form-avatar="{ field, value, onChange }">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="handleAvatarUpload"
        >
          <img v-if="value" :src="value" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </template>
    </ViaGrid>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ViaGrid } from '@via-grid/vue3'
import { Plus } from '@element-plus/icons-vue'
import type { GridConfig, GridApi } from '@via-grid/core'

// 网格配置
const gridConfig: GridConfig = {
  // 字段配置
  fields: {
    id: {
      prop: 'id',
      label: 'ID',
      type: 'number',
      table: { width: 80, fixed: 'left' },
      search: { show: false },
      form: { show: false }
    },
    avatar: {
      prop: 'avatar',
      label: '头像',
      type: 'string',
      table: { width: 80 },
      search: { show: false },
      form: { span: 24 }
    },
    name: {
      prop: 'name',
      label: '姓名',
      type: 'string',
      required: true,
      table: { width: 120 },
      search: { show: true },
      form: { span: 12 }
    },
    email: {
      prop: 'email',
      label: '邮箱',
      type: 'email',
      required: true,
      table: { width: 200 },
      search: { show: true },
      form: { span: 12 }
    },
    age: {
      prop: 'age',
      label: '年龄',
      type: 'number',
      table: { width: 80 },
      search: { show: false },
      form: { span: 12 }
    },
    status: {
      prop: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '激活', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ],
      table: { width: 100 },
      search: { show: true },
      form: { span: 12 }
    },
    createTime: {
      prop: 'createTime',
      label: '创建时间',
      type: 'datetime',
      table: { width: 180 },
      search: { show: true, type: 'daterange' },
      form: { show: false }
    }
  },
  
  // 表格配置
  table: {
    showSelection: true,
    showIndex: true,
    showActions: true,
    actionWidth: 180
  },
  
  // 搜索配置
  search: {
    show: true,
    labelWidth: '80px',
    autoSearch: true,
    resetToFirstPage: true
  },
  
  // 分页配置
  pager: {
    show: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100]
  },
  
  // 表单配置
  form: {
    labelWidth: '100px',
    modalType: 'modal',
    width: '600px'
  },
  
  // 工具栏配置
  toolbar: {
    showDefaultButtons: true,
    buttons: [
      {
        key: 'export',
        label: '导出',
        icon: 'el-icon-download',
        type: 'primary'
      }
    ]
  }
}

// 模拟 API
const gridApi: GridApi = {
  async list(params) {
    console.log('搜索参数:', params)
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    const mockData = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${index}`,
      name: `用户${index + 1}`,
      email: `user${index + 1}@example.com`,
      age: Math.floor(Math.random() * 40) + 20,
      status: Math.random() > 0.5 ? 'active' : 'inactive',
      createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    }))
    
    // 简单的搜索过滤
    let filteredData = mockData
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name)
      )
    }
    if (params.email) {
      filteredData = filteredData.filter(item => 
        item.email.includes(params.email)
      )
    }
    if (params.status) {
      filteredData = filteredData.filter(item => 
        item.status === params.status
      )
    }
    
    // 分页
    const { page = 1, pageSize = 10 } = params
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const pageData = filteredData.slice(start, end)
    
    return {
      data: pageData,
      total: filteredData.length,
      page,
      pageSize
    }
  },
  
  async create(data) {
    console.log('创建数据:', data)
    await new Promise(resolve => setTimeout(resolve, 500))
    return { ...data, id: Date.now() }
  },
  
  async update(id, data) {
    console.log('更新数据:', id, data)
    await new Promise(resolve => setTimeout(resolve, 500))
    return { ...data, id }
  },
  
  async delete(id) {
    console.log('删除数据:', id)
    await new Promise(resolve => setTimeout(resolve, 500))
    return true
  }
}

let gridInstance: any = null

const handleGridReady = (grid: any) => {
  gridInstance = grid
  console.log('网格实例已准备就绪:', grid)
}

const handleStatusChange = async (row: any, status: boolean) => {
  try {
    await gridApi.update(row.id, {
      ...row,
      status: status ? 'active' : 'inactive'
    })
    
    // 刷新表格
    if (gridInstance) {
      gridInstance.refresh()
    }
    
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarUpload = (options: any) => {
  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    options.onSuccess(e.target?.result)
  }
  reader.readAsDataURL(options.file)
}
</script>

<style scoped>
.grid-example {
  padding: 20px;
}

.avatar-uploader .avatar {
  width: 60px;
  height: 60px;
  display: block;
}

.avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
}

.avatar-uploader .avatar-uploader-icon:hover {
  border-color: #409eff;
}
</style>
