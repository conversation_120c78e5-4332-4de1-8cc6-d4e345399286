<template>
  <div class="toolbar-example">
    <h2>工具栏示例</h2>
    <p>展示 ViaToolbar 组件基于 vxe-toolbar 的配置式 API 用法</p>
    
    <div class="example-section">
      <h3>基础工具栏</h3>
      <ViaToolbar
        :loading="loading"
        :selected-count="selectedCount"
        :show-default-buttons="true"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
        @refresh="handleRefresh"
      >
        <!-- 左侧自定义按钮 -->
        <template #toolbar-buttons>
          <vxe-button status="warning" icon="el-icon-upload">批量导入</vxe-button>
          <vxe-button status="success" icon="el-icon-download">批量导出</vxe-button>
        </template>
        
        <!-- 右侧自定义工具 -->
        <template #toolbar-tools>
          <vxe-button icon="el-icon-setting" content="设置" />
          <vxe-button icon="el-icon-help" content="帮助" />
        </template>
      </ViaToolbar>
    </div>
    
    <div class="example-section">
      <h3>完整功能工具栏</h3>
      <ViaToolbar
        :loading="loading"
        :selected-count="selectedCount"
        :show-default-buttons="true"
        :perfect="true"
        :custom="true"
        :import="true"
        :export="true"
        :print="true"
        :zoom="true"
        :setting="true"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
        @refresh="handleRefresh"
      />
    </div>
    
    <div class="example-section">
      <h3>自定义按钮工具栏</h3>
      <ViaToolbar
        :loading="loading"
        :selected-count="selectedCount"
        :buttons="customButtons"
        :show-default-buttons="false"
      />
    </div>
    
    <div class="example-section">
      <h3>模拟表格</h3>
      <el-table
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
      </el-table>
    </div>
    
    <div class="example-section">
      <h3>操作日志</h3>
      <el-card>
        <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
          <el-tag size="small">{{ log.time }}</el-tag>
          <span>{{ log.message }}</span>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ViaToolbar } from '@via-grid/vue3'
import type { ToolbarButton } from '@via-grid/vue3'

const loading = ref(false)
const selectedCount = ref(0)
const actionLogs = ref<Array<{ time: string, message: string }>>([])

const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    status: 'active',
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    status: 'inactive',
    createTime: '2024-01-02 11:00:00'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    status: 'active',
    createTime: '2024-01-03 12:00:00'
  }
])

const customButtons: ToolbarButton[] = [
  {
    key: 'approve',
    label: '批量审核',
    icon: 'el-icon-check',
    type: 'success',
    handler: () => handleApprove(),
    visible: () => selectedCount.value > 0
  },
  {
    key: 'reject',
    label: '批量拒绝',
    icon: 'el-icon-close',
    type: 'danger',
    handler: () => handleReject(),
    visible: () => selectedCount.value > 0
  },
  {
    key: 'archive',
    label: '归档',
    icon: 'el-icon-folder',
    type: 'warning',
    handler: () => handleArchive()
  },
  {
    key: 'sync',
    label: '同步数据',
    icon: 'el-icon-refresh',
    type: 'primary',
    handler: () => handleSync(),
    loading: () => loading.value
  }
]

const addLog = (message: string) => {
  actionLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  
  // 只保留最近 10 条日志
  if (actionLogs.value.length > 10) {
    actionLogs.value = actionLogs.value.slice(0, 10)
  }
}

const handleAdd = () => {
  addLog('点击了新增按钮')
  ElMessage.success('新增操作')
}

const handleEdit = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请先选择要编辑的数据')
    return
  }
  addLog(`点击了编辑按钮，选中 ${selectedCount.value} 条数据`)
  ElMessage.success(`编辑 ${selectedCount.value} 条数据`)
}

const handleDelete = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedCount.value} 条数据吗？`,
    '确认删除',
    { type: 'warning' }
  ).then(() => {
    addLog(`删除了 ${selectedCount.value} 条数据`)
    ElMessage.success('删除成功')
  }).catch(() => {
    addLog('取消了删除操作')
  })
}

const handleRefresh = async () => {
  loading.value = true
  addLog('开始刷新数据')
  
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    addLog('数据刷新完成')
    ElMessage.success('刷新成功')
  } finally {
    loading.value = false
  }
}

const handleApprove = () => {
  addLog(`批量审核了 ${selectedCount.value} 条数据`)
  ElMessage.success('批量审核成功')
}

const handleReject = () => {
  addLog(`批量拒绝了 ${selectedCount.value} 条数据`)
  ElMessage.success('批量拒绝成功')
}

const handleArchive = () => {
  addLog('执行了归档操作')
  ElMessage.success('归档成功')
}

const handleSync = async () => {
  loading.value = true
  addLog('开始同步数据')
  
  try {
    // 模拟同步延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    addLog('数据同步完成')
    ElMessage.success('同步成功')
  } catch (error) {
    addLog('数据同步失败')
    ElMessage.error('同步失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedCount.value = selection.length
  addLog(`选择了 ${selection.length} 条数据`)
}
</script>

<style scoped>
.toolbar-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
