<template>
  <div class="form-example">
    <h2>表单示例</h2>
    <p>展示 ViaForm 组件的配置式 API 用法</p>
    
    <div class="example-section">
      <h3>基础表单</h3>
      <el-button @click="showForm = true">打开表单</el-button>
      
      <ViaForm
        v-model:visible="showForm"
        :mode="formMode"
        :data="formData"
        :loading="formLoading"
        title="用户信息"
        @submit="handleSubmit"
        @cancel="handleCancel"
      >
        <!-- 自定义头像字段 -->
        <template #form-avatar="{ field, value, onChange }">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="(options) => handleAvatarUpload(options, onChange)"
          >
            <img v-if="value" :src="value" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </template>
        
        <!-- 自定义技能字段 -->
        <template #form-skills="{ field, value, onChange }">
          <el-select
            :model-value="value"
            @update:model-value="onChange"
            multiple
            placeholder="请选择技能"
            style="width: 100%"
          >
            <el-option
              v-for="skill in skillOptions"
              :key="skill.value"
              :label="skill.label"
              :value="skill.value"
            />
          </el-select>
        </template>
        
        <!-- 自定义地址字段 -->
        <template #form-address="{ field, value, onChange }">
          <el-cascader
            :model-value="value"
            @update:model-value="onChange"
            :options="addressOptions"
            placeholder="请选择地址"
            style="width: 100%"
          />
        </template>
      </ViaForm>
    </div>
    
    <div class="example-section">
      <h3>表单数据</h3>
      <el-card>
        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      </el-card>
    </div>
    
    <div class="example-section">
      <h3>操作按钮</h3>
      <el-space>
        <el-button @click="openAddForm">新增用户</el-button>
        <el-button @click="openEditForm">编辑用户</el-button>
        <el-button @click="resetForm">重置表单</el-button>
      </el-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ViaForm } from '@via-grid/vue3'
import { Plus } from '@element-plus/icons-vue'

const showForm = ref(false)
const formMode = ref<'add' | 'edit'>('add')
const formLoading = ref(false)

const formData = reactive({
  id: null,
  avatar: '',
  name: '',
  email: '',
  age: null,
  gender: '',
  phone: '',
  skills: [],
  address: [],
  bio: '',
  status: 'active'
})

const skillOptions = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'Vue.js', value: 'vue' },
  { label: 'React', value: 'react' },
  { label: 'Node.js', value: 'nodejs' },
  { label: 'Python', value: 'python' },
  { label: 'Java', value: 'java' },
  { label: 'Go', value: 'go' }
]

const addressOptions = [
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'changning', label: '长宁区' }
    ]
  },
  {
    value: 'guangzhou',
    label: '广州',
    children: [
      { value: 'tianhe', label: '天河区' },
      { value: 'yuexiu', label: '越秀区' },
      { value: 'haizhu', label: '海珠区' }
    ]
  }
]

const openAddForm = () => {
  formMode.value = 'add'
  resetForm()
  showForm.value = true
}

const openEditForm = () => {
  formMode.value = 'edit'
  // 模拟编辑数据
  Object.assign(formData, {
    id: 1,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
    name: '张三',
    email: '<EMAIL>',
    age: 25,
    gender: 'male',
    phone: '13800138000',
    skills: ['javascript', 'vue', 'nodejs'],
    address: ['beijing', 'haidian'],
    bio: '这是一个测试用户的简介信息。',
    status: 'active'
  })
  showForm.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    avatar: '',
    name: '',
    email: '',
    age: null,
    gender: '',
    phone: '',
    skills: [],
    address: [],
    bio: '',
    status: 'active'
  })
}

const handleSubmit = async () => {
  formLoading.value = true
  
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('提交表单数据:', formData)
    
    ElMessage.success(formMode.value === 'add' ? '创建成功' : '更新成功')
    showForm.value = false
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

const handleCancel = () => {
  showForm.value = false
}

const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarUpload = (options: any, onChange: Function) => {
  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    const result = e.target?.result as string
    onChange(result)
    options.onSuccess(result)
  }
  reader.readAsDataURL(options.file)
}
</script>

<style scoped>
.form-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
  border-radius: 6px;
}

.avatar-uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
}

.avatar-uploader .avatar-uploader-icon:hover {
  border-color: #409eff;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
