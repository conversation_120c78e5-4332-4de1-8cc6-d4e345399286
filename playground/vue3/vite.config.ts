import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@via-grid/core': resolve(__dirname, '../../packages/core/src'),
      '@via-grid/shared': resolve(__dirname, '../../packages/shared/src'),
      '@via-grid/vue3': resolve(__dirname, '../../packages/vue3/src')
    }
  },
  server: {
    port: 3000,
    open: true
  }
})
