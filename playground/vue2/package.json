{"name": "via-grid-playground-vue2", "version": "0.0.0", "description": "Via Grid Vue2 开发调试环境", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@via-grid/core": "workspace:*", "@via-grid/shared": "workspace:*", "@via-grid/vue2": "workspace:*", "vue": "^2.7.16", "vxe-table": "^3.10.0", "element-ui": "^2.15.14"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.1", "vite": "^5.0.12", "typescript": "^5.3.3"}}