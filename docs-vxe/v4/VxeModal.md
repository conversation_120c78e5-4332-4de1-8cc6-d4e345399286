# VxeModal 弹窗

VxeModal 是一个功能强大的弹窗组件，支持多种类型的弹窗（模态框、消息提示、确认框等），具有丰富的配置选项和交互功能。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/es/style.css'

const app = createApp()
app.use(VxeUI)
```

### 局部引入
```javascript
import { VxeModal } from 'vxe-pc-ui'
```

## 基础用法

```vue
<template>
  <div>
    <vxe-button @click="showModal = true">打开弹窗</vxe-button>
    <vxe-modal v-model="showModal" title="基础弹窗" width="500" height="300">
      <p>这是一个基础的弹窗内容</p>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showModal: false
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| modelValue | v-model 绑定值 | boolean | — | — |
| size | 尺寸 | string | medium, small, mini | 继承上下文 |
| loading | 是否加载中 | boolean | — | false |
| id | 设置唯一的 id（对于 Message 防止重复弹出 或 Storage 拖动状态保存等场景可能会用到） | string | — | — |

### 内容配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| title | 窗口的标题（支持开启国际化） | string | — | 消息提示 |
| content | 显示的文本（支持开启国际化） | string | — | — |
| type | 窗口类型 | string | alert, confirm, message | — |
| status | 只对 type=alert,confirm,message 有效，消息状态 | string | info, success, warning, error, loading | — |
| className | 给窗口附加 className | string | — | — |
| iconStatus | 自定义状态图标 | string | — | — |

### 按钮配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| show-cancel-button | 是否显示取消按钮 | boolean | — | 默认false（如果 type=confirm 则默认 true） |
| cancel-button-text | 只对 type=confirm 有效，取消按钮的文本内容 | string | — | 取消 |
| show-confirm-button | 是否显示确认按钮 | boolean | — | 默认false（如果 type=alert\|confirm 则默认 true） |
| confirm-button-text | 只对 type=alert,confirm 有效，确定按钮的文本内容 | string | — | 确定 |
| confirm-closable | 点击确认按钮时自动关闭窗口 | boolean | — | true |
| cancel-closable | 点击取消按钮时自动关闭窗口 | boolean | — | true |

### 显示配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| showHeader | 是否显示头部 | boolean | — | true |
| showFooter | 是否显示底部 | boolean | — | false |
| show-maximize | 是否显示最大化按钮 | boolean | — | false |
| show-minimize | 是否显示最小化按钮 | boolean | — | false |
| show-zoom | 是否显示最大化与最小化按钮 | boolean | — | false |
| show-close | 是否显示关闭按钮 | boolean | — | true |
| show-title-overflow | 设置标题内容过长时显示为省略号 | boolean | — | true |

### 交互配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| lock-view | 是否锁住页面，不允许窗口之外的任何操作 | boolean | — | true |
| lock-scroll | 是否锁住滚动条，不允许页面滚动 | boolean | — | true |
| mask | 是否显示遮罩层 | boolean | — | true |
| mask-closable | 是否允许点击遮罩层关闭窗口 | boolean | — | false |
| esc-closable | 是否允许按 Esc 键关闭窗口 | boolean | — | false |
| draggable | 是否启用窗口拖动 | boolean | — | 默认 true，继承 setConfig.modal.draggable |
| dblclickZoom | 只对 type=modal 有效，是否允许通过双击头部放大或还原窗口 | boolean | — | 默认 true，继承 setConfig.modal.dblclickZoom |

### 尺寸配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| width | 窗口的宽度 | string \| number | — | — |
| height | 窗口的高度 | string \| number | — | — |
| min-width | 窗口的最小宽度 | string \| number | — | — |
| min-height | 窗口的最小高度 | string \| number | — | — |
| resize | 是否允许窗口边缘拖动调整窗口大小 | boolean | — | false |
| margin-size | 只对 resize 启用后有效，用于设置可拖动界限范围，如果为负数则允许拖动超出屏幕边界 | number | — | 默认 0，继承 setConfig.modal.marginSize |

### 位置配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| top | 只对 type=message 有效，消息距离顶部的位置 | string \| number | — | 15 |
| position | 只对 type=modal 有效，窗口的默认位置，可以设置为 center 居中显示 | string | — | — |
| zIndex | 自定义堆叠顺序（对于某些特殊场景，比如被遮挡时可能会用到） | number | — | 继承 setConfig.zIndex |
| transfer | 是否将弹框容器插入于 body 内 | boolean | — | 默认 false，继承 setConfig.modal.transfer |

### 高级配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| duration | 只对 type=message 有效，自动关闭的延时，如果为 -1 禁用自动关闭 | number | — | 3000 |
| fullscreen | 窗口打开时默认最大化显示（默认只会在首次打开触发一次，如果需要每次都发可以通过 remember 设置） | boolean | — | false |
| remember | 记忆功能，会记住最后操作状态，再次打开窗口时还原窗口状态 | boolean | — | false |
| destroy-on-close | 在窗口关闭时销毁内容 | boolean | — | false |
| storage | 是否启用 localStorage 本地保存，会将窗口拖动的状态保存到本地（需要有 id） | boolean | — | false |
| before-hide-method | 在窗口隐藏之前执行，可以返回 Error 阻止关闭，支持异步 | Function | — | — |
| zoom-config | 缩放配置项 | object | — | — |

## Slots 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 窗口内容模板 | {} |
| header | 窗口头部的模板（如果使用了，则 slot title 无效） | {} |
| title | 窗口标题的模板 | {} |
| corner | 窗口右上角的模板 | {} |
| footer | 窗口底部的模板 | {} |
| left | 自定义左侧模板 | {} |
| right | 自定义右侧模板 | {} |
| aside | 窗口侧边栏的模板 | {} |
| leftfoot | 窗口底部左侧的模板 | {} |
| rightfoot | 窗口底部右侧的模板 | {} |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| show | 在窗口显示时会触发该事件 | { type } |
| hide | 在窗口隐藏时会触发该事件 | { type } |
| confirm | 点击确定按钮时会触发该事件 | { type, $event } |
| cancel | 点击取消按钮时会触发该事件 | { type, $event } |
| close | 点击关闭按钮时会触发该事件 | { type, $event } |
| zoom | 窗口缩放时会触发该事件 | { type, $event } |
| resize | 窗口调整大小时会触发该事件 | { type, $event } |
| move | 窗口移动时会触发该事件 | { type, $event } |

## Methods 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| open() | 手动打开窗口 | — | — |
| close() | 手动关闭窗口 | — | — |
| getBox() | 获取当前窗口元素 | — | Element |
| getPosition() | 只对 type=modal 有效，获取窗口位置 | — | {top,left} |
| setPosition(top, left) | 只对 type=modal 有效，设置窗口位置 | top?: number, left?: number | Promise |
| isMaximized() | 判断是否最大化显示 | — | Boolean |
| zoom() | 切换窗口最大化/还原 | — | Promise |
| minimize() | 最小化窗口 | — | Promise |
| maximize() | 最大化窗口 | — | Promise |
| revert() | 还原窗口 | — | Promise |

## 常见示例

### 基础弹窗
```vue
<template>
  <div>
    <vxe-button @click="showModal = true">打开弹窗</vxe-button>
    <vxe-modal v-model="showModal" title="基础弹窗" width="500" height="300">
      <p>这是一个基础的弹窗内容</p>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showModal: false
    }
  }
}
</script>
```

### 确认对话框
```vue
<template>
  <div>
    <vxe-button @click="showConfirm = true">确认对话框</vxe-button>
    <vxe-modal 
      v-model="showConfirm" 
      type="confirm" 
      title="确认删除" 
      content="确定要删除这条记录吗？"
      @confirm="handleConfirm"
      @cancel="handleCancel">
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showConfirm: false
    }
  },
  methods: {
    handleConfirm() {
      console.log('确认删除')
      this.showConfirm = false
    },
    handleCancel() {
      console.log('取消删除')
      this.showConfirm = false
    }
  }
}
</script>
```

### 消息提示
```vue
<template>
  <div>
    <vxe-button @click="showMessage">显示消息</vxe-button>
  </div>
</template>

<script>
import { VxeUI } from 'vxe-pc-ui'

export default {
  methods: {
    showMessage() {
      VxeUI.modal.message({
        content: '这是一条消息提示',
        status: 'success'
      })
    }
  }
}
</script>
```

### 可拖拽弹窗
```vue
<template>
  <div>
    <vxe-button @click="showDraggable = true">可拖拽弹窗</vxe-button>
    <vxe-modal 
      v-model="showDraggable" 
      title="可拖拽弹窗" 
      width="600" 
      height="400"
      draggable
      resize>
      <p>这个弹窗可以拖拽和调整大小</p>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showDraggable: false
    }
  }
}
</script>
```

### 全屏弹窗
```vue
<template>
  <div>
    <vxe-button @click="showFullscreen = true">全屏弹窗</vxe-button>
    <vxe-modal 
      v-model="showFullscreen" 
      title="全屏弹窗" 
      fullscreen
      show-zoom>
      <p>这是一个全屏弹窗</p>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showFullscreen: false
    }
  }
}
</script>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.7.30
- 依赖：vxe-pc-ui

## 相关链接

- [官方文档](https://vxeui.com/)
- [GitHub](https://github.com/x-extends/vxe-pc-ui)
- [Gitee](https://gitee.com/x-extends/vxe-pc-ui)
