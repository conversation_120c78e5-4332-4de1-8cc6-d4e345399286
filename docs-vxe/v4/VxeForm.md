# VxeForm 表单

VxeForm 是一个功能强大的表单组件，支持数据绑定、校验、布局控制、渲染器配置等功能，可以快速构建各种复杂的表单界面。支持多种内置渲染器，包括输入框、选择器、日期选择器、单选框、多选框等，同时支持自定义渲染器和插槽。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/es/style.css'

const app = createApp()
app.use(VxeUI)
```

### 局部引入
```javascript
import { VxeForm } from 'vxe-pc-ui'
```

## 基础用法

```vue
<template>
  <vxe-form :data="formData" :items="formItems" @submit="submitEvent"></vxe-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        age: '',
        email: ''
      },
      formItems: [
        { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
        { field: 'age', title: '年龄', itemRender: { name: 'VxeNumberInput' } },
        { field: 'email', title: '邮箱', itemRender: { name: 'VxeInput' } },
        { 
          itemRender: { 
            name: 'VxeButton', 
            props: { type: 'submit', content: '提交' } 
          } 
        }
      ]
    }
  },
  methods: {
    submitEvent({ data }) {
      console.log('表单数据：', data)
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| data | 表单数据 | VxeFormPropTypes.Data | — | — | — |
| loading | 是否加载中 | VxeFormPropTypes.Loading | — | false | — |
| span | 所有项的栅格占据的列数（共 24 分栏） | VxeFormPropTypes.Span | — | — | — |
| align | 所有项的内容对齐方式 | VxeFormPropTypes.Align | — | — | — |
| vertical-align | 所有项垂直的对齐方式 | VxeFormPropTypes.VerticalAlign | center | center | vxe-pc-ui@4.2.0 |
| border | 显示边框 | VxeFormPropTypes.Border | — | false | vxe-pc-ui@4.2.0 |
| size | 尺寸 | VxeFormPropTypes.Size | medium, small, mini | 继承上下文 | — |

### 标题配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| title-background | 所有项显示标题背景 | VxeFormPropTypes.TitleBackground | — | false | vxe-pc-ui@4.2.0 |
| title-align | 所有项的标题对齐方式 | VxeFormPropTypes.TitleAlign | — | — | — |
| title-width | 所有项的标题宽度 | VxeFormPropTypes.TitleWidth | — | — | — |
| title-colon | 是否显示标题冒号 | VxeFormPropTypes.TitleColon | — | 默认 false，继承 setConfig.form.titleColon | — |
| title-asterisk | 是否显示必填字段的红色星号 | VxeFormPropTypes.TitleAsterisk | — | 默认 true，继承 setConfig.form.titleAsterisk | — |
| title-overflow | 所有设置标题内容过长时显示为省略号 | VxeFormPropTypes.TitleOverflow | ellipsis（只显示省略号）, title（并且显示为原生 title）, tooltip（并且显示为 tooltip 提示） | — | vxe-pc-ui@4.0.4 |

### 布局配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| padding | 显示边距 | VxeFormPropTypes.Padding | — | 默认 true，继承 setConfig.form.padding | vxe-pc-ui@4.1.0 |
| vertical | 所有项使用垂直布局 | VxeFormPropTypes.Vertical | — | false | vxe-pc-ui@4.5.10 |
| class-name | 给表单附加 className | VxeFormPropTypes.ClassName | — | — | vxe-pc-ui@4.0.9 |

### 状态配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| readonly | 设置表单为只读状态，所有被支持的控件会默认继承该属性，在只读模式中，校验功能将不会生效 | VxeFormPropTypes.Readonly | — | false | vxe-pc-ui@4.0.51 |
| disabled | 设置表单为禁用状态，所有被支持的控件会默认继承该属性，在禁用模式中，校验功能是有效的 | VxeFormPropTypes.Disabled | — | false | vxe-pc-ui@4.0.51 |

### 折叠配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| collapse-status | v-model 绑定值，折叠状态 | VxeFormPropTypes.CollapseStatus | — | true | vxe-pc-ui@4.0.29 |
| custom-layout | 是否使用自定义布局 | VxeFormPropTypes.CustomLayout | — | 默认 false，继承 setConfig.form.customLayout | vxe-pc-ui@4.0.29 |
| collapse-config | 可折叠配置项 | VxeFormPropTypes.CollapseConfig | — | — | vxe-pc-ui@4.2.26 |

### 数据配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| items | 项列表 | VxeFormPropTypes.Items | — | — | — |
| rules | 校验规则配置项 | VxeFormPropTypes.Rules | — | — | — |
| prevent-submit | 是否禁用默认的回车提交方式，禁用后配合 validate() 方法可以更加自由的控制提交逻辑 | VxeFormPropTypes.PreventSubmit | — | false | — |
| valid-config | 检验配置项 | VxeFormPropTypes.ValidConfig | — | — | — |
| params | 额外的参数（可以用来存放一些私有参数） | VxeFormPropTypes.Params | — | — | vxe-pc-ui@4.6.45 |

### Items 配置项详细说明

`items` 数组中的每个对象支持以下属性：

#### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| field | 字段名 | string | — | — | — |
| title | 标题（支持开启国际化） | string | — | — | — |
| span | 栅格占据的列数（共 24 分栏） | number | — | — | — |
| align | 内容对齐方式 | string | — | — | — |
| vertical-align | 项垂直的对齐方式 | string | center | — | vxe-pc-ui@4.2.0 |

#### 标题配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| title-background | 显示标题背景 | boolean | — | false | vxe-pc-ui@4.2.0 |
| title-align | 标题对齐方式 | string | — | — | — |
| title-width | 标题宽度 | string \| number | — | — | — |
| title-colon | 是否显示标题冒号 | boolean | — | 继承 form.titleColon | — |
| title-asterisk | 是否显示必填字段的红色星号 | boolean | — | 继承 form.titleAsterisk | — |
| title-overflow | 标题内容过长时显示为省略号 | string | ellipsis, title, tooltip | 继承 form.title-overflow | — |
| show-title | 是否显示标题 | boolean | — | true | — |
| title-prefix | 前缀配置项 | any | — | — | — |
| title-suffix | 后缀配置项 | any | — | — | — |

#### 布局配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| padding | 显示边距 | boolean | — | 继承 form.padding | vxe-pc-ui@4.1.0 |
| vertical | 使用垂直布局 | boolean | — | 继承 form.vertical | — |
| class-name | 给表单项附加 className | string \| Function | — | — | — |
| content-class-name | 给表单项内容附加 className | string \| Function | — | — | — |
| content-style | 给表单项内容附加样式 | object \| Function | — | — | — |

#### 显示控制

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| visible | 默认是否显示 | boolean | — | true | — |
| visible-method | 该方法的返回值用来决定该项是否显示 | Function | — | — | — |
| folding | 默认收起 | boolean | — | false | — |
| collapse-node | 折叠节点 | boolean | — | false | — |

#### 渲染配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| item-render | 项渲染器配置项 | object | — | — | — |
| reset-value | 重置时的默认值 | any \| Function | — | undefined | vxe-pc-ui@4.3.8 |
| formatter | 格式化显示内容 | string \| Function | — | — | vxe-pc-ui@4.6.2 |
| params | 额外的参数（可以用来存放一些私有参数） | any | — | — | vxe-pc-ui@4.6.45 |
| children | 项集合 | any[] | — | — | vxe-pc-ui@4.0.7 |
| slots | 插槽 | object | — | — | — |

#### item-render 渲染器配置

`item-render` 对象支持以下属性：

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| name | 渲染器名称 | string | input, textarea, select, VxeInput, VxeTextarea, VxeSelect, VxeButton, VxeButtonGroup, VxeRadio, VxeRadioGroup, VxeCheckbox, VxeCheckboxGroup, VxeSwitch, VxeDatePicker, VxeDateRangePicker | — |
| enabled | 是否启用 | boolean | — | true |
| props | 渲染的参数（请查看对应组件的 Props） | any | — | — |
| options | 下拉选项列表 | any[] | — | — |
| option-props | 下拉选项属性参数配置 | any | — | { value, label } |
| option-groups | 下拉分组选项列表 | any[] | — | — |
| option-group-props | 下拉分组选项属性参数配置 | any | — | { options, label } |
| events | 渲染组件的事件（请查看对应组件的 Events） | any | — | {data, field}, ...[自定义的 arguments] |
| content | 渲染组件的内容（仅用于特殊组件） | string | — | — |
| default-value | 项默认值 | any \| Function | — | null |
| params | 额外的参数（可以用来存放一些私有参数） | any | — | — |

#### 常用渲染器示例

```javascript
// 输入框
{
  field: 'name',
  title: '姓名',
  itemRender: {
    name: 'VxeInput',
    props: { placeholder: '请输入姓名', clearable: true }
  }
}

// 数字输入框
{
  field: 'age',
  title: '年龄',
  itemRender: {
    name: 'VxeNumberInput',
    props: { min: 1, max: 120, placeholder: '请输入年龄' }
  }
}

// 密码输入框
{
  field: 'password',
  title: '密码',
  itemRender: {
    name: 'VxePasswordInput',
    props: { placeholder: '请输入密码', showPassword: true }
  }
}

// 文本域
{
  field: 'remark',
  title: '备注',
  itemRender: {
    name: 'VxeTextarea',
    props: { placeholder: '请输入备注', rows: 4, maxlength: 200 }
  }
}

// 下拉选择
{
  field: 'gender',
  title: '性别',
  itemRender: {
    name: 'VxeSelect',
    props: { placeholder: '请选择性别', clearable: true },
    options: [
      { label: '男', value: '1' },
      { label: '女', value: '0' }
    ]
  }
}

// 下拉选择（自定义选项属性）
{
  field: 'city',
  title: '城市',
  itemRender: {
    name: 'VxeSelect',
    options: [
      { name: '北京', id: 'beijing' },
      { name: '上海', id: 'shanghai' }
    ],
    optionProps: { label: 'name', value: 'id' }
  }
}

// 单选框组
{
  field: 'status',
  title: '状态',
  itemRender: {
    name: 'VxeRadioGroup',
    options: [
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ]
  }
}

// 多选框组
{
  field: 'hobbies',
  title: '爱好',
  itemRender: {
    name: 'VxeCheckboxGroup',
    options: [
      { label: '读书', value: 'reading' },
      { label: '运动', value: 'sports' },
      { label: '音乐', value: 'music' },
      { label: '旅游', value: 'travel' }
    ]
  }
}

// 开关
{
  field: 'enabled',
  title: '启用状态',
  itemRender: {
    name: 'VxeSwitch',
    props: { openLabel: '开启', closeLabel: '关闭' }
  }
}

// 日期选择器
{
  field: 'birthday',
  title: '生日',
  itemRender: {
    name: 'VxeDatePicker',
    props: { placeholder: '请选择生日', clearable: true, format: 'yyyy-MM-dd' }
  }
}

// 日期范围选择器
{
  field: 'dateRange',
  title: '日期范围',
  itemRender: {
    name: 'VxeDateRangePicker',
    props: { startPlaceholder: '开始日期', endPlaceholder: '结束日期' }
  }
}

// 按钮
{
  itemRender: {
    name: 'VxeButton',
    props: { type: 'submit', content: '提交', status: 'primary' }
  }
}

// 按钮组
{
  itemRender: {
    name: 'VxeButtonGroup',
    content: [
      { content: '保存', status: 'primary' },
      { content: '重置', status: 'default' }
    ]
  }
}

// 带事件的渲染器
{
  field: 'category',
  title: '分类',
  itemRender: {
    name: 'VxeSelect',
    options: [],
    events: {
      change: ({ data, field }, event) => {
        console.log('分类改变:', data[field])
        // 可以在这里处理联动逻辑
      }
    }
  }
}
```

## Slots 插槽

| 插槽名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| default | 表单内容模板 | {} | — |

### Items 插槽说明

在 `items` 配置中，每个表单项也支持插槽配置。可以通过 `slots` 属性来定义插槽：

| 插槽名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| default | 自定义表单项内容 | { data, field, item } | — |
| title | 自定义标题内容 | { data, field, item } | — |
| prefix | 自定义标题前缀模板 | { data, field, item } | vxe-pc-ui@4.7.19 |
| suffix | 自定义标题后缀模板 | { data, field, item } | vxe-pc-ui@4.7.19 |
| extra | 已废弃，被 suffix 替换 | { data, field, item } | 已废弃 |

#### 插槽使用示例

```javascript
// 在 items 中使用插槽
{
  field: 'custom',
  title: '自定义内容',
  slots: {
    default: ({ data, field, item }) => {
      return h('div', [
        h('span', '自定义内容: '),
        h('strong', data[field])
      ])
    }
  }
}

// 自定义标题插槽
{
  field: 'name',
  title: '姓名',
  slots: {
    title: ({ data, field, item }) => {
      return h('span', { style: 'color: red' }, '* 姓名')
    }
  },
  itemRender: { name: 'VxeInput' }
}
```

## Events 事件

| 事件名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| submit | 只对 prevent-submit=false 有效，表单提交时会触发该事件 | { data, $event } | — |
| submit-invalid | 只对 prevent-submit=false 有效，表单提交时如果校验不通过会触发该事件 | { data, errMap, $event } | — |
| reset | 表单重置时会触发该事件 | { data, $event } | — |
| toggle-collapse | 即将废弃，请使用 collapse | { status, data, $event } | 已废弃 |
| collapse | 当折叠按钮被手动点击时会触发该事件 | { status, data, $event } | vxe-pc-ui@4.0.29 |

### 事件使用示例

```vue
<template>
  <vxe-form
    :data="formData"
    :items="formItems"
    :rules="formRules"
    @submit="handleSubmit"
    @submit-invalid="handleSubmitInvalid"
    @reset="handleReset"
    @collapse="handleCollapse">
  </vxe-form>
</template>

<script>
export default {
  methods: {
    handleSubmit({ data, $event }) {
      console.log('表单提交成功:', data)
      // 处理提交逻辑
    },
    handleSubmitInvalid({ data, errMap, $event }) {
      console.log('表单校验失败:', errMap)
      // 处理校验失败逻辑
    },
    handleReset({ data, $event }) {
      console.log('表单已重置:', data)
      // 处理重置逻辑
    },
    handleCollapse({ status, data, $event }) {
      console.log('折叠状态改变:', status)
      // 处理折叠状态改变逻辑
    }
  }
}
</script>
```

## Methods 方法

| 方法名 | 说明 | 参数 | 返回值 | 版本 |
|--------|------|------|-------|------|
| reset() | 重置表单 | — | Promise | — |
| validate() | 对表单进行校验 | — | Promise | — |
| validateField(fieldOrItem) | 对表单指定项进行校验，支持同时校验多个字段 | fieldOrItem?: string \| ItemInfo | Promise | vxe-pc-ui@4.0.28 |
| clearValidate(fieldOrItem) | 手动清除校验状态，如果指定 field 则清除指定的项，否则清除整个表单 | fieldOrItem?: string \| ItemInfo | Promise | — |
| updateStatus(slotParams) | 更新项状态（当使用自定义渲染时可能会用到） | slotParams: { field } | Promise | — |
| toggleCollapse() | 只对 collapse-node 有效，手动切换折叠状态 | — | Promise | — |
| getItems() | 获取表单项列表 | — | any[] | — |

### 方法使用示例

```vue
<template>
  <vxe-form ref="formRef" :data="formData" :items="formItems" :rules="formRules">
  </vxe-form>
  <div>
    <vxe-button @click="handleValidate">校验表单</vxe-button>
    <vxe-button @click="handleReset">重置表单</vxe-button>
    <vxe-button @click="handleValidateField">校验指定字段</vxe-button>
    <vxe-button @click="handleClearValidate">清除校验</vxe-button>
  </div>
</template>

<script>
export default {
  methods: {
    async handleValidate() {
      try {
        const result = await this.$refs.formRef.validate()
        console.log('校验通过:', result)
      } catch (errMap) {
        console.log('校验失败:', errMap)
      }
    },
    async handleReset() {
      await this.$refs.formRef.reset()
      console.log('表单已重置')
    },
    async handleValidateField() {
      try {
        const result = await this.$refs.formRef.validateField('name')
        console.log('字段校验通过:', result)
      } catch (errMap) {
        console.log('字段校验失败:', errMap)
      }
    },
    async handleClearValidate() {
      await this.$refs.formRef.clearValidate()
      console.log('校验状态已清除')
    }
  }
}
</script>
```

## 常见示例

### 基础表单
```vue
<template>
  <vxe-form :data="formData" :items="formItems"></vxe-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        age: 0,
        sex: '',
        address: ''
      },
      formItems: [
        { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
        { field: 'age', title: '年龄', itemRender: { name: 'VxeNumberInput' } },
        { 
          field: 'sex', 
          title: '性别', 
          itemRender: { 
            name: 'VxeSelect',
            options: [
              { label: '男', value: '1' },
              { label: '女', value: '0' }
            ]
          } 
        },
        { field: 'address', title: '地址', itemRender: { name: 'VxeTextarea' } }
      ]
    }
  }
}
</script>
```

### 带校验的表单
```vue
<template>
  <vxe-form 
    :data="formData" 
    :items="formItems" 
    :rules="formRules"
    @submit="submitEvent">
  </vxe-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        email: '',
        age: ''
      },
      formItems: [
        { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
        { field: 'email', title: '邮箱', itemRender: { name: 'VxeInput' } },
        { field: 'age', title: '年龄', itemRender: { name: 'VxeNumberInput' } },
        { 
          itemRender: { 
            name: 'VxeButton', 
            props: { type: 'submit', content: '提交' } 
          } 
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入姓名' }
        ],
        email: [
          { required: true, message: '请输入邮箱' },
          { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '邮箱格式不正确' }
        ],
        age: [
          { required: true, message: '请输入年龄' },
          { min: 1, max: 120, message: '年龄必须在1-120之间' }
        ]
      }
    }
  },
  methods: {
    submitEvent({ data }) {
      console.log('提交数据：', data)
    }
  }
}
</script>
```

### 栅格布局表单
```vue
<template>
  <vxe-form :data="formData" :items="formItems" span="12"></vxe-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        phone: '',
        email: '',
        address: ''
      },
      formItems: [
        { field: 'name', title: '姓名', span: 12, itemRender: { name: 'VxeInput' } },
        { field: 'phone', title: '电话', span: 12, itemRender: { name: 'VxeInput' } },
        { field: 'email', title: '邮箱', span: 12, itemRender: { name: 'VxeInput' } },
        { field: 'address', title: '地址', span: 24, itemRender: { name: 'VxeTextarea' } }
      ]
    }
  }
}
</script>
```

### 折叠表单
```vue
<template>
  <vxe-form
    :data="formData"
    :items="formItems"
    :collapse-config="{ trigger: 'manual' }"
    v-model:collapse-status="collapseStatus">
    <template #collapse>
      <vxe-button @click="toggleCollapse">
        {{ collapseStatus ? '展开' : '收起' }}
      </vxe-button>
    </template>
  </vxe-form>
</template>

<script>
export default {
  data() {
    return {
      collapseStatus: true,
      formData: {
        name: '',
        age: '',
        email: '',
        phone: '',
        address: ''
      },
      formItems: [
        { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
        { field: 'age', title: '年龄', itemRender: { name: 'VxeNumberInput' } },
        { field: 'email', title: '邮箱', folding: true, itemRender: { name: 'VxeInput' } },
        { field: 'phone', title: '电话', folding: true, itemRender: { name: 'VxeInput' } },
        { field: 'address', title: '地址', folding: true, itemRender: { name: 'VxeTextarea' } }
      ]
    }
  },
  methods: {
    toggleCollapse() {
      this.collapseStatus = !this.collapseStatus
    }
  }
}
</script>
```

### 完整的表单配置示例
```vue
<template>
  <vxe-form
    :data="formData"
    :items="formItems"
    :rules="formRules"
    title-colon
    title-asterisk
    @submit="submitEvent">
  </vxe-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        gender: '',
        age: '',
        email: '',
        phone: '',
        hobbies: [],
        birthday: '',
        status: '1',
        remark: ''
      },
      formItems: [
        // 基础输入框
        {
          field: 'name',
          title: '姓名',
          span: 12,
          itemRender: {
            name: 'VxeInput',
            props: { placeholder: '请输入姓名', clearable: true }
          }
        },
        // 下拉选择
        {
          field: 'gender',
          title: '性别',
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: { placeholder: '请选择性别' },
            options: [
              { label: '男', value: '1' },
              { label: '女', value: '0' }
            ]
          }
        },
        // 数字输入框
        {
          field: 'age',
          title: '年龄',
          span: 12,
          itemRender: {
            name: 'VxeNumberInput',
            props: { min: 1, max: 120, placeholder: '请输入年龄' }
          }
        },
        // 邮箱输入框
        {
          field: 'email',
          title: '邮箱',
          span: 12,
          itemRender: {
            name: 'VxeInput',
            props: { placeholder: '请输入邮箱地址', clearable: true }
          }
        },
        // 电话输入框
        {
          field: 'phone',
          title: '电话',
          span: 12,
          itemRender: {
            name: 'VxeInput',
            props: { placeholder: '请输入电话号码', clearable: true }
          }
        },
        // 日期选择器
        {
          field: 'birthday',
          title: '生日',
          span: 12,
          itemRender: {
            name: 'VxeDatePicker',
            props: { placeholder: '请选择生日', clearable: true }
          }
        },
        // 多选框组
        {
          field: 'hobbies',
          title: '爱好',
          span: 24,
          itemRender: {
            name: 'VxeCheckboxGroup',
            options: [
              { label: '读书', value: 'reading' },
              { label: '运动', value: 'sports' },
              { label: '音乐', value: 'music' },
              { label: '旅游', value: 'travel' }
            ]
          }
        },
        // 单选框组
        {
          field: 'status',
          title: '状态',
          span: 24,
          itemRender: {
            name: 'VxeRadioGroup',
            options: [
              { label: '启用', value: '1' },
              { label: '禁用', value: '0' }
            ]
          }
        },
        // 文本域
        {
          field: 'remark',
          title: '备注',
          span: 24,
          itemRender: {
            name: 'VxeTextarea',
            props: {
              placeholder: '请输入备注信息',
              rows: 4,
              maxlength: 200,
              showWordCount: true
            }
          }
        },
        // 提交按钮
        {
          span: 24,
          align: 'center',
          itemRender: {
            name: 'VxeButton',
            props: {
              type: 'submit',
              content: '提交',
              status: 'primary',
              size: 'medium'
            }
          }
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入姓名' },
          { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符' }
        ],
        gender: [
          { required: true, message: '请选择性别' }
        ],
        age: [
          { required: true, message: '请输入年龄' },
          { type: 'number', min: 1, max: 120, message: '年龄必须在1-120之间' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址' },
          { type: 'email', message: '请输入正确的邮箱地址' }
        ],
        phone: [
          { required: true, message: '请输入电话号码' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
        ]
      }
    }
  },
  methods: {
    submitEvent({ data }) {
      console.log('提交数据：', data)
      // 这里可以调用 API 提交数据
    }
  }
}
</script>
```

## 高级用法

### 动态表单项

```vue
<template>
  <vxe-form :data="formData" :items="dynamicItems">
  </vxe-form>
  <vxe-button @click="addItem">添加字段</vxe-button>
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      dynamicItems: [
        { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } }
      ]
    }
  },
  methods: {
    addItem() {
      const newField = `field_${Date.now()}`
      this.dynamicItems.push({
        field: newField,
        title: `动态字段 ${this.dynamicItems.length}`,
        itemRender: { name: 'VxeInput' }
      })
      this.$set(this.formData, newField, '')
    }
  }
}
</script>
```

### 条件显示

```javascript
// 使用 visibleMethod 实现条件显示
{
  field: 'email',
  title: '邮箱',
  itemRender: { name: 'VxeInput' },
  visibleMethod: ({ data }) => {
    // 只有当类型为 'user' 时才显示邮箱字段
    return data.type === 'user'
  }
}
```

### 自定义校验规则

```javascript
const formRules = {
  password: [
    { required: true, message: '请输入密码' },
    {
      validator: ({ itemValue }) => {
        if (itemValue && itemValue.length < 6) {
          return new Error('密码长度不能少于6位')
        }
      }
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: ({ itemValue, data }) => {
        if (itemValue && itemValue !== data.password) {
          return new Error('两次输入的密码不一致')
        }
      }
    }
  ]
}
```

## 最佳实践

### 1. 表单数据结构设计

```javascript
// 推荐：使用扁平的数据结构
const formData = {
  name: '',
  age: 0,
  email: '',
  address: ''
}

// 避免：过度嵌套的数据结构
const formData = {
  user: {
    basic: {
      name: '',
      age: 0
    },
    contact: {
      email: '',
      address: ''
    }
  }
}
```

### 2. 渲染器选择

```javascript
// 推荐：根据数据类型选择合适的渲染器
const items = [
  // 文本输入
  { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
  // 数字输入
  { field: 'age', title: '年龄', itemRender: { name: 'VxeNumberInput' } },
  // 选择器
  { field: 'gender', title: '性别', itemRender: { name: 'VxeSelect', options: [...] } },
  // 日期选择
  { field: 'birthday', title: '生日', itemRender: { name: 'VxeDatePicker' } }
]
```

### 3. 校验规则组织

```javascript
// 推荐：将校验规则单独组织
const validationRules = {
  required: (message) => ({ required: true, message }),
  email: { type: 'email', message: '请输入正确的邮箱格式' },
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
  minLength: (min, message) => ({ min, message }),
  maxLength: (max, message) => ({ max, message })
}

const formRules = {
  name: [validationRules.required('请输入姓名')],
  email: [validationRules.required('请输入邮箱'), validationRules.email],
  phone: [validationRules.required('请输入手机号'), validationRules.phone]
}
```

### 4. 性能优化

```javascript
// 推荐：使用 Object.freeze 冻结静态配置
const formItems = Object.freeze([
  { field: 'name', title: '姓名', itemRender: { name: 'VxeInput' } },
  { field: 'email', title: '邮箱', itemRender: { name: 'VxeInput' } }
])

// 推荐：避免在模板中使用复杂计算
// 不推荐
// :items="items.filter(item => item.visible)"

// 推荐
computed: {
  visibleItems() {
    return this.items.filter(item => item.visible)
  }
}
```

## 常见问题

### Q: 如何实现表单项的联动？

A: 可以通过渲染器的 events 配置或者使用 visibleMethod 来实现：

```javascript
{
  field: 'type',
  title: '类型',
  itemRender: {
    name: 'VxeSelect',
    options: [
      { label: '个人', value: 'personal' },
      { label: '企业', value: 'company' }
    ],
    events: {
      change: ({ data }) => {
        // 根据类型清空相关字段
        if (data.type === 'personal') {
          data.companyName = ''
        } else {
          data.idCard = ''
        }
      }
    }
  }
}
```

### Q: 如何自定义表单项的样式？

A: 可以通过 className、contentClassName 和 contentStyle 属性：

```javascript
{
  field: 'important',
  title: '重要信息',
  className: 'important-field',
  contentClassName: 'important-content',
  contentStyle: { backgroundColor: '#fff3cd' },
  itemRender: { name: 'VxeInput' }
}
```

### Q: 如何处理大量表单项的性能问题？

A: 可以使用以下策略：
1. 使用 `folding` 属性实现分组折叠
2. 使用 `visibleMethod` 按需显示
3. 避免在渲染器中使用复杂的计算逻辑
4. 合理使用 `v-show` 而不是 `v-if`

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.7.30
- 依赖：vxe-pc-ui

## 相关链接

- [官方文档](https://vxeui.com/)
- [GitHub](https://github.com/x-extends/vxe-pc-ui)
- [Gitee](https://gitee.com/x-extends/vxe-pc-ui)
- [VxeForm 在线示例](https://vxeui.com/#/form/base)
- [VxeForm API 文档](https://vxeui.com/#/form/api)
