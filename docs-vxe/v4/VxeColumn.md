# VxeColumn 列组件

VxeColumn 是 VxeTable 的列组件，用于定义表格的列配置，支持排序、筛选、编辑、自定义渲染等功能。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

const app = createApp()
app.use(VxeTable)
```

### 局部引入
```javascript
import { VxeColumn } from 'vxe-table'
```

## 基础用法

```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" width="120"></vxe-column>
    <vxe-column field="age" title="年龄" width="80" align="center"></vxe-column>
    <vxe-column field="address" title="地址" min-width="200"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市朝阳区' },
        { name: '李四', age: 30, address: '上海市浦东新区' }
      ]
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| type | 列的类型（部分功能需要设置 column-config.useKey 或 row-config.useKey） | string | — | — |
| field | 列字段名（注：属性层级越深，渲染性能就越差，例如：aa.bb.cc.dd.ee） | string | — | — |
| title | 列标题（支持开启国际化） | string | — | — |
| width | 列宽度（如果为空则均匀分配剩余宽度，如果全部列固定了，可能会存在宽屏下不会铺满，可以配合 "%" 或者 "min-width" 布局） | number \| string | auto, px, % | 继承 table.column-config.width |
| min-width | 最小列宽度；会自动将剩余空间按比例分配 | number \| string | auto, px, % | 继承 table.column-config.minWidth |
| resizable | 列是否允许拖动列宽调整大小 | boolean | — | 继承 table.resizable |
| visible | 默认是否显示 | boolean | — | true |
| fixed | 将列固定在左侧或者右侧（注意：固定列应该放在左右两侧的位置） | string | left（冻结左侧）, right（冻结右侧） | — |

### 对齐方式

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| align | 列对齐方式 | string | left（左对齐）, center（居中对齐）, right（右对齐） | 继承 table.align |
| header-align | 表头列的对齐方式 | string | left（左对齐）, center（居中对齐）, right（右对齐） | 继承 align > 继承 table.header-align |
| footer-align | 表尾列的对齐方式 | string | left（左对齐）, center（居中对齐）, right（右对齐） | 继承 align > 继承 table.footer-align |

### 显示控制

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| show-overflow | 当内容过长时显示为省略号 | string \| boolean | ellipsis（只显示省略号）, title（并且显示为原生 title）, tooltip（并且显示为 tooltip 提示） | 继承 table.show-overflow |
| show-header-overflow | 当表头内容过长时显示为省略号 | string \| boolean | ellipsis（只显示省略号）, title（并且显示为原生 title）, tooltip（并且显示为 tooltip 提示） | 继承 table.show-header-overflow |
| show-footer-overflow | 当表尾内容过长时显示为省略号 | boolean \| string | ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示） | 继承 table.show-footer-overflow |

### 样式配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| class-name | 给单元格附加 className | string \| (({row, rowIndex, $rowIndex, column, columnIndex, $columnIndex}) => any) | — | — |
| header-class-name | 给表头的单元格附加 className | string \| (({ $rowIndex, column, columnIndex, $columnIndex }) => any) | — | — |
| footer-class-name | 给表尾的单元格附加 className | string \| (({ $rowIndex, column, columnIndex, $columnIndex }) => any) | — | — |
| padding | 显示边距 | boolean | — | 继承 cell-config.padding |
| verticalAlign | 项垂直的对齐方式 | string | top, center | 继承 cell-config.verticalAlign |

### 格式化配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| formatter | 格式化显示内容 | (({ cellValue, row, column }) => string) \| any[] \| string | — | — |

### 排序配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| sortable | 数据排序，是否允许列排序 | boolean | — | false |
| sort-by | 数据排序，只对 sortable 有效，指定排序的字段（当值 formatter 格式化后，可以设置该字段，使用值进行排序） | string \| (({ row, column }) => string \| number) | — | — |
| sort-type | 数据排序，排序的字段类型，比如字符串转数值等 | string | auto, number, string | auto |

### 筛选配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| filters | 数据筛选，配置筛选条件（注：筛选只能用于列表，如果是树结构则过滤根节点） | any[] | — | — |
| filter-multiple | 数据筛选，只对 filters 有效，筛选是否允许多选 | boolean | — | true |
| filter-method | 数据筛选，只对 filters 有效，列的筛选方法，该方法的返回值用来决定该行是否显示 | ({ value, option, cellValue, row, column }) => boolean | — | — |
| filter-reset-method | 数据筛选，只对 filters 有效，自定义筛选重置方法 | ({ options, column }) => void | — | — |
| filter-recover-method | 数据筛选，只对 filters 有效，自定义筛选复原方法（使用自定义筛选时可能会用到） | ({ option, column }) => void | — | — |
| filter-render | 数据筛选，筛选渲染器配置项 | any | — | — |

### 导出配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| header-export-method | 自定义表头单元格数据导出方法，返回自定义的标题 | ({ column, options }) => string | — | — |
| export-method | 自定义单元格数据导出方法，返回自定义的值 | ({ row, column, options }) => string | — | — |
| footer-export-method | 自定义表尾单元格数据导出方法，返回自定义的值 | ({ items, _columnIndex, options }) => string | — | — |

### 标题配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 兼容性 |
|------|------|------|--------|--------|--------|
| title-help | 即将废弃，请使用 title.prefix | any | — | — | 已废弃 |
| title-prefix | 标题前缀图标配置项 | any | — | — | vxe-table@4.2.0 |
| title-suffix | 标题后缀图标配置项 | any | — | — | vxe-table@4.5.21 |

### 聚合配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| agg-func | 指定聚合函数 | boolean \| string | sum,max,count,avg,min,max,first,last | — |

### 单元格配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| cell-type | 只对特定功能有效，单元格值类型（例如：导出数据类型设置） | string | auto（默认自动转换），number（数值）, string（字符串） | auto |
| cell-render | 默认的渲染器配置项 | any | — | — |
| edit-render | 可编辑渲染器配置项 | any | — | — |
| content-render | 内容渲染配置项 | any | — | — |

### 树形配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| tree-node | 只对 tree-config 配置时有效，指定为树节点 | boolean | — | false |

### 其他配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| params | 额外的参数（可以用来存放一些私有参数） | any | — | — |
| col-id | 个性化列的唯一主键（注：列主键必须确保唯一，操作不正确将导致出现问题） | string \| number | — | — |

## Slots 插槽

| 插槽名 | 说明 | 参数 | 兼容性 |
|--------|------|------|--------|
| default | 自定义显示内容模板 | {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex, fixed} | — |
| header | 自定义表头内容的模板 | {column, columnIndex, $columnIndex, _columnIndex, $rowIndex, firstFilterOption, fixed} | vxe-pc-ui@*,4.10.1 |
| footer | 自定义表尾内容的模板（只有传 footerData 才有对应参数 row, rowIndex, $rowIndex） | {column, columnIndex, $columnIndex, _columnIndex, $rowIndex, items, fixed} | — |
| title | 只对 type=checkbox,radio 有效，自定义标题模板 | {column, columnIndex, $columnIndex, _columnIndex, $rowIndex, fixed} | vxe-table@4.0.15 |
| checkbox | 只对 type=checkbox 有效，自定义复选框模板 | {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex, checked, disabled, indeterminate, fixed} | vxe-table@4.0.15 |
| radio | 只对 type=radio 有效，自定义单选框模板 | {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex, checked, disabled, fixed} | vxe-table@4.0.15 |
| content | 只对 type=expand 有效，自定义展开后的内容模板 | {row, rowIndex, $rowIndex, column, fixed} | — |
| filter | 只对 filter-render 启用时有效，自定义筛选模板 | {column, columnIndex, $columnIndex, fixed} | — |
| edit | 只对 edit-render 启用时有效，自定义可编辑组件模板 | {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex, fixed} | — |
| valid | 只对 edit-rules 与 edit-render 启用时有效，自定义校验提示模板 | {row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex, rule, content} | vxe-table@4.5.20 |

## 常见示例

### 基础列配置
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" width="120"></vxe-column>
    <vxe-column field="age" title="年龄" width="80" align="center"></vxe-column>
    <vxe-column field="address" title="地址" min-width="200"></vxe-column>
  </vxe-table>
</template>
```

### 固定列
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" width="120" fixed="left"></vxe-column>
    <vxe-column field="age" title="年龄" width="80"></vxe-column>
    <vxe-column field="address" title="地址" width="200"></vxe-column>
    <vxe-column field="action" title="操作" width="100" fixed="right"></vxe-column>
  </vxe-table>
</template>
```

### 排序列
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" sortable></vxe-column>
    <vxe-column field="age" title="年龄" sortable sort-type="number"></vxe-column>
    <vxe-column field="date" title="日期" sortable sort-type="string"></vxe-column>
  </vxe-table>
</template>
```

### 筛选列
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" :filters="nameFilters"></vxe-column>
    <vxe-column field="age" title="年龄" :filters="ageFilters" :filter-multiple="false"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25 },
        { name: '李四', age: 30 }
      ],
      nameFilters: [
        { label: '张三', value: '张三' },
        { label: '李四', value: '李四' }
      ],
      ageFilters: [
        { label: '25岁', value: 25 },
        { label: '30岁', value: 30 }
      ]
    }
  }
}
</script>
```

### 格式化列
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="amount" title="金额" :formatter="formatAmount"></vxe-column>
    <vxe-column field="date" title="日期" :formatter="formatDate"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', amount: 1234.56, date: new Date() },
        { name: '李四', amount: 9876.54, date: new Date() }
      ]
    }
  },
  methods: {
    formatAmount({ cellValue }) {
      return `¥${cellValue.toFixed(2)}`
    },
    formatDate({ cellValue }) {
      return cellValue.toLocaleDateString()
    }
  }
}
</script>
```

### 自定义插槽
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="status" title="状态">
      <template #default="{ row }">
        <span :class="getStatusClass(row.status)">{{ row.status }}</span>
      </template>
    </vxe-column>
    <vxe-column title="操作" width="120">
      <template #default="{ row }">
        <button @click="edit(row)">编辑</button>
        <button @click="remove(row)">删除</button>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', status: '正常' },
        { name: '李四', status: '禁用' }
      ]
    }
  },
  methods: {
    getStatusClass(status) {
      return status === '正常' ? 'status-normal' : 'status-disabled'
    },
    edit(row) {
      console.log('编辑', row)
    },
    remove(row) {
      console.log('删除', row)
    }
  }
}
</script>

<style>
.status-normal { color: green; }
.status-disabled { color: red; }
</style>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.14.8
- 尝鲜版本：4.15.0-beta.12

## 相关链接

- [官方文档](https://vxetable.cn/v4/)
- [GitHub](https://github.com/x-extends/vxe-table)
- [Gitee](https://gitee.com/x-extends/vxe-table)
