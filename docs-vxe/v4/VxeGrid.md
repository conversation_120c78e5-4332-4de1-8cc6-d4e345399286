# VxeGrid 配置式表格

VxeGrid 是一个功能强大的配置式表格组件，基于 VxeTable 构建，提供了更高级的功能，包括表单、工具栏、分页、数据代理等完整的表格解决方案。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

const app = createApp()
app.use(VxeTable)
```

### 局部引入
```javascript
import { VxeGrid } from 'vxe-table'
```

## 基础用法

```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        showHeaderOverflow: true,
        showOverflow: true,
        keepSource: true,
        id: 'full_edit_1',
        height: 600,
        rowConfig: {
          keyField: 'id',
          isHover: true
        },
        columnConfig: {
          resizable: true
        },
        customConfig: {
          storage: true
        },
        printConfig: {},
        importConfig: {},
        exportConfig: {},
        checkboxConfig: {
          labelField: 'id',
          reserve: true,
          highlight: true,
          range: true
        },
        editRules: {
          name: [
            { required: true, message: 'app.body.valid.rName' },
            { min: 3, max: 50, message: '名称长度在 3 到 50 个字符' }
          ],
          email: [
            { required: true, message: '邮件必须填写' }
          ]
        },
        editConfig: {
          trigger: 'click',
          mode: 'row',
          showStatus: true
        },
        columns: [
          { type: 'checkbox', width: 50 },
          { type: 'seq', width: 60 },
          { field: 'name', title: '姓名', editRender: { name: 'input' } },
          { field: 'role', title: '角色', editRender: { name: 'input' } },
          { field: 'email', title: '邮箱', editRender: { name: 'input' } },
          { field: 'createTime', title: '创建时间' }
        ],
        data: [
          { id: 10001, name: 'Test1', role: 'Develop', email: '<EMAIL>', createTime: new Date() },
          { id: 10002, name: 'Test2', role: 'Test', email: '<EMAIL>', createTime: new Date() },
          { id: 10003, name: 'Test3', role: 'PM', email: '<EMAIL>', createTime: new Date() }
        ]
      }
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| id | 唯一标识（被某些特定的功能所依赖） | string | — | — |
| columns | 列配置 | array | — | — |
| data | 表格数据（与 loadData 行为一致，更新数据是不会重置状态） | any[] | — | — |
| height | 表格的高度；支持铺满父容器或者固定高度 | number \| string | %, px | — |
| min-height | 表格最小高度 | number \| string | %, px | 默认 144 |
| max-height | 表格的最大高度 | number \| string | %, px | — |
| auto-resize | 自动监听父元素的变化去重新计算表格 | boolean | — | true |
| sync-resize | 自动跟随某个属性的变化去重新计算表格 | boolean \| string \| number | — | — |

### 外观配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| stripe | 是否带有斑马纹样式 | boolean | — | false |
| border | 是否带有边框 | boolean \| string | default, full, outer, inner, none | false |
| round | 是否为圆角边框 | boolean | — | false |
| size | 表格的尺寸 | string | medium, small, mini | 继承上下文 |
| loading | 表格是否显示加载中 | boolean | — | true |

### 对齐方式

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| align | 所有的列对齐方式 | string | left, center, right | left |
| header-align | 所有的表头列的对齐方式 | string | left, center, right | 继承 align |
| footer-align | 所有的表尾列的对齐方式 | string | left, center, right | 继承 align |

### 显示控制

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| show-header | 是否显示表头 | boolean | — | false |
| show-footer | 是否显示表尾 | boolean | — | false |
| show-overflow | 设置所有内容过长时显示为省略号 | boolean \| string | ellipsis, title, tooltip | — |
| show-header-overflow | 设置表头所有内容过长时显示为省略号 | boolean \| string | ellipsis, title, tooltip | — |
| show-footer-overflow | 设置表尾所有内容过长时显示为省略号 | boolean \| string | ellipsis, title, tooltip | — |

### 样式配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| row-class-name | 给行附加 className | string \| Function | — | — |
| cell-class-name | 给单元格附加 className | string \| Function | — | — |
| header-row-class-name | 给表头的行附加 className | string \| Function | — | — |
| header-cell-class-name | 给表头的单元格附加 className | string \| Function | — | — |
| footer-row-class-name | 给表尾的行附加 className | string \| Function | — | — |
| footer-cell-class-name | 给表尾的单元格附加 className | string \| Function | — | — |

### 数据配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| footer-data | 表尾数据 | any[] | — | — |
| footer-method | 表尾的数据获取方法，返回一个二维数组 | Function | — | — |
| keep-source | 保持原始值的状态，被某些功能所依赖 | boolean | — | false |
| empty-text | 空数据时显示的内容 | string | — | — |
| empty-render | 空内容渲染配置项 | any | — | — |

### 合并配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| merge-cells | 临时合并指定的单元格 | Array | — | — |
| merge-footer-items | 临时合并表尾 | Array | — | — |
| span-method | 自定义合并函数 | Function | — | — |
| footer-span-method | 表尾合并行或列 | Function | — | — |

## 功能配置项

### 列配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| column-config | 列配置信息 | object | — |
| current-column-config | 当前列配置信息 | object | — |

### 行配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| row-config | 行配置信息 | object | — |
| current-row-config | 当前行配置信息 | object | — |

### 单元格配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| cell-config | 单元格配置项 | object | — |
| header-cell-config | 表头单元格配置项 | object | — |
| footer-cell-config | 表尾单元格配置项 | object | — |

### 聚合配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| aggregate-config | 数据聚合配置项 | object | — |

### 调整配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| resizable-config | 列宽拖动配置项 | object | — |

### 序号配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| seq-config | 序号配置项 | object | — |

### 排序配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| sort-config | 排序配置项 | object | — |

### 拖拽配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| row-drag-config | 行拖拽配置项 | object | — |
| column-drag-config | 列拖拽配置项 | object | — |

### 筛选配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| filter-config | 筛选配置项 | object | — |

### 导入导出配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| export-config | 导出配置项 | object | — |
| import-config | 导入配置项 | object | — |
| print-config | 打印配置项 | object | — |

### 选择配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| radio-config | 单选框配置项 | object | — |
| checkbox-config | 复选框配置项 | object | — |

### 提示配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tooltip-config | tooltip 配置项 | object | — |

### 展开配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| expand-config | 展开行配置项 | object | — |

### 树形配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tree-config | 树形结构配置项 | object | — |

### 菜单配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menu-config | 右键菜单配置项 | object | — |

### 鼠标配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| mouse-config | 鼠标配置项 | object | — |

### 键盘配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| keyboard-config | 按键配置项 | object | — |

### 编辑配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| edit-config | 可编辑配置项 | object | — |
| valid-config | 校验配置项 | object | — |
| edit-rules | 校验规则配置项 | object | — |

### 个性化配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| custom-config | 个性化信息配置项 | object | — |

### 虚拟滚动配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| virtual-x-config | 横向虚拟滚动配置项 | object | — |
| virtual-y-config | 纵向虚拟滚动配置项 | object | — |
| scrollbar-config | 滚动条配置项 | object | — |

### 加载配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| loading-config | 加载中配置项 | object | — |

### 其他配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| params | 自定义参数 | any | — |

## Grid 特有配置

### 表单配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| form-config | 表单配置项 | object | — |

### 工具栏配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| toolbar-config | 工具栏配置 | object | — |

### 分页配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| pager-config | 分页配置项 | object | — |

### 数据代理配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| proxy-config | 数据代理配置项（基于 Promise API） | object | — |

### 缩放配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| zoom-config | 缩放配置项 | object | — |

### 布局配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| layouts | 自定义布局 | string[][] | [[Form], [Toolbar, Top, Table, Bottom, Pager]] |

## Slots 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| empty | 自定义空数据时显示模板 | {} |
| loading | 自定义加载中模板 | {} |
| row-drag-icon | 自定义行拖拽按钮插槽模板 | { row, column } |
| column-drag-icon | 个性化列拖拽按钮插槽模板 | { column } |
| form | 表单模板 | {} |
| toolbar | 工具栏模板 | {} |
| top | 表格顶部模板 | {} |
| bottom | 表格底部模板 | {} |
| left | 表格左边模板 | {} |
| right | 表格右边模板 | {} |
| asideLeft | 左侧模板 | {} |
| asideRight | 右侧模板 | {} |
| pager | 分页模板 | {} |

## Events 事件

### 键盘事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| keydown-start | 当表格被激活且键盘被按下开始时会触发的事件 | { $event } |
| keydown | 当表格被激活且键盘被按下时会触发的事件 | { $event } |
| keydown-end | 当表格被激活且键盘被按下结束时会触发的事件 | { $event } |

### 选择事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| current-row-change | 当前行选中状态发生变化时触发的事件 | { newValue, oldValue, row, $event } |
| current-row-disabled | 当前列选中状态发生变化时触发的事件 | { row, $event } |
| current-column-change | 当手动选中列并且值发生改变时触发的事件 | { newValue, oldValue, column, $event } |
| current-column-disabled | 当手动选中列并且被禁止选中时触发的事件 | { column, $event } |
| radio-change | 当手动勾选单选框并且值发生改变时触发的事件 | { newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| checkbox-change | 当手动勾选复选框并且值发生改变时触发的事件 | { checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| checkbox-all | 当手动勾选全选时触发的事件 | { checked, $event } |
| checkbox-range-select | 当同时按住 Shift 键勾选复选框转为范围选择时会触发的事件 | { rangeRecords, $event } |
| checkbox-range-start | 当鼠标范围选择开始时会触发的事件 | { $event } |
| checkbox-range-change | 当鼠标范围选择内的行数发生变化时会触发的事件 | { records, reserves, $event } |
| checkbox-range-end | 当鼠标范围选择结束时会触发的事件 | { records, reserves, $event } |

### 单元格事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| cell-click | 单元格被点击时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event } |
| cell-dblclick | 单元格被双击时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-menu | 单元格被鼠标右键时触发该事件 | { type, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-mouseenter | 当鼠标移动到单元格时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-mouseleave | 当鼠标移开单元格时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-delete-value | 当按下删除键指定清空单元格值时会触发该事件 | { row, rowIndex, column, columnIndex, activeArea, cellAreas, $event } |

### 表头事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| header-cell-click | 表头单元格被点击时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, triggerResizable, triggerSort, triggerFilter, $event } |
| header-cell-dblclick | 表头单元格被双击时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, $event } |
| header-cell-menu | 表头单元格被鼠标右键时触发该事件 | { type, column, columnIndex, $event } |

### 表尾事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| footer-cell-click | 表尾单元格被点击时会触发该事件 | { items, $rowIndex, column, columnIndex, $columnIndex, $event } |
| footer-cell-dblclick | 表尾单元格被双击时会触发该事件 | { items, $rowIndex, column, columnIndex, $columnIndex, $event } |
| footer-cell-menu | 表尾单元格被鼠标右键时触发该事件 | { type, column, columnIndex, $event } |

### 排序事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| sort-change | 当排序条件发生变化时会触发该事件 | { column, field, order, sortBy, sortList, $event } |
| clear-sort | 当用户点击清除所有排序时会触发该事件 | { sortList, $event } |
| clear-all-sort | 当所有列排序条件都被清除空时会触发该事件 | { cols, sortList, $event } |

### 筛选事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| filter-change | 当筛选条件发生变化时会触发该事件 | { column, field, values, datas, filterList, $event } |
| filter-visible | 当筛选面板被触发时会触发该事件 | { column, field, visible, filterList, $event } |
| clear-filter | 当用户点击清除所有筛选条件时会触发该事件 | { filterList, $event } |
| clear-all-filter | 当所有列筛选条件都被清除空时会触发该事件 | { cols, filterList, $event } |

### 调整事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| column-resizable-change | 当列宽拖动发生变化时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, $event } |
| row-resizable-change | 当行高拖动发生变化时会触发该事件 | { row, $rowIndex, rowIndex, column, columnIndex, $columnIndex, $event } |

### 展开事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| toggle-row-expand | 当行展开或收起时会触发该事件 | { expanded, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| toggle-tree-expand | 当树节点展开或收起时会触发该事件 | { expanded, row, column, columnIndex, $columnIndex, $event } |

### 菜单事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| menu-click | 当点击右键菜单时会触发该事件 | { menu, type, row, rowIndex, column, columnIndex, $event } |

### 编辑事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| edit-closed | 单元格编辑状态下被关闭时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |
| edit-activated | 单元格被激活编辑时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |
| edit-disabled | 当单元格激活时如果是禁用状态时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |

### 校验事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| valid-error | 当数据校验不通过时会触发该事件 | { rule, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |

### 滚动事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| scroll | 表格滚动时会触发该事件 | { type, isTop, isBottom, isLeft, isRight, scrollTop, scrollLeft, scrollHeight, scrollWidth, bodyWidth, bodyHeight, isX, isY, $event } |
| scroll-boundary | 当滚动条滚动到边界时会触发该事件 | { type, direction, isTop, isBottom, isLeft, isRight, scrollTop, scrollLeft, scrollHeight, scrollWidth, bodyWidth, bodyHeight, isX, isY, $event } |

### 个性化事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| custom | 在个性化列按钮被手动点击后会触发该事件 | { type, $event } |
| custom-fixed-change | 个性化列面板中切换列冻结状态时会触发该事件 | { column, fixed, $event } |
| custom-visible-change | 个性化列面板中切换列可视时会触发该事件 | { column, checked, $event } |
| custom-visible-all | 个性化列面板中切换全部列可视时会触发该事件 | { checked, $event } |

### 拖拽事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| row-dragstart | 当行拖拽按钮开始拖拽时会触发该事件 | { row, column, $event } |
| row-dragover | 当行拖拽按钮拖拽过程中会触发该事件 | { oldRow, targetRow, dragRow, dragPos, $event } |
| row-dragend | 当行拖拽按钮拖拽结束时会触发该事件 | { newRow, oldRow, dragRow, dragPos, dragToChild, offsetIndex, $event } |
| column-dragstart | 当列拖拽按钮开始拖拽时会触发该事件 | { column, $event } |
| column-dragover | 当列拖拽按钮拖拽过程中会触发该事件 | { oldColumn, targetColumn, dragColumn, dragPos, dragToChild, $event } |
| column-dragend | 当列拖拽按钮拖拽结束时会触发该事件 | { newColumn, oldColumn, dragColumn, dragPos, offsetIndex, $event } |

### Grid 特有事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| page-change | 分页发生改变时会触发该事件 | { type, currentPage, pageSize, $event } |
| form-submit | 表单提交时会触发该事件 | { data, $event } |
| form-submit-invalid | 表单提交时如果校验不通过会触发该事件 | { data, errMap, $event } |
| form-reset | 表单重置时会触发该事件 | { data, $event } |
| form-collapse | 当折叠按钮被手动点击时会触发该事件 | { status, data, $event } |
| proxy-query | 当手动点击查询时会触发该事件 | { status, isReload, isInited } |
| proxy-delete | 当手动点击删除时会触发该事件 | { status } |
| proxy-save | 当手动点击保存时会触发该事件 | { status } |
| toolbar-button-click | 当左侧按钮被点击时会后触发该事件 | { code, button, $event } |
| toolbar-tool-click | 当右侧工具被点击时会后触发该事件 | { code, tool, $event } |
| zoom | 当最大化或还原操作被手动点击时会后触发该事件 | { type, $event } |

## Methods 方法

### 基础方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getEl() | 获取容器元素 | — | HTMLDivElement |
| loadData(data) | 加载数据 | data: array | Promise |
| reloadData(data) | 加载数据并清除所有状态 | data: array | Promise |
| updateData() | 手动处理数据，用于手动排序与筛选 | — | Promise |

### 行操作方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setRow(rows, record) | 修改行数据 | rows: Row \| Row[], record: object | Promise |
| reloadRow(rows, record, field) | 修改行数据并恢复到初始状态 | rows: Row \| Row[], record: object, field?: string | Promise |
| reloadRowExpand(row) | 重新懒加载展开行，并展开内容 | rows: Row | Promise |
| reloadTreeExpand(row) | 重新懒加载树节点，并展开该节点 | rows: Row | Promise |
| createRow(records) | 创建 Row,Rows 对象 | records: object \| array | Promise |
| createData(records) | 创建 data 对象 | records: array | Promise |
| insert(records) | 往表格插入临时数据，从第一行插入 | records?: object \| Array | Promise<{row, rows}> |
| insertAt(records, row) | 往表格插入临时数据，从指定位置插入 | records: object \| Array, row?: Row \| -1 \| 0 | Promise<{row, rows}> |
| insertNextAt(records, row) | 往表格插入临时数据，从指定位置的下一行插入 | records: object \| Array, row?: Row \| -1 \| 0 | Promise<{row, rows}> |
| insertChild(records, parentRow) | 往指定节点插入子级临时数据 | records: object \| Array, parentRow: Row | Promise<{row, rows}> |
| insertChildAt(records, parentRow, targetRow) | 往指定节点插入子级临时数据 | records: object \| Array, parentRow: Row, targetRow: Row | Promise<{row, rows}> |
| insertChildNextAt(records, parentRow, targetRow) | 与 insertChildAt 行为一致，区别就是会插入指定目标子级的到下一行 | records: object \| Array, parentRow: Row, targetRow: Row | Promise<{row, rows}> |
| revertData(rows, field) | 还原指定行或整个表格的数据 | rows: Row \| Array, field?: string | Promise |
| remove(rows) | 删除指定行数据 | rows: Row \| Array | Promise<{row, rows}> |
| removeCheckboxRow() | 删除复选框选中的行数据 | — | Promise<{row, rows}> |
| removeRadioRow() | 删除单选框选中的行数据 | — | Promise<{row, rows}> |
| removeCurrentRow() | 删除当前行选中的行数据 | — | Promise<{row, rows}> |
| removeInsertRow() | 删除新增的临时数据 | — | Promise<{row, rows}> |

### 列操作方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| loadColumn(columns) | 加载列配置 | columns: array | Promise |
| reloadColumn(columns) | 加载列配置并恢复到初始状态 | columns: array | Promise |
| refreshColumn() | 刷新列配置 | — | Promise |
| hideColumn(fieldOrColumns) | 隐藏指定列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[] | Promise |
| showColumn(fieldOrColumns) | 显示指定列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[] | Promise |
| setColumnFixed(fieldOrColumns, fixed) | 设置指定列为冻结列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[], fixed: string | Promise |
| clearColumnFixed(fieldOrColumns) | 取消指定的冻结列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[] | Promise |
| setColumnWidth(fieldOrColumns, width) | 设置列宽 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[], width: number \| string | Promise<{ status }> |
| getColumnWidth(fieldOrColumns) | 获取列宽 | fieldOrColumn: string \| ColumnConfig | number |
| moveColumnTo(fieldOrColumn, targetFieldOrColumn, options) | 移动列到指定列的位置 | fieldOrColumn: string \| ColumnConfig, targetFieldOrColumn: string \| ColumnConfig, options?: object | Promise<{ status }> |

### 获取数据方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getTableData() | 获取当前表格的数据 | — | {fullData, visibleData, tableData, footerData} |
| getFullData() | 获取完整的全量表体数据 | — | any[] |
| getData(rowIndex) | 获取数据，和 data 的行为一致 | rowIndex?: number | Array |
| getRecordset() | 获取表格数据集 | — | {insertRecords, removeRecords, updateRecords} |
| getInsertRecords() | 获取插入的临时数据 | — | Array |
| getRemoveRecords() | 获取已删除的数据 | — | Array |
| getUpdateRecords() | 获取已修改的数据 | — | Array |
| getPendingRecords() | 获取已标记删除的数据 | — | Array |

### 获取行列信息方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getParams() | 获取自定义的参数 | — | any |
| getRowSeq(row) | 根据 row 获取行的序号 | row: Row | string \| number |
| getRowIndex(row) | 根据 row 获取相对于 data 中的索引 | row: Row | number |
| getVTRowIndex(row) | 根据 row 获取相对于当前数据中的索引 | row: Row | number |
| getVMRowIndex(row) | 根据 row 获取渲染中的虚拟索引 | row: Row | number |
| getRowById(rowid) | 根据行的唯一主键获取行 | rowid: string | String |
| getRowid(row) | 根据行获取行的唯一主键 | row: Row | Row |
| getRowNode(tr) | 根据 tr 元素获取对应的 row 信息 | tr: Element | {item, items, index, parent} |
| getColumns() | 获取表格的可视的列 | — | Array |
| getFullColumns() | 获取表格的全量列 | — | Column[] |
| getColid(column) | 根据列获取列的唯一主键 | column: ColumnConfig | String |
| getColumnById(colid) | 根据列的唯一主键获取列 | colid: string | Column |
| getColumnByField(field) | 根据列的字段名获取列 | field: string | Column |
| getParentColumn(field) | 当存在多级表头时，用于获取父列配置 | field: string | Column |
| getTableColumn() | 获取当前表格的列 | — | {collectColumn, fullColumn, visibleColumn, tableColumn} |
| getColumnIndex(column) | 根据 column 获取相对于 columns 中的索引 | column: ColumnConfig | number |
| getVMColumnIndex(column) | 根据 column 获取渲染中的虚拟索引 | column | number |
| getVTColumnIndex(column) | 根据 column 获取相对于当前表格列中的索引 | column | number |
| getColumnNode(cell) | 根据 th/td 元素获取对应的 column 信息 | cell: HTMLTableDataCellElement \| HTMLTableHeaderCellElement | {item, items, index, parent} |

### 树形结构方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getTreeRowChildren(rowOrRowid) | 获取指定行的子级 | rowOrRowid: string \| Row | any[] |
| getTreeParentRow(rowOrRowid) | 获取指定行的父级 | rowOrRowid: string \| Row | any |
| isTreeExpandByRow(row) | 判断行是否为树形节点展开状态 | row: Row | boolean |
| isTreeExpandLoaded(row) | 判断树节点是否懒加载完成 | row: Row | boolean |
| setTreeExpand(rows, expanded) | 设置展开树形节点 | rows: Row \| Array, expanded: boolean | Promise |
| setAllTreeExpand(expanded) | 设置所有树节点的展开与否 | expanded: boolean | Promise |
| toggleTreeExpand(row) | 切换展开树形节点的状态 | row: Row | Promise |
| clearTreeExpand() | 手动清空树形节点的展开状态 | — | Promise |
| clearTreeExpandLoaded(rows) | 手动清空懒加载树节点的状态 | rows: any \| any[] | Promise |

### 选择相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setCurrentRow(row) | 设置指定行为高亮状态 | row: Row | Promise |
| getCurrentRecord() | 获取高亮的当前行数据 | — | Row |
| setCurrentColumn(fieldOrColumn) | 设置某列行为高亮状态 | fieldOrColumn: string \| VxeTableDefines.ColumnInfo | Promise |
| getCurrentColumn() | 获取当前列 | — | ColumnConfig |
| setRadioRow(row) | 设置指定行为选中状态 | row: any | Promise |
| setRadioRowKey(key) | 设置指定行为选中状态 | key: string | Promise |
| getRadioRecord(isFull) | 获取当前已选中的行数据 | — | Row |
| getRadioReserveRecord(isFull) | 获取已保留选中的行数据 | — | Row |
| setCheckboxRow(rows, checked) | 设置行为选中状态 | rows: Row \| Array, checked: boolean | Promise |
| setCheckboxRowKey(keys, checked) | 设置行为选中状态 | keys: string \| string[], checked: boolean | Promise |
| getCheckboxRecords(isFull) | 获取当前已选中的行数据 | — | Array |
| getCheckboxReserveRecords(isFull) | 获取已保留选中的行数据 | — | Array |
| getCheckboxIndeterminateRecords(isFull) | 获取半选状态的行数据 | — | Array |
| setAllCheckboxRow(checked) | 设置所有行的选中状态 | checked: boolean | Promise |
| toggleCheckboxRow(row) | 切换某一行的选中状态 | row: Row | Promise |
| toggleAllCheckboxRow() | 切换所有行的选中状态 | — | Promise |
| clearCurrentRow() | 手动清空当前高亮的状态 | — | Promise |
| clearCurrentColumn() | 手动清空当前高亮的状态 | — | Promise |
| clearRadioRow() | 手动清空用户的选择 | — | Promise |
| clearRadioReserve() | 手动清空用户保留选中的行数据 | — | Promise |
| clearCheckboxRow() | 手动清空用户的选择 | — | Promise |
| clearCheckboxReserve() | 手动清空用户保留选中的行数据 | — | Promise |

### 编辑相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setEditRow(row, fieldOrColumn) | 激活行编辑 | row: Row, fieldOrColumn?: string \| VxeTableDefines.ColumnInfo | Promise |
| setEditCell(row, fieldOrColumn) | 激活单元格编辑 | row: Row, fieldOrColumn: string \| VxeTableDefines.ColumnInfo | Promise |
| getEditCell() | 获取已激活编辑的单元格信息 | — | { row, column } |
| isEditByRow(row) | 判断行是否为激活编辑状态 | row | boolean |
| isInsertByRow(row) | 判断行是否为插入的临时数据 | row: Row | boolean |
| isUpdateByRow(row, field) | 判断行数据是否发生改变 | row: Row, field?: string | boolean |
| clearEdit() | 手动清除单元格激活状态 | — | Promise |

### 校验相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| validate(rows) | 快速校验 | rows?: boolean \| Row \| Row[] | Promise |
| fullValidate(rows) | 完整校验 | rows?: boolean \| Row \| Row[] | Promise |
| validateField(rows, fieldOrColumn) | 快速校验指定列 | rows?: boolean \| Row \| Row[], fieldOrColumn: string \| ColumnInfo \| string[] \| ColumnInfo[] | Promise |
| fullValidateField(rows, fieldOrColumn) | 完整校验指定列 | rows?: boolean \| Row \| Row[], fieldOrColumn: string \| ColumnInfo \| string[] \| ColumnInfo[] | Promise |
| clearValidate() | 手动清除校验 | — | Promise |

### 排序相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| sort(sortConfs, order) | 手动对表格进行排序 | sortConfs, order | Promise |
| setSort(sortConfs, update) | 手动对表格进行排序 | sortConfs, update?: boolean | Promise |
| clearSort(fieldOrColumn) | 手动清空排序条件 | fieldOrColumn?: string \| ColumnConfig | Promise |
| getSortColumns() | 获取当前排序的所有列信息 | — | any[] |
| isSort(fieldOrColumn) | 判断指定列是否为排序状态 | fieldOrColumn?: string \| ColumnInfo | boolean |

### 筛选相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setFilter(fieldOrColumn, options, update) | 修改筛选列的选项 | fieldOrColumn, options, update | Promise |
| openFilter(fieldOrColumn) | 手动弹出筛选面板 | fieldOrColumn: string \| ColumnInfo | Promise |
| clearFilter(fieldOrColumn) | 手动清空筛选条件 | fieldOrColumn?: string \| ColumnConfig | Promise |
| getCheckedFilters() | 获取当前筛选的所有列信息 | — | any[] |
| isFilter(fieldOrColumn) | 判断指定列是否为筛选状态 | fieldOrColumn?: string \| VxeTableDefines.ColumnInfo | boolean |
| closeFilter() | 手动关闭筛选面板 | — | Promise |

### 展开相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setRowExpand(rows, expanded) | 设置展开行 | rows: Row \| Array, expanded: boolean | Promise |
| setAllRowExpand(expanded) | 设置所有行的展开与否 | expanded: boolean | Promise |
| toggleRowExpand(row) | 切换展开行的状态 | row: Row | Promise |
| getRowExpandRecords() | 获取已展开的行数据 | — | Array |
| isRowExpandByRow(row) | 判断行是否为展开状态 | row | boolean |
| isRowExpandLoaded(row) | 判断展开行是否懒加载完成 | row | boolean |
| clearRowExpand() | 手动清空展开行状态 | — | Promise |
| clearRowExpandLoaded(row) | 手动清空懒加载展开行的状态 | row: any | Promise |

### 滚动相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| scrollTo(x, y) | 滚动到对应的位置 | x, y | Promise |
| scrollToRow(row, fieldOrColumn) | 滚动到对应的行 | row: Row, fieldOrColumn?: string \| ColumnConfig | Promise |
| scrollToColumn(fieldOrColumn) | 滚动到对应的列 | fieldOrColumn: string \| ColumnConfig | Promise |
| getScroll() | 获取表格的滚动状态 | — | { virtualX, virtualY, scrollTop, scrollLeft } |
| refreshScroll() | 刷新滚动操作 | — | Promise |
| clearScroll() | 手动清除滚动相关信息 | — | Promise |

### 导入导出方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| exportData(options) | 将表格数据导出 | options: object | Promise |
| openExport(options) | 打开高级导出 | options: object | Promise |
| closeExport() | 手动关闭导出面板 | — | Promise |
| importData(options) | 将数据导入表格 | options: object | Promise |
| openImport(options) | 打开高级导入 | options: object | Promise |
| closeImport() | 手动关闭导入面板 | — | Promise |
| print(options) | 打印 | options: object | Promise |
| openPrint(options) | 弹出打印面板 | options: object | Promise |
| closePrint() | 手动关闭打印面板 | — | Promise |
| getPrintHtml(options) | 获取打印的 HTML 标签 | options: object | Promise<{html}> |
| saveFile(options) | 保存文件到本地 | options: object | Promise |
| readFile(options) | 读取本地文件 | options: object | Promise<{ file, files }> |

### 个性化方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getCustomStoreData() | 获取个性化列设置信息 | — | any |
| toggleCustom() | 手动切换个性化列面板打开与关闭 | — | Promise |
| openCustom() | 手动弹出个性化列面板 | — | Promise |
| closeCustom() | 手动关闭个性化列面板 | — | Promise |
| cancelCustom() | 取消个性化列 | — | Promise |
| saveCustom() | 保存个性化列 | — | Promise |
| resetCustom(options) | 重置个性化列 | options: boolean \| object | Promise |

### Grid 特有方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| commitProxy(code, ...arguments) | 给数据代理提交指令 | code: string | — |
| getProxyInfo() | 获取数据代理信息 | — | {data, filter, form, sort, pager, pendingRecords} |
| resetForm() | 重置表单 | — | Promise |
| validateForm() | 对表单进行校验 | — | Promise |
| validateFormField(field) | 对表单指定项进行校验 | field: VxeFormItemPropTypes.Field \| VxeFormItemPropTypes.Field[] | Promise |
| clearFormValidate(field) | 手动清除表单校验状态 | field?: VxeFormItemPropTypes.Field \| VxeFormItemPropTypes.Field[] | Promise |
| getFormData() | 获取表单数据 | — | any |
| getFormItems(index) | 获取表单项列表 | index? number | Array |
| homePage() | 跳转首页 | — | Promise |
| endPage() | 跳转末页 | — | Promise |
| setCurrentPage(currentPage) | 修改每当前页数 | currentPage: number | Promise |
| setPageSize(pageSize) | 修改每页大小 | pageSize: number | Promise |
| zoom() | 切换表格最大化/还原 | — | Promise |
| isMaximized() | 判断是否最大化显示 | — | Boolean |
| maximize() | 如果表格处于常规状态，则最大化表格 | — | — |
| revert() | 如果表格处于最大化状态，则还原表格 | — | — |

### 其他方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| recalculate(refull) | 重新计算表格 | refull?: boolean | Promise |
| clearAll() | 手动清除表格所有条件，还原到初始状态 | — | Promise |
| focus() | 使表格获取焦点 | — | Promise |
| blur() | 使表格失去焦点 | — | Promise |
| openTooltip(target, content) | 打开 tooltip 提示 | target: HTMLElement, content: string \| number | Promise |
| closeTooltip() | 手动关闭 tooltip 提示 | — | Promise |
| closeMenu() | 手动关闭右键菜单 | — | Promise |

## 常见示例

### 基础配置式表格
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        columns: [
          { field: 'name', title: '姓名' },
          { field: 'age', title: '年龄' },
          { field: 'address', title: '地址' }
        ],
        data: [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ]
      }
    }
  }
}
</script>
```

### 带工具栏的表格
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        toolbarConfig: {
          buttons: [
            { code: 'insert_actived', name: '新增' },
            { code: 'delete', name: '删除' },
            { code: 'save', name: '保存' }
          ]
        },
        columns: [
          { type: 'checkbox', width: 50 },
          { field: 'name', title: '姓名', editRender: { name: 'input' } },
          { field: 'age', title: '年龄', editRender: { name: 'input' } },
          { field: 'address', title: '地址', editRender: { name: 'input' } }
        ],
        data: [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ],
        editConfig: {
          trigger: 'click',
          mode: 'row'
        }
      }
    }
  }
}
</script>
```

### 带分页的表格
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        pagerConfig: {
          enabled: true,
          currentPage: 1,
          pageSize: 10,
          total: 100
        },
        columns: [
          { field: 'name', title: '姓名' },
          { field: 'age', title: '年龄' },
          { field: 'address', title: '地址' }
        ],
        data: []
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      // 模拟异步加载数据
      setTimeout(() => {
        this.gridOptions.data = [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ]
      }, 100)
    }
  }
}
</script>
```

### 带表单查询的表格
```vue
<template>
  <vxe-grid v-bind="gridOptions" @form-submit="searchEvent"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        formConfig: {
          enabled: true,
          data: {
            name: '',
            age: ''
          },
          items: [
            { field: 'name', title: '姓名', itemRender: { name: 'input' } },
            { field: 'age', title: '年龄', itemRender: { name: 'input' } },
            {
              itemRender: {
                name: 'VxeButton',
                props: { type: 'submit', content: '查询' }
              }
            }
          ]
        },
        columns: [
          { field: 'name', title: '姓名' },
          { field: 'age', title: '年龄' },
          { field: 'address', title: '地址' }
        ],
        data: [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ]
      }
    }
  },
  methods: {
    searchEvent({ data }) {
      console.log('查询条件：', data)
      // 执行查询逻辑
    }
  }
}
</script>
```

### 数据代理表格
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        proxyConfig: {
          ajax: {
            query: ({ page, sort, filters, form }) => {
              return new Promise(resolve => {
                setTimeout(() => {
                  const list = [
                    { id: 1, name: '张三', age: 25, address: '北京市' },
                    { id: 2, name: '李四', age: 30, address: '上海市' }
                  ]
                  resolve({
                    page: {
                      total: list.length
                    },
                    result: list
                  })
                }, 100)
              })
            },
            save: ({ body }) => {
              return new Promise(resolve => {
                setTimeout(() => {
                  console.log('保存数据：', body)
                  resolve()
                }, 100)
              })
            }
          }
        },
        toolbarConfig: {
          buttons: [
            { code: 'query', name: '查询' },
            { code: 'save', name: '保存' }
          ]
        },
        pagerConfig: {
          enabled: true
        },
        columns: [
          { type: 'checkbox', width: 50 },
          { field: 'name', title: '姓名', editRender: { name: 'input' } },
          { field: 'age', title: '年龄', editRender: { name: 'input' } },
          { field: 'address', title: '地址', editRender: { name: 'input' } }
        ],
        editConfig: {
          trigger: 'click',
          mode: 'row'
        }
      }
    }
  }
}
</script>
```

### 树形表格
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        treeConfig: {
          transform: true,
          rowField: 'id',
          parentField: 'parentId'
        },
        columns: [
          { field: 'name', title: '名称', treeNode: true },
          { field: 'size', title: '大小' },
          { field: 'type', title: '类型' },
          { field: 'date', title: '修改日期' }
        ],
        data: [
          { id: 1, parentId: null, name: '文档', type: '文件夹', date: '2020-02-20' },
          { id: 2, parentId: 1, name: '表格.xlsx', size: '2MB', type: 'Excel', date: '2020-02-20' },
          { id: 3, parentId: 1, name: '文档.docx', size: '1MB', type: 'Word', date: '2020-02-20' }
        ]
      }
    }
  }
}
</script>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.14.8
- 尝鲜版本：4.15.0-beta.12

## 相关链接

- [官方文档](https://vxetable.cn/v4/)
- [GitHub](https://github.com/x-extends/vxe-table)
- [Gitee](https://gitee.com/x-extends/vxe-table)
