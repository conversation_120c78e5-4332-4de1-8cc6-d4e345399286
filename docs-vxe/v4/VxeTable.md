# VxeTable 基础表格

VxeTable 是一个功能强大的 Vue 表格组件，支持虚拟滚动、树形结构、增删改查、导入导出、打印、校验、自定义模板、快捷菜单、数据代理、键盘导航、模态窗口、自定义渲染、数据分页、虚拟列表、数据校验、Excel 导入导出等丰富功能。是目前 Vue 生态系统中功能最完整的表格组件。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/es/style.css'

const app = createApp()
app.use(VxeUI)
```

### 局部引入
```javascript
import { VxeTable } from 'vxe-pc-ui'
```

## 基础用法

```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市' },
        { name: '李四', age: 30, address: '上海市' }
      ]
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| id | 唯一标识（被某些特定的功能所依赖） | string | — | — | — |
| data | 表格数据（与 loadData 行为一致，更新数据是不会重置状态） | any[] | — | — | — |
| height | 表格的高度；支持铺满父容器或者固定高度 | number \| string | %, px | — | — |
| min-height | 表格最小高度 | number \| string | %, px | 默认 144，继承 setConfig.table.minHeight | vxe-table@4.5.0 |
| max-height | 表格的最大高度 | number \| string | %, px | — | — |
| auto-resize | 自动监听父元素的变化去重新计算表格 | boolean | — | 默认 true，继承 setConfig.table.autoResize | — |
| sync-resize | 自动跟随某个属性的变化去重新计算表格 | boolean \| string \| number | — | — | — |
| loading | 表格是否显示加载中 | boolean | — | false | — |
| empty-text | 空数据时显示的内容 | string | — | — | — |
| empty-render | 空内容渲染配置项 | object | — | — | — |

### 外观配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| stripe | 是否带有斑马纹样式 | boolean | — | false | — |
| border | 是否带有边框 | boolean \| string | default, full, outer, inner, none | 默认 false，继承 setConfig.table.border | — |
| round | 是否为圆角边框 | boolean | — | false | — |
| size | 表格的尺寸 | string | medium, small, mini | 继承上下文 | — |
| fit | 列的宽度是否自撑开（可能会被某些特定的功能所影响） | boolean | — | 默认 true，继承 setConfig.table.fit | — |
| auto-width | 是否自动计算列宽（如果设置为 true 则不支持横向虚拟滚动，如果设置为 false 则支持横向虚拟滚动） | boolean | — | false | — |

### 对齐方式

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| align | 所有的列对齐方式 | string | left, center, right | left | — |
| header-align | 所有的表头列的对齐方式 | string | left, center, right | 继承 align | — |
| footer-align | 所有的表尾列的对齐方式 | string | left, center, right | 继承 align | — |

### 显示控制

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| show-header | 是否显示表头 | boolean | — | 默认 true，继承 setConfig.table.showHeader | — |
| show-footer | 是否显示表尾 | boolean | — | false | — |
| show-overflow | 设置所有内容过长时显示为省略号 | boolean \| string | ellipsis（只显示省略号）, title（并且显示为原生 title）, tooltip（并且显示为 tooltip 提示） | 默认 ellipsis，继承 setConfig.table.showOverflow | — |
| show-header-overflow | 设置表头所有内容过长时显示为省略号 | boolean \| string | ellipsis, title, tooltip | 继承 show-overflow | — |
| show-footer-overflow | 设置表尾所有内容过长时显示为省略号 | boolean \| string | ellipsis, title, tooltip | 继承 show-overflow | — |

### 样式配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| row-class-name | 给行附加 className | string \| Function | — | — | — |
| cell-class-name | 给单元格附加 className | string \| Function | — | — | — |
| header-row-class-name | 给表头的行附加 className | string \| Function | — | — | — |
| header-cell-class-name | 给表头的单元格附加 className | string \| Function | — | — | — |
| footer-row-class-name | 给表尾的行附加 className | string \| Function | — | — | — |
| footer-cell-class-name | 给表尾的单元格附加 className | string \| Function | — | — | — |
| row-style | 给行附加样式 | object \| Function | — | — | — |
| cell-style | 给单元格附加样式 | object \| Function | — | — | — |
| header-row-style | 给表头的行附加样式 | object \| Function | — | — | — |
| header-cell-style | 给表头的单元格附加样式 | object \| Function | — | — | — |
| footer-row-style | 给表尾的行附加样式 | object \| Function | — | — | — |
| footer-cell-style | 给表尾的单元格附加样式 | object \| Function | — | — | — |

### 数据配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| footer-data | 表尾数据 | any[] | — | — | — |
| footer-method | 表尾的数据获取方法，返回一个二维数组 | Function | — | — | — |
| keep-source | 保持原始值的状态，被某些功能所依赖 | boolean | — | false | — |
| row-id | 自定义行数据唯一主键的字段名（行数据必须要有唯一主键，默认自动生成） | string | — | 默认 _X_ROW_KEY，继承 setConfig.table.rowId | — |
| row-key | 是否需要为每一行的 VNode 设置 key 属性（非特殊情况下不需要使用） | boolean | — | false | — |
| column-key | 是否需要为每一列的 VNode 设置 key 属性（非特殊情况下不需要使用） | boolean | — | false | — |

### 合并配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| merge-cells | 临时合并指定的单元格 | Array | — | — | — |
| merge-footer-items | 临时合并表尾 | Array | — | — | — |
| span-method | 自定义合并函数 | Function | — | — | — |
| footer-span-method | 表尾合并行或列 | Function | — | — | — |

### 虚拟滚动配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| scroll-y | 纵向虚拟滚动配置（用于优化渲染性能） | object | — | — | — |
| scroll-x | 横向虚拟滚动配置（用于优化渲染性能） | object | — | — | — |

## 功能配置项

### 编辑配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| edit-config | 可编辑配置项 | object | — | — | — |
| edit-rules | 校验规则配置项 | object | — | — | — |
| valid-config | 校验配置项 | object | — | — | — |

### 列配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| column-config | 列配置信息 | object | — | — | — |

### 行配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| row-config | 行配置信息 | object | — | — | — |

### 序号配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| seq-config | 序号配置项 | object | — | — | — |

### 排序配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| sort-config | 排序配置项 | object | — | — | — |

### 筛选配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| filter-config | 筛选配置项 | object | — | — | — |

### 导入导出配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| import-config | 导入配置项 | object | — | — | — |
| export-config | 导出配置项 | object | — | — | — |
| print-config | 打印配置项 | object | — | — | — |

### 拖拽配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| row-drag-config | 行拖拽配置项 | object | — | — | vxe-table@4.10.4 |
| column-drag-config | 列拖拽配置项 | object | — | — | vxe-table@4.11.19 |

### 调整配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| resizable-config | 列宽拖动配置项 | object | — | — | — |

### 选择配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| radio-config | 单选框配置项 | object | — | — | — |
| checkbox-config | 复选框配置项 | object | — | — | — |

### 展开配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| expand-config | 展开行配置项（不支持虚拟滚动） | object | — | — | — |

### 树形配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| tree-config | 树形结构配置项 | object | — | — | — |

### 菜单配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| menu-config | 右键菜单配置项 | object | — | — | — |

### 鼠标配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| mouse-config | 鼠标配置项 | object | — | — | — |

### 键盘配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| keyboard-config | 键盘配置项 | object | — | — | — |

### 剪贴板配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| clip-config | 剪贴板配置项 | object | — | — | — |

### 查找替换配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| fnr-config | 查找替换配置项 | object | — | — | — |

### 提示配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| tooltip-config | tooltip 配置项 | object | — | — | — |

### 个性化配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| custom-config | 个性化信息配置项 | object | — | — | — |

### 滚动条配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| scrollbar-config | 滚动条配置项 | object | — | — | — |

### 其他配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
|------|------|------|--------|--------|------|
| loading-config | 加载中配置项 | object | — | — | — |
| params | 自定义参数（可以用来存放一些自定义的数据） | any | — | — | — |

## Slots 插槽

| 插槽名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| empty | 自定义空数据时显示模板 | {} | — |
| loading | 自定义加载中模板 | {} | vxe-table@4.3.7 |
| row-drag-icon | 自定义行拖拽按钮插槽模板 | { row, column } | vxe-table@4.10.4 |
| column-drag-icon | 个性化列拖拽按钮插槽模板 | { column } | vxe-table@4.11.19 |

## Events 事件

### 键盘事件

| 事件名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| keydown-start | 当表格被激活且键盘被按下开始时会触发的事件 | { $event } | — |
| keydown | 当表格被激活且键盘被按下时会触发的事件 | { $event } | — |
| keydown-end | 当表格被激活且键盘被按下结束时会触发的事件 | { $event } | — |

### 选择事件

| 事件名 | 说明 | 参数 | 版本 |
|--------|------|------|------|
| current-row-change | 当前行选中状态发生变化时触发的事件 | { newValue, oldValue, row, $event } | — |
| current-column-change | 当前列选中状态发生变化时触发的事件 | { newValue, oldValue, column, $event } | — |
| radio-change | 当手动勾选单选框并且值发生改变时触发的事件 | { newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } | — |
| checkbox-change | 当手动勾选复选框并且值发生改变时触发的事件 | { checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } | — |
| checkbox-all | 当手动勾选全选时触发的事件 | { checked, $event } | — |

### 单元格事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| cell-click | 单元格被点击时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event } |
| cell-dblclick | 单元格被双击时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-menu | 单元格被鼠标右键时触发该事件 | { type, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-mouseenter | 当鼠标移动到单元格时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| cell-mouseleave | 当鼠标移开单元格时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |

### 表头事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| header-cell-click | 表头单元格被点击时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, triggerResizable, triggerSort, triggerFilter, $event } |
| header-cell-dblclick | 表头单元格被双击时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, $event } |
| header-cell-menu | 表头单元格被鼠标右键时触发该事件 | { type, column, columnIndex, $event } |

### 表尾事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| footer-cell-click | 表尾单元格被点击时会触发该事件 | { items, $rowIndex, column, columnIndex, $columnIndex, $event } |
| footer-cell-dblclick | 表尾单元格被双击时会触发该事件 | { items, $rowIndex, column, columnIndex, $columnIndex, $event } |
| footer-cell-menu | 表尾单元格被鼠标右键时触发该事件 | { type, column, columnIndex, $event } |

### 排序事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| sort-change | 当排序条件发生变化时会触发该事件 | { column, field, order, sortBy, sortList, $event } |
| clear-sort | 当用户点击清除所有排序时会触发该事件 | { sortList, $event } |

### 筛选事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| filter-change | 当筛选条件发生变化时会触发该事件 | { column, field, values, datas, filterList, $event } |
| filter-visible | 当筛选面板被触发时会触发该事件 | { column, field, visible, filterList, $event } |
| clear-filter | 当用户点击清除所有筛选条件时会触发该事件 | { filterList, $event } |

### 调整事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| column-resizable-change | 当列宽拖动发生变化时会触发该事件 | { $rowIndex, column, columnIndex, $columnIndex, $event } |
| row-resizable-change | 当行高拖动发生变化时会触发该事件 | { row, $rowIndex, rowIndex, column, columnIndex, $columnIndex, $event } |

### 展开事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| toggle-row-expand | 当行展开或收起时会触发该事件 | { expanded, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event } |
| toggle-tree-expand | 当树节点展开或收起时会触发该事件 | { expanded, row, column, columnIndex, $columnIndex, $event } |

### 菜单事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| menu-click | 当点击右键菜单时会触发该事件 | { menu, type, row, rowIndex, column, columnIndex, $event } |

### 编辑事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| edit-closed | 单元格编辑状态下被关闭时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |
| edit-activated | 单元格被激活编辑时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |
| edit-disabled | 当单元格激活时如果是禁用状态时会触发该事件 | { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |

### 校验事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| valid-error | 当数据校验不通过时会触发该事件 | { rule, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex } |

### 滚动事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| scroll | 表格滚动时会触发该事件 | { type, isTop, isBottom, isLeft, isRight, scrollTop, scrollLeft, scrollHeight, scrollWidth, bodyWidth, bodyHeight, isX, isY, $event } |

### 个性化事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| custom | 在个性化列按钮被手动点击后会触发该事件 | { type, $event } |

## Methods 方法

### 基础方法

| 方法名 | 说明 | 参数 | 返回值 | 版本 |
|--------|------|------|-------|------|
| getEl() | 获取容器元素 | — | HTMLDivElement | — |
| loadData(data) | 加载数据 | data: array | Promise | — |
| reloadData(data) | 加载数据并清除所有状态 | data: array | Promise | — |
| updateData() | 手动处理数据，用于手动排序与筛选 | — | Promise | — |
| syncData() | 同步 data 数据；如果用了该方法，那么组件将不再记录增删改的状态，只能自行实现对应逻辑 | — | Promise | — |

### 行操作方法

| 方法名 | 说明 | 参数 | 返回值 | 版本 |
|--------|------|------|-------|------|
| setRow(rows, record) | 修改行数据 | rows: Row \| Row[], record: object | Promise | — |
| reloadRow(rows, record, field) | 修改行数据并恢复到初始状态 | rows: Row \| Row[], record: object, field?: string | Promise | — |
| createRow(records) | 创建 Row,Rows 对象 | records: object \| array | Promise | — |
| insert(records) | 往表格插入临时数据，从第一行插入 | records?: object \| Array | Promise<{row, rows}> | — |
| insertAt(records, row) | 往表格插入临时数据，从指定位置插入 | records: object \| Array, row?: Row \| -1 \| 0 | Promise<{row, rows}> | — |
| remove(rows) | 删除指定行数据 | rows: Row \| Array | Promise<{row, rows}> | — |
| removeCheckboxRow() | 删除复选框选中的行数据 | — | Promise<{row, rows}> | — |
| removeRadioRow() | 删除单选框选中的行数据 | — | Promise<{row, rows}> | — |
| removeCurrentRow() | 删除当前行选中的行数据 | — | Promise<{row, rows}> | — |

### 列操作方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| loadColumn(columns) | 加载列配置 | columns: array | Promise |
| reloadColumn(columns) | 加载列配置并恢复到初始状态 | columns: array | Promise |
| refreshColumn() | 刷新列配置 | — | Promise |
| hideColumn(fieldOrColumns) | 隐藏指定列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[] | Promise |
| showColumn(fieldOrColumns) | 显示指定列 | fieldOrColumn: string \| ColumnConfig \| string[] \| ColumnConfig[] | Promise |

### 获取数据方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getTableData() | 获取当前表格的数据 | — | {fullData, visibleData, tableData, footerData} |
| getData(rowIndex) | 获取数据，和 data 的行为一致 | rowIndex?: number | Array |
| getRecordset() | 获取表格数据集 | — | {insertRecords, removeRecords, updateRecords} |
| getInsertRecords() | 获取插入的临时数据 | — | Array |
| getRemoveRecords() | 获取已删除的数据 | — | Array |
| getUpdateRecords() | 获取已修改的数据 | — | Array |

### 选择相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setCurrentRow(row) | 设置指定行为高亮状态 | row: Row | Promise |
| getCurrentRecord() | 获取高亮的当前行数据 | — | Row |
| setRadioRow(row) | 设置指定行为选中状态 | row: any | Promise |
| getRadioRecord(isFull) | 获取当前已选中的行数据 | — | Row |
| setCheckboxRow(rows, checked) | 设置行为选中状态 | rows: Row \| Array, checked: boolean | Promise |
| getCheckboxRecords(isFull) | 获取当前已选中的行数据 | — | Array |
| setAllCheckboxRow(checked) | 设置所有行的选中状态 | checked: boolean | Promise |
| toggleCheckboxRow(row) | 切换某一行的选中状态 | row: Row | Promise |
| toggleAllCheckboxRow() | 切换所有行的选中状态 | — | Promise |

### 编辑相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setEditRow(row, fieldOrColumn) | 激活行编辑 | row: Row, fieldOrColumn?: string \| ColumnInfo | Promise |
| setEditCell(row, fieldOrColumn) | 激活单元格编辑 | row: Row, fieldOrColumn: string \| ColumnInfo | Promise |
| getEditCell() | 获取已激活编辑的单元格信息 | — | { row, column } |
| isEditByRow(row) | 判断行是否为激活编辑状态 | row | boolean |
| clearEdit() | 手动清除单元格激活状态 | — | Promise |

### 校验相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| validate(rows) | 快速校验 | rows?: boolean \| Row \| Row[] | Promise |
| fullValidate(rows) | 完整校验 | rows?: boolean \| Row \| Row[] | Promise |
| clearValidate() | 手动清除校验 | — | Promise |

### 排序相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| sort(sortConfs, order) | 手动对表格进行排序 | sortConfs, order | Promise |
| clearSort(fieldOrColumn) | 手动清空排序条件 | fieldOrColumn?: string \| ColumnConfig | Promise |
| getSortColumns() | 获取当前排序的所有列信息 | — | any[] |

### 筛选相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| setFilter(fieldOrColumn, options, update) | 修改筛选列的选项 | fieldOrColumn, options, update | Promise |
| clearFilter(fieldOrColumn) | 手动清空筛选条件 | fieldOrColumn?: string \| ColumnConfig | Promise |
| getCheckedFilters() | 获取当前筛选的所有列信息 | — | any[] |

### 滚动相关方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| scrollTo(x, y) | 滚动到对应的位置 | x, y | Promise |
| scrollToRow(row, fieldOrColumn) | 滚动到对应的行 | row: Row, fieldOrColumn?: string \| ColumnConfig | Promise |
| scrollToColumn(fieldOrColumn) | 滚动到对应的列 | fieldOrColumn: string \| ColumnConfig | Promise |
| getScroll() | 获取表格的滚动状态 | — | { virtualX, virtualY, scrollTop, scrollLeft } |

### 导入导出方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| exportData(options) | 将表格数据导出 | options: object | Promise |
| openExport(options) | 打开高级导出 | options: object | Promise |
| importData(options) | 将数据导入表格 | options: object | Promise |
| openImport(options) | 打开高级导入 | options: object | Promise |
| print(options) | 打印 | options: object | Promise |
| openPrint(options) | 弹出打印面板 | options: object | Promise |

### 其他方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| recalculate(refull) | 重新计算表格 | refull?: boolean | Promise |
| refreshScroll() | 刷新滚动操作 | — | Promise |
| clearAll() | 手动清除表格所有条件，还原到初始状态 | — | Promise |
| focus() | 使表格获取焦点 | — | Promise |
| blur() | 使表格失去焦点 | — | Promise |

## 常见示例

### 基础表格
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 28, address: '北京市朝阳区' },
        { name: '李四', age: 32, address: '上海市浦东新区' },
        { name: '王五', age: 26, address: '广州市天河区' }
      ]
    }
  }
}
</script>
```

### 带边框的表格
```vue
<template>
  <vxe-table :data="tableData" border>
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 斑马纹表格
```vue
<template>
  <vxe-table :data="tableData" stripe>
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 固定高度表格
```vue
<template>
  <vxe-table :data="tableData" height="400">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 加载状态
```vue
<template>
  <vxe-table :data="tableData" :loading="loading">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        // 模拟异步加载数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.tableData = [
          { name: '张三', age: 28, address: '北京市朝阳区' },
          { name: '李四', age: 32, address: '上海市浦东新区' }
        ]
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```

### 复选框选择
```vue
<template>
  <vxe-table :data="tableData" :checkbox-config="{ checkField: 'checked' }">
    <vxe-column type="checkbox" width="60"></vxe-column>
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 单选框选择
```vue
<template>
  <vxe-table :data="tableData" :radio-config="{ checkField: 'selected' }">
    <vxe-column type="radio" width="60"></vxe-column>
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 排序功能
```vue
<template>
  <vxe-table :data="tableData" :sort-config="{ trigger: 'cell' }">
    <vxe-column field="name" title="姓名" sortable></vxe-column>
    <vxe-column field="age" title="年龄" sortable></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>
```

### 筛选功能
```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名" :filters="nameFilters" :filter-method="filterNameMethod"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 28, address: '北京市朝阳区' },
        { name: '李四', age: 32, address: '上海市浦东新区' },
        { name: '王五', age: 26, address: '广州市天河区' }
      ],
      nameFilters: [
        { label: '张三', value: '张三' },
        { label: '李四', value: '李四' },
        { label: '王五', value: '王五' }
      ]
    }
  },
  methods: {
    filterNameMethod({ value, row }) {
      return row.name === value
    }
  }
}
</script>
```

## 高级用法

### 虚拟滚动
```vue
<template>
  <vxe-table
    :data="tableData"
    height="400"
    :scroll-y="{ enabled: true, gt: 100 }"
    :scroll-x="{ enabled: true, gt: 10 }">
    <vxe-column field="name" title="姓名" width="150"></vxe-column>
    <vxe-column field="age" title="年龄" width="100"></vxe-column>
    <vxe-column field="address" title="地址" width="200"></vxe-column>
    <!-- 更多列... -->
  </vxe-table>
</template>
```

### 树形结构
```vue
<template>
  <vxe-table
    :data="tableData"
    :tree-config="{ children: 'children', indent: 20, showIcon: true }">
    <vxe-column field="name" title="名称" tree-node></vxe-column>
    <vxe-column field="size" title="大小"></vxe-column>
    <vxe-column field="type" title="类型"></vxe-column>
  </vxe-table>
</template>
```

### 可编辑表格
```vue
<template>
  <vxe-table
    :data="tableData"
    :edit-config="{ trigger: 'click', mode: 'cell' }">
    <vxe-column field="name" title="姓名" :edit-render="{ name: 'VxeInput' }"></vxe-column>
    <vxe-column field="age" title="年龄" :edit-render="{ name: 'VxeNumberInput' }"></vxe-column>
    <vxe-column field="address" title="地址" :edit-render="{ name: 'VxeInput' }"></vxe-column>
  </vxe-table>
</template>
```

## 最佳实践

### 1. 性能优化
- 对于大数据量，启用虚拟滚动
- 合理设置列宽，避免频繁重新计算
- 使用 `keep-source` 属性保持原始数据状态

### 2. 数据处理
- 使用 `loadData` 方法加载数据
- 使用 `reloadData` 方法重置表格状态
- 合理使用 `syncData` 方法同步数据

### 3. 事件处理
- 合理使用表格事件，避免性能问题
- 在组件销毁时清理事件监听器

## 常见问题

### Q: 如何实现表格的动态高度？
A: 可以使用 `max-height` 属性或者监听容器变化：
```vue
<vxe-table :data="tableData" :auto-resize="true" max-height="400">
```

### Q: 如何自定义单元格渲染？
A: 可以使用插槽或者渲染器：
```vue
<vxe-column field="status" title="状态">
  <template #default="{ row }">
    <span :class="row.status === 'active' ? 'text-green' : 'text-red'">
      {{ row.status === 'active' ? '激活' : '禁用' }}
    </span>
  </template>
</vxe-column>
```

### Q: 如何处理表格的校验？
A: 使用 `edit-rules` 配置校验规则：
```javascript
const editRules = {
  name: [
    { required: true, message: '请输入姓名' }
  ],
  age: [
    { type: 'number', min: 18, max: 65, message: '年龄必须在18-65之间' }
  ]
}
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.7.30
- 依赖：vxe-pc-ui

## 相关链接

- [官方文档](https://vxeui.com/)
- [GitHub](https://github.com/x-extends/vxe-pc-ui)
- [Gitee](https://gitee.com/x-extends/vxe-pc-ui)
- [VxeTable 在线示例](https://vxeui.com/#/table/base)
- [VxeTable API 文档](https://vxeui.com/#/table/api)
