# VxeToolbar 工具栏

VxeToolbar 是一个功能强大的工具栏组件，通常与 VxeTable 或 VxeGrid 配合使用，提供导入、导出、打印、刷新、个性化列等常用功能按钮。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

const app = createApp()
app.use(VxeTable)
```

### 局部引入
```javascript
import { VxeToolbar } from 'vxe-table'
```

## 基础用法

```vue
<template>
  <div>
    <vxe-toolbar>
      <template #buttons>
        <vxe-button @click="insertEvent">新增</vxe-button>
        <vxe-button @click="removeEvent">删除</vxe-button>
        <vxe-button @click="saveEvent">保存</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-table ref="xTable" :data="tableData">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="name" title="姓名"></vxe-column>
      <vxe-column field="age" title="年龄"></vxe-column>
      <vxe-column field="address" title="地址"></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市' },
        { name: '李四', age: 30, address: '上海市' }
      ]
    }
  },
  methods: {
    insertEvent() {
      console.log('新增')
    },
    removeEvent() {
      console.log('删除')
    },
    saveEvent() {
      console.log('保存')
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| size | 尺寸 | string | medium, small, mini | 继承上下文 |
| loading | 是否加载中 | boolean | — | false |
| class-name | 给工具栏 className | string \| Function | — | — |

### 按钮配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| buttons | 左侧按钮列表 | any[] | — | 默认继承 setConfig.toolbar.buttons |
| tools | 右侧工具列表 | any[] | — | 默认继承 setConfig.toolbar.tools |

### 导入配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| import | 导入按钮配置（需要设置 "import-config"） | boolean | — | false |
| import-options | 导入参数配置项 | object | — | 默认继承 setConfig.toolbar.importOptions |

### 导出配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| export | 导出按钮配置（需要设置 "export-config"） | boolean | — | false |
| export-options | 导出参数配置项 | object | — | 默认继承 setConfig.toolbar.exportOptions |

### 打印配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| print | 打印按钮配置 | boolean | — | false |
| print-options | 打印参数配置项 | object | — | 默认继承 setConfig.toolbar.printOptions |

### 刷新配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| refresh | 刷新按钮配置 | boolean | — | false |
| refresh-options | 刷新参数配置项 | object | — | 默认继承 setConfig.toolbar.refreshOptions |

### 个性化配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| custom | 个性化列配置 | boolean | — | false |
| custom-options | 个性化列参数配置项 | object | — | 默认继承 setConfig.toolbar.customOptions |

## Slots 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| buttons | 按钮列表 | {} |
| button-prefix | 自定义左侧按钮列表前缀插槽模板 | { buttons } |
| button-suffix | 自定义左侧按钮列表后缀插槽模板 | { buttons } |
| tools | 右侧工具列表 | {} |
| tool-prefix | 自定义右侧工具列表前缀插槽模板 | { tools } |
| tool-suffix | 自定义右侧工具列表后缀插槽模板 | { tools } |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| button-click | 只对 buttons 配置时有效，当左侧按钮被点击时会后触发该事件 | { code, button, $event } |
| tool-click | 只对 tools 配置时有效，当右侧工具被点击时会后触发该事件 | { code, tool, $event } |

## 常见示例

### 配置式工具栏
```vue
<template>
  <div>
    <vxe-toolbar
      :buttons="toolbarConfig.buttons"
      :tools="toolbarConfig.tools"
      @button-click="buttonClickEvent"
      @tool-click="toolClickEvent">
    </vxe-toolbar>
    <vxe-table ref="xTable" :data="tableData">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="name" title="姓名"></vxe-column>
      <vxe-column field="age" title="年龄"></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25 },
        { name: '李四', age: 30 }
      ],
      toolbarConfig: {
        buttons: [
          { code: 'insert', name: '新增' },
          { code: 'delete', name: '删除' },
          { code: 'save', name: '保存' }
        ],
        tools: [
          { code: 'refresh', name: '刷新' },
          { code: 'export', name: '导出' }
        ]
      }
    }
  },
  methods: {
    buttonClickEvent({ code }) {
      switch (code) {
        case 'insert':
          this.insertEvent()
          break
        case 'delete':
          this.deleteEvent()
          break
        case 'save':
          this.saveEvent()
          break
      }
    },
    toolClickEvent({ code }) {
      switch (code) {
        case 'refresh':
          this.refreshEvent()
          break
        case 'export':
          this.exportEvent()
          break
      }
    },
    insertEvent() {
      console.log('新增')
    },
    deleteEvent() {
      console.log('删除')
    },
    saveEvent() {
      console.log('保存')
    },
    refreshEvent() {
      console.log('刷新')
    },
    exportEvent() {
      console.log('导出')
    }
  }
}
</script>
```

### 插槽式工具栏
```vue
<template>
  <div>
    <vxe-toolbar>
      <template #buttons>
        <vxe-button status="primary" @click="insertEvent">新增</vxe-button>
        <vxe-button @click="removeEvent">删除</vxe-button>
        <vxe-button @click="saveEvent">保存</vxe-button>
      </template>
      <template #tools>
        <vxe-button @click="refreshEvent">刷新</vxe-button>
        <vxe-button @click="exportEvent">导出</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-table ref="xTable" :data="tableData">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="name" title="姓名"></vxe-column>
      <vxe-column field="age" title="年龄"></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25 },
        { name: '李四', age: 30 }
      ]
    }
  },
  methods: {
    insertEvent() {
      this.$refs.xTable.insert({ name: '', age: 0 })
    },
    removeEvent() {
      this.$refs.xTable.removeCheckboxRow()
    },
    saveEvent() {
      const { insertRecords, removeRecords, updateRecords } = this.$refs.xTable.getRecordset()
      console.log('新增：', insertRecords)
      console.log('删除：', removeRecords)
      console.log('修改：', updateRecords)
    },
    refreshEvent() {
      this.$refs.xTable.reloadData(this.tableData)
    },
    exportEvent() {
      this.$refs.xTable.exportData()
    }
  }
}
</script>
```

### 带功能按钮的工具栏
```vue
<template>
  <div>
    <vxe-toolbar
      import
      export
      print
      refresh
      custom
      @button-click="buttonClickEvent">
    </vxe-toolbar>
    <vxe-table
      ref="xTable"
      :data="tableData"
      :import-config="{}"
      :export-config="{}"
      :print-config="{}"
      :custom-config="{}">
      <vxe-column field="name" title="姓名"></vxe-column>
      <vxe-column field="age" title="年龄"></vxe-column>
      <vxe-column field="address" title="地址"></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市' },
        { name: '李四', age: 30, address: '上海市' }
      ]
    }
  },
  methods: {
    buttonClickEvent({ code }) {
      console.log('按钮点击：', code)
    }
  }
}
</script>
```

### 与 Grid 配合使用
```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        toolbarConfig: {
          buttons: [
            { code: 'insert_actived', name: '新增' },
            { code: 'delete', name: '删除' },
            { code: 'save', name: '保存' }
          ],
          tools: [
            { code: 'refresh', name: '刷新' },
            { code: 'export', name: '导出' }
          ]
        },
        columns: [
          { type: 'checkbox', width: 50 },
          { field: 'name', title: '姓名', editRender: { name: 'input' } },
          { field: 'age', title: '年龄', editRender: { name: 'input' } },
          { field: 'address', title: '地址', editRender: { name: 'input' } }
        ],
        data: [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ],
        editConfig: {
          trigger: 'click',
          mode: 'row'
        }
      }
    }
  }
}
</script>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.14.8
- 尝鲜版本：4.15.0-beta.12

## 相关链接

- [官方文档](https://vxetable.cn/v4/)
- [GitHub](https://github.com/x-extends/vxe-table)
- [Gitee](https://gitee.com/x-extends/vxe-table)
