# VxeButton 按钮

VxeButton 是一个功能丰富的按钮组件，支持多种样式、状态、图标、下拉菜单等功能，可以满足各种业务场景的需求。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/es/style.css'

const app = createApp()
app.use(VxeUI)
```

### 局部引入
```javascript
import { VxeButton } from 'vxe-pc-ui'
```

## 基础用法

```vue
<template>
  <div>
    <vxe-button>默认按钮</vxe-button>
    <vxe-button status="primary">主要按钮</vxe-button>
    <vxe-button status="success">成功按钮</vxe-button>
    <vxe-button status="info">信息按钮</vxe-button>
    <vxe-button status="warning">警告按钮</vxe-button>
    <vxe-button status="danger">危险按钮</vxe-button>
  </div>
</template>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| content | 内容（支持开启国际化） | string | — | — |
| mode | 按钮模式,支持文本和按钮 | string | text, button | 默认 button，继承 button-group.mode |
| title | 标题 | string | — | — |
| type | 原生按钮类型 | string | submit, reset | — |
| size | 尺寸 | string | medium, small, mini | 继承上下文 |
| name | 用来标识这一项 | string | — | — |

### 图标配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| icon | 前缀图标，参数 prefix-icon 的简写 | string | — | — |
| icon-render | 前缀图标，参数 prefix-render 的简写 | object | — | — |
| prefix-icon | 前缀图标 | string | — | — |
| prefix-render | 前缀图标渲染器配置项 | object | — | — |
| suffix-icon | 后缀图标 | string | — | — |
| suffix-render | 后缀图标渲染器配置项 | object | — | — |

### 样式配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| round | 圆角边框 | boolean | — | 默认 false，继承 button-group.round |
| circle | 圆角按钮 | boolean | — | 默认 false，继承 button-group.circle |
| status | 按钮的状态 | string | primary, success, info, warning, danger | 继承 button-group.status |
| class-name | 给展示容器附加 className | string \| Function | — | — |
| popup-class-name | 给下拉容器附加 className | string \| Function | — | — |

### 状态配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| disabled | 是否禁用 | boolean | — | false |
| loading | 是否加载中 | boolean | — | false |

### 权限配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| permission-code | 权限码 | string | — | — |

### 路由配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| router-link | 对应 vue-router 路由配置 | object | — | — |

### 下拉配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| placement | 固定显示下拉面板的方向 | string | top, bottom | bottom |
| destroy-on-close | 在下拉容器关闭时销毁内容 | boolean | — | false |
| trigger | 触发方式 | string | manual, hover, click | hover |
| transfer | 是否将弹框容器插入于 body 内（对于嵌入到表格或者弹窗中被遮挡时需要设置为 true） | boolean | — | 默认 false，继承 setConfig.button.transfer |
| options | 下拉按钮列表 | array | — | — |

## Slots 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 按钮内容 | {} |
| icon | 被 prefix 替换 | {} |
| prefix | 自定义前缀模板 | {} |
| suffix | 自定义后缀模板 | {} |
| dropdowns | 下拉按钮 | {} |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 在单击按钮时触发该事件 | { $event } |
| dropdown-click | 下拉列表按钮点击时会触发该事件 | { name, option, $event } |

## Methods 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| focus() | 使按钮获取焦点 | — | Promise |
| blur() | 使按钮失去焦点 | — | Promise |

## 常见示例

### 基础按钮
```vue
<template>
  <div>
    <vxe-button>默认按钮</vxe-button>
    <vxe-button status="primary">主要按钮</vxe-button>
    <vxe-button status="success">成功按钮</vxe-button>
    <vxe-button status="warning">警告按钮</vxe-button>
    <vxe-button status="danger">危险按钮</vxe-button>
  </div>
</template>
```

### 不同尺寸
```vue
<template>
  <div>
    <vxe-button size="mini">迷你按钮</vxe-button>
    <vxe-button size="small">小型按钮</vxe-button>
    <vxe-button size="medium">中等按钮</vxe-button>
    <vxe-button>默认按钮</vxe-button>
  </div>
</template>
```

### 带图标的按钮
```vue
<template>
  <div>
    <vxe-button icon="vxe-icon-search">搜索</vxe-button>
    <vxe-button icon="vxe-icon-edit" status="primary">编辑</vxe-button>
    <vxe-button icon="vxe-icon-delete" status="danger">删除</vxe-button>
    <vxe-button suffix-icon="vxe-icon-arrow-down">下拉</vxe-button>
  </div>
</template>
```

### 圆角按钮
```vue
<template>
  <div>
    <vxe-button round>圆角按钮</vxe-button>
    <vxe-button round status="primary">主要按钮</vxe-button>
    <vxe-button circle icon="vxe-icon-search"></vxe-button>
    <vxe-button circle icon="vxe-icon-edit" status="primary"></vxe-button>
  </div>
</template>
```

### 禁用状态
```vue
<template>
  <div>
    <vxe-button disabled>禁用按钮</vxe-button>
    <vxe-button disabled status="primary">禁用主要按钮</vxe-button>
    <vxe-button disabled icon="vxe-icon-search">禁用图标按钮</vxe-button>
  </div>
</template>
```

### 加载状态
```vue
<template>
  <div>
    <vxe-button :loading="loading" @click="handleClick">
      {{ loading ? '加载中...' : '点击加载' }}
    </vxe-button>
    <vxe-button :loading="loading" status="primary" @click="handleClick">
      提交
    </vxe-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false
    }
  },
  methods: {
    handleClick() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 2000)
    }
  }
}
</script>
```

### 文本按钮
```vue
<template>
  <div>
    <vxe-button mode="text">文本按钮</vxe-button>
    <vxe-button mode="text" status="primary">主要文本</vxe-button>
    <vxe-button mode="text" status="success">成功文本</vxe-button>
    <vxe-button mode="text" status="warning">警告文本</vxe-button>
    <vxe-button mode="text" status="danger">危险文本</vxe-button>
  </div>
</template>
```

### 下拉按钮
```vue
<template>
  <div>
    <vxe-button 
      :options="dropdownOptions" 
      @dropdown-click="handleDropdownClick">
      下拉按钮
    </vxe-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dropdownOptions: [
        { name: 'option1', content: '选项1' },
        { name: 'option2', content: '选项2' },
        { name: 'option3', content: '选项3' }
      ]
    }
  },
  methods: {
    handleDropdownClick({ name, option }) {
      console.log('选择了：', name, option)
    }
  }
}
</script>
```

### 按钮组合
```vue
<template>
  <div>
    <vxe-button @click="handleSave" status="primary">保存</vxe-button>
    <vxe-button @click="handleCancel">取消</vxe-button>
    <vxe-button @click="handleReset" status="info">重置</vxe-button>
  </div>
</template>

<script>
export default {
  methods: {
    handleSave() {
      console.log('保存')
    },
    handleCancel() {
      console.log('取消')
    },
    handleReset() {
      console.log('重置')
    }
  }
}
</script>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.7.30
- 依赖：vxe-pc-ui

## 相关链接

- [官方文档](https://vxeui.com/)
- [GitHub](https://github.com/x-extends/vxe-pc-ui)
- [Gitee](https://gitee.com/x-extends/vxe-pc-ui)
