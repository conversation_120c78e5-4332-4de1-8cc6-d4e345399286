# VxeInput 输入框

VxeInput 是一个功能丰富的输入框组件，支持多种输入类型、校验、清除、搜索等功能，可以满足各种表单输入需求。

## 安装与引入

### 全局安装
```javascript
import { createApp } from 'vue'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/es/style.css'

const app = createApp()
app.use(VxeUI)
```

### 局部引入
```javascript
import { VxeInput } from 'vxe-pc-ui'
```

## 基础用法

```vue
<template>
  <div>
    <vxe-input v-model="value" placeholder="请输入内容"></vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: ''
    }
  }
}
</script>
```

## Props 配置项

### 基础配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| modelValue | v-model 绑定值 | string | — | — |
| size | 尺寸 | string | medium, small, mini | 继承上下文 |
| title | 标题提示 | string | — | — |
| type | 渲染类型 | string | text, search | text |
| name | 原生 name 属性 | string | — | — |
| form | 原生 form 属性 | string | — | — |

### 输入配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| trim | 自动去掉左右空格 | boolean | — | false |
| clearable | 当有值时，是否在右侧显示清除按钮 | boolean | — | 默认 false，继承 setConfig.input.clearable |
| placeholder | 当值为空时，显示的占位符 | string | — | 请输入 |
| autocomplete | 原生 autocomplete 属性 | string | — | off |
| maxlength | 最大长度 | number | — | — |
| max-length | 最大长度 | number | — | — |
| readonly | 是否只读 | boolean | — | false |
| disabled | 是否禁用 | boolean | — | false |

### 样式配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| className | 附加 className | string | — | — |
| prefix-icon | 头部图标 | string | — | — |
| suffix-icon | 尾部图标 | string | — | — |
| align | 内容对齐方式 | string | left, center, right | left |

### 数字输入配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| min | 只对 type=number,integer,float,amount 有效，最小值 | number | — | — |
| max | 只对 type=number,integer,float,amount 有效，最大值 | number | — | — |
| step | 只对 type=number,integer,float,amount 有效，数字间隔 | number | — | 1 |
| digits | 只对 type=float,amount 有效，小数位数 | number | — | 默认 2，继承 setConfig.input.digits |
| exponential | 只对 type=number,integer,float,amount 有效，数值是否允许输入科学计数 | boolean | — | 默认 false，继承 setConfig.input.exponential |
| controls | 只对 type=number,integer,float,amount 有效，是否显示控制按钮 | boolean | — | 默认 true，继承 setConfig.input.controls |

### 字数统计配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| showWordCount | 只对 type=text,search 有效，是否显示字数统计 | boolean | — | false |
| countMethod | 只对 showWordCount 有效，自定义字数计算方法 | Function | — | false |

### 日期配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| start-date | 只对 type=date,datetime,week,month,quarter,year 有效，设置面板起始日期 | Date | — | 默认 new Date(1900, 0, 1)，继承 setConfig.input.startDate |
| end-date | 只对 type=date,datetime,week,month,quarter,year 有效，设置面板结束日期 | Date | — | 默认 new Date(2100, 0, 1)，继承 setConfig.input.endDate |
| start-day | 设置每周的起始日期是星期几 | number | 0, 1, 2, 3, 4, 5, 6 | 默认 1，继承 setConfig.input.startDay |
| select-day | 只对 type=week 有效，设置周视图选中后返回星期几 | number | 0, 1, 2, 3, 4, 5, 6 | 默认 1，继承 setConfig.input.selectDay |
| label-format | 只对 type=date,datetime,week,month,quarter,year 有效，输入框中显示的日期格式 | string | — | 继承 setConfig.input.labelFormat |
| value-format | 只对 type=date,datetime,week,month,quarter,year 有效，绑定值的返回格式，默认返回 Date 类型，如果指定格式则返回字符串 | string | — | 继承 setConfig.input.valueFormat |
| editable | 只对 type=date,time,datetime,week,month,quarter,year 有效，文本框是否允许输入 | boolean | — | 默认 true，继承 setConfig.input.editable |
| disabled-method | 只对 type=date,datetime,week,month,quarter,year 有效，该方法的返回值用来决定该日期是否允许选中 | Function | — | — |
| festival-method | 只对 type=date,datetime,week,month,quarter,year 有效，该方法用于返回对应日期显示的节日 | Function | — | — |

### 高级配置

| 属性 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| multiple | 只对 type=date,week,month,quarter,year 有效，是否启用多选 | boolean | — | false |
| transfer | 是否将弹框容器插入于 body 内（对于嵌入到表格或者弹窗中被遮挡时需要设置为 true） | boolean | — | 默认 false，继承 setConfig.input.transfer |

## Slots 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| prefix | 前缀图标模板 | {} |
| suffix | 后缀图标模板 | {} |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 在键盘输入时触发该事件 | { value, $event } |
| change | 在键盘输入时值发生变化时触发该事件 | { value, $event } |
| keydown | 在键盘输入按下时触发该事件 | { value, $event } |
| keyup | 在键盘输入按下弹起时触发该事件 | { value, $event } |
| click | 在点击输入框时触发该事件 | { value, $event } |
| focus | 在输入框聚焦时触发该事件 | { value, $event } |
| blur | 在输入框失焦时触发该事件 | { value, $event } |
| clear | 在点击右侧清除按钮时触发该事件 | { value, $event } |
| search-click | 只对 type=search 有效，在点击右侧搜索按钮时触发该事件 | { value, $event } |
| toggle-visible | 只对 type=password 有效，在点击右侧切换按钮时触发该事件 | { value, visible, $event } |
| prev-number | 只对 type=number 有效，在点击右侧向上按钮时触发该事件 | { value, $event } |
| next-number | 只对 type=number 有效，在点击右侧向下按钮时触发该事件 | { value, $event } |
| prefix-click | 在点击头部图标时触发该事件 | { value, $event } |
| suffix-click | 在点击尾部图标时触发该事件 | { value, $event } |
| date-prev | 只对 type=date,datetime,week,month,year 有效，在点击上一个视图按钮时触发该事件 | { viewType, viewDate, value, $event } |
| date-today | 只对 type=date,datetime,week,month,year 有效，在点击到今天视图按钮时触发该事件 | { value, $event } |
| date-next | 只对 type=date,datetime,week,month,year 有效，在点击下一个视图按钮时触发该事件 | { viewType, viewDate, value, $event } |

## Methods 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| focus() | 使输入框获取焦点 | — | Promise |
| blur() | 使输入框失去焦点 | — | Promise |
| select() | 使输入框选中内容 | — | Promise |

## 常见示例

### 基础输入框
```vue
<template>
  <div>
    <vxe-input v-model="value" placeholder="请输入内容"></vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: ''
    }
  }
}
</script>
```

### 可清除输入框
```vue
<template>
  <div>
    <vxe-input v-model="value" placeholder="请输入内容" clearable></vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: ''
    }
  }
}
</script>
```

### 搜索输入框
```vue
<template>
  <div>
    <vxe-input 
      v-model="searchValue" 
      type="search" 
      placeholder="请输入搜索内容"
      @search-click="handleSearch">
    </vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchValue: ''
    }
  },
  methods: {
    handleSearch({ value }) {
      console.log('搜索：', value)
    }
  }
}
</script>
```

### 带图标的输入框
```vue
<template>
  <div>
    <vxe-input 
      v-model="value" 
      placeholder="请输入用户名"
      prefix-icon="vxe-icon-user">
    </vxe-input>
    <vxe-input 
      v-model="password" 
      placeholder="请输入密码"
      suffix-icon="vxe-icon-lock">
    </vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: '',
      password: ''
    }
  }
}
</script>
```

### 字数统计
```vue
<template>
  <div>
    <vxe-input 
      v-model="content" 
      placeholder="请输入内容"
      :max-length="100"
      show-word-count>
    </vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      content: ''
    }
  }
}
</script>
```

### 不同尺寸
```vue
<template>
  <div>
    <vxe-input v-model="value1" size="mini" placeholder="迷你输入框"></vxe-input>
    <vxe-input v-model="value2" size="small" placeholder="小型输入框"></vxe-input>
    <vxe-input v-model="value3" size="medium" placeholder="中等输入框"></vxe-input>
    <vxe-input v-model="value4" placeholder="默认输入框"></vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: '',
      value3: '',
      value4: ''
    }
  }
}
</script>
```

### 禁用状态
```vue
<template>
  <div>
    <vxe-input v-model="value1" placeholder="正常输入框"></vxe-input>
    <vxe-input v-model="value2" placeholder="只读输入框" readonly></vxe-input>
    <vxe-input v-model="value3" placeholder="禁用输入框" disabled></vxe-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: '',
      value2: '只读内容',
      value3: '禁用内容'
    }
  }
}
</script>
```

## 版本兼容性

- 支持 Vue 3.2+
- 当前稳定版本：4.7.30
- 依赖：vxe-pc-ui

## 相关链接

- [官方文档](https://vxeui.com/)
- [GitHub](https://github.com/x-extends/vxe-pc-ui)
- [Gitee](https://gitee.com/x-extends/vxe-pc-ui)
