# VXE Table v4 API 文档

本目录包含了 VXE Table v4 版本的完整 API 文档，基于官方文档整理而成。

## 文档列表

### 核心组件

1. **[VxeTable.md](./VxeTable.md)** - 基础表格组件
   - 完整的 Props 配置项
   - 详细的 Events 事件列表
   - 全面的 Methods 方法说明
   - 丰富的使用示例

2. **[VxeColumn.md](./VxeColumn.md)** - 列组件
   - 列配置属性
   - 渲染器配置
   - 编辑器配置
   - 校验规则配置

3. **[VxeGrid.md](./VxeGrid.md)** - 配置式表格组件
   - Grid 特有配置项
   - 表单、工具栏、分页集成
   - 数据代理配置
   - 完整的功能示例

4. **[VxeToolbar.md](./VxeToolbar.md)** - 工具栏组件
   - 按钮配置
   - 工具配置
   - 导入导出功能
   - 个性化设置

## 版本信息

- **当前稳定版本**: 4.14.8
- **尝鲜版本**: 4.15.0-beta.12
- **Vue 版本要求**: Vue 3.2+
- **发布时间**: 2023-06-01 ~ 长期维护

## 快速开始

### 安装

```bash
npm install vxe-table
```

### 全局引入

```javascript
import { createApp } from 'vue'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

const app = createApp()
app.use(VxeTable)
```

### 局部引入

```javascript
import { VxeTable, VxeColumn, VxeGrid, VxeToolbar } from 'vxe-table'
```

## 主要特性

### VxeTable 基础表格
- 🎯 **基础功能**: 数据展示、排序、筛选、分页
- 🎨 **样式定制**: 斑马纹、边框、尺寸、主题
- 📝 **可编辑**: 单元格编辑、行编辑、批量编辑
- ✅ **数据校验**: 内置校验规则、自定义校验
- 🌳 **树形结构**: 树形数据展示、懒加载
- 📊 **虚拟滚动**: 支持大数据量渲染
- 🔧 **个性化**: 列拖拽、列宽调整、个性化设置

### VxeGrid 配置式表格
- ⚙️ **配置驱动**: 通过配置对象快速构建表格
- 🛠️ **工具栏集成**: 内置常用操作按钮
- 📋 **表单集成**: 查询表单与表格联动
- 📄 **分页集成**: 内置分页组件
- 🔄 **数据代理**: 自动处理增删改查操作
- 📤 **导入导出**: 支持 Excel、CSV 等格式

### VxeColumn 列组件
- 🎛️ **灵活配置**: 支持多种列类型
- 🎨 **渲染器**: 自定义单元格渲染
- ✏️ **编辑器**: 多种编辑器类型
- 🔍 **筛选器**: 自定义筛选组件
- 📏 **列宽控制**: 固定宽度、自适应宽度

### VxeToolbar 工具栏
- 🔘 **按钮组**: 左侧操作按钮
- 🛠️ **工具组**: 右侧工具按钮
- 📥 **导入功能**: 文件导入配置
- 📤 **导出功能**: 数据导出配置
- 🖨️ **打印功能**: 表格打印配置
- 🔄 **刷新功能**: 数据刷新配置
- ⚙️ **个性化**: 列显示隐藏设置

## 使用示例

### 基础表格

```vue
<template>
  <vxe-table :data="tableData">
    <vxe-column field="name" title="姓名"></vxe-column>
    <vxe-column field="age" title="年龄"></vxe-column>
    <vxe-column field="address" title="地址"></vxe-column>
  </vxe-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市' },
        { name: '李四', age: 30, address: '上海市' }
      ]
    }
  }
}
</script>
```

### 配置式表格

```vue
<template>
  <vxe-grid v-bind="gridOptions"></vxe-grid>
</template>

<script>
export default {
  data() {
    return {
      gridOptions: {
        border: true,
        resizable: true,
        height: 400,
        columns: [
          { field: 'name', title: '姓名' },
          { field: 'age', title: '年龄' },
          { field: 'address', title: '地址' }
        ],
        data: [
          { name: '张三', age: 25, address: '北京市' },
          { name: '李四', age: 30, address: '上海市' }
        ]
      }
    }
  }
}
</script>
```

## 相关链接

- [官方文档](https://vxetable.cn/v4/)
- [GitHub 仓库](https://github.com/x-extends/vxe-table)
- [Gitee 仓库](https://gitee.com/x-extends/vxe-table)
- [插件市场](https://vxetable.cn/plugins/)
- [企业版功能](https://vxetable.cn/enterprise/)

## 注意事项

1. **版本兼容性**: 确保使用 Vue 3.2+ 版本
2. **样式引入**: 记得引入 CSS 样式文件
3. **按需引入**: 可以按需引入组件以减小打包体积
4. **TypeScript**: 支持 TypeScript 类型定义
5. **浏览器兼容**: 支持现代浏览器，IE11+ 需要 polyfill

## 更新日志

- **v4.14.8**: 当前稳定版本，修复已知问题
- **v4.15.0-beta.12**: 尝鲜版本，包含新功能预览

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进文档。

## 许可证

本文档基于 MIT 许可证开源。
