# Via Grid 开发前置提示词

## 🎯 核心开发原则

在执行任何开发任务前，你必须严格遵循以下原则：

### 1. **ROADMAP.md 是唯一权威规范**
- 所有实现必须严格按照 ROADMAP.md 执行
- 任何疑问都要先查阅 ROADMAP.md 相关部分
- 不得擅自修改或偏离已约定的规范

### 2. **插槽命名规范（关键）**
- ✅ **必须使用连字符 `-`** 分隔：`table-name`、`search-status`、`form-tags`
- ✅ **支持 vxe-form 原生插槽**：`search-status-title`、`search-status-prefix`、`search-status-suffix`
- ✅ **扩展插槽格式**：`toolbar-buttons-after`、`table-action-after`
- ✅ **区域插槽格式**：`search-to-toolbar`、`toolbar-to-table`

### 3. **技术架构约束**
- Vue2 版本：基于 vxe-table v3.x，支持 Element UI、Ant Design Vue 1.x
- Vue3 版本：基于 vxe-table v4.x，支持 Element Plus、Ant Design Vue 4.x、Naive UI
- 构建工具：Vite + Rollup + UnoCSS
- 代码规范：antfu/eslint-config
- 弹窗组件：支持 modal 和 drawer 动态切换
- 表单组件：必须使用 vxe-form，不能使用 el-form

### 4. **设计原则约束**
- **约定优于配置**：提供合理默认值，最小配置即可使用，按需覆盖实现定制
- **API 优先**：先设计 API，再实现功能，确保接口的一致性和易用性
- **类型安全**：完整的 TypeScript 支持，提供智能提示和编译时检查
- **渐进增强**：从简单到复杂的使用方式，支持不同层次的定制需求

## 🔍 强制执行流程

### 开发前检查（必须执行）
1. **查阅 ROADMAP.md**：找到相关功能的具体要求和开发阶段
2. **确认插槽规范**：验证插槽命名使用 `-` 分隔符，支持 vxe-form 原生插槽
3. **检查技术约束**：确认使用正确的组件和版本，遵循设计原则
4. **理解完整需求**：确保没有遗漏关键细节，遵循约定优于配置原则

### 开发中验证（实时检查）
1. **每完成一个功能模块立即验证**
2. **对照 ROADMAP.md 检查实现正确性**
3. **确认插槽命名符合规范**
4. **测试关键功能点**

### 开发后确认（最终验证）
1. **完整功能测试**
2. **插槽系统测试**
3. **规范符合性验证**
4. **性能和兼容性检查**

## 📋 关键检查清单

### 插槽系统检查
- [ ] 所有插槽使用连字符 `-` 命名
- [ ] vxe-form 原生插槽 (title/prefix/suffix) 正确实现
- [ ] 扩展插槽 `-before`、`-after` 支持完整
- [ ] 区域插槽 (如 `search-to-toolbar`) 正确实现
- [ ] 作用域插槽参数传递完整

### 组件架构检查
- [ ] 基于正确的 vxe 版本实现 (Vue2: v3.x, Vue3: v4.x)
- [ ] 弹窗组件使用动态组件模式
- [ ] 表单组件使用 vxe-form
- [ ] 适配器系统正确集成
- [ ] UnoCSS 样式框架正确配置
- [ ] antfu/eslint-config 代码规范遵循

### 功能完整性检查
- [ ] 所有 ROADMAP.md 要求已实现
- [ ] 约定优于配置原则遵循
- [ ] 边界情况已处理
- [ ] 错误处理机制完善
- [ ] 类型定义完整
- [ ] 最小配置即可使用

## 🚨 常见错误预防

### 插槽命名错误
- ❌ 使用 `#table:name` 而不是 `#table-name`
- ❌ 使用冒号分隔符而不是连字符分隔符
- ❌ 扩展插槽命名错误 (应使用 `-after`、`-before`)
- ❌ vxe-form 原生插槽命名错误 (应使用 `-title`、`-prefix`、`-suffix`)

### 组件实现错误
- ❌ 使用 el-form 而不是 vxe-form
- ❌ 弹窗组件使用 v-if 而不是动态组件
- ❌ 版本依赖不匹配

### 功能遗漏错误
- ❌ vxe-form 原生插槽功能未实现
- ❌ 作用域插槽参数不完整
- ❌ 权限控制功能缺失
- ❌ 约定优于配置原则未遵循
- ❌ 默认值配置不合理

## 🔄 问题处理机制

### 发现偏离时
1. **立即停止当前实现**
2. **查阅 ROADMAP.md 相关部分**
3. **使用 ROLLBACK-PROMPT.md 中的修正指令**
4. **重新开始正确实现**

### 遇到疑问时
1. **优先查阅 ROADMAP.md**
2. **参考已有的正确实现**
3. **询问具体的技术细节**
4. **确认理解后再继续**

## 📝 开发任务模板

```markdown
## 当前开发任务：[任务名称]

### ROADMAP.md 要求确认
- [ ] 已查阅 ROADMAP.md 第 [行号] 部分
- [ ] 确认插槽命名规范：使用 `-` 分隔
- [ ] 确认技术架构要求：基于 vxe 生态
- [ ] 确认设计原则：约定优于配置
- [ ] 确认功能完整性要求

### 关键实现点
1. [实现点1]
2. [实现点2]
3. [实现点3]

### 风险预防
- 插槽命名：确保使用连字符分隔
- vxe-form 插槽：确保原生插槽功能实现
- 组件选择：确保使用正确的 vxe 组件
- 设计原则：确保遵循约定优于配置

### 验证计划
- [ ] 功能实现验证
- [ ] 插槽系统验证
- [ ] 规范符合性验证
```

## 🎯 执行指令

**在开始任何开发任务前，请回复确认：**

```
✅ 已查阅 ROADMAP.md 相关部分
✅ 已确认插槽命名规范（使用连字符分隔）
✅ 已确认技术架构要求（基于 vxe 生态）
✅ 已确认设计原则（约定优于配置）
✅ 已理解完整功能要求
✅ 准备开始正确实现

开始执行开发任务。
```

**如果发现任何偏离或疑问，立即使用：**

```
🚨 发现问题：[具体问题描述]
📋 ROADMAP 要求：[相关要求]
🔄 需要澄清：[具体疑问]

请提供指导或使用 ROLLBACK-PROMPT.md 修正。
```

## 📅 开发阶段指导

### 第一阶段：核心架构搭建
- **重点**：Monorepo 配置、包依赖关系、构建工具配置
- **关键检查**：pnpm workspace、Vite + UnoCSS、antfu/eslint-config
- **输出**：统一类型定义、Schema 转换逻辑、适配器基类

### 第二阶段：Vue3 版本实现
- **重点**：基于 vxe-table v4.x 的 Composition API 实现
- **关键检查**：ViaGrid 主组件、Composables 系统、适配器集成
- **输出**：完整的 Vue3 版本包

### 第三阶段：Vue2 版本实现
- **重点**：基于 vxe-table v3.x 的 Options API 实现
- **关键检查**：ViaGrid 主组件、Mixins 系统、适配器集成
- **输出**：完整的 Vue2 版本包

### 第四阶段：集成和完善
- **重点**：跨版本测试、文档完善、工具链优化
- **关键检查**：兼容性测试、API 一致性、性能优化
- **输出**：发布就绪的完整产品

## 🎨 UI 库支持矩阵

| UI 库 | Vue2 支持 | Vue3 支持 | 官方插件 | 开发优先级 |
|-------|----------|----------|----------|------------|
| Element Plus | ❌ | ✅ | @vxe-ui/plugin-render-element | 🔥 最高 |
| Element UI | ✅ | ❌ | @vxe-ui/plugin-render-element | 🔥 最高 |
| Ant Design Vue | ✅ | ✅ | @vxe-ui/plugin-render-antd | 🔶 中等 |
| Naive UI | ❌ | ✅ | @vxe-ui/plugin-render-naive | 🔶 中等 |

## 🔧 技术栈详细要求

### 构建和开发工具
- **包管理器**：pnpm >= 8.0.0
- **构建工具**：Vite + Rollup
- **样式框架**：UnoCSS (preset-wind, preset-icons, transformer-directives)
- **代码规范**：antfu/eslint-config
- **类型检查**：TypeScript 严格模式

### 版本兼容要求
- **Node.js**：>= 18.0.0
- **Vue 2**：>= 2.6.0 (配合 vxe-table v3.x)
- **Vue 3**：>= 3.2.0 (配合 vxe-table v4.x)

---

**重要提醒：这个前置提示词的目的是确保开发过程的准确性和高效性。严格遵循这些原则将避免重复修正，提升协作质量。**