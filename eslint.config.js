import antfu from '@antfu/eslint-config'

export default antfu(
  {
    vue: true,
    typescript: true,
    unocss: true,
    formatters: {
      css: true,
      html: true,
      markdown: 'prettier'
    }
  },
  {
    files: ['**/*.vue'],
    rules: {
      // Vue 特定规则
      'vue/component-name-in-template-casing': ['error', 'PascalCase'],
      'vue/component-definition-name-casing': ['error', 'PascalCase'],
      'vue/custom-event-name-casing': ['error', 'camelCase'],
      'vue/define-macros-order': ['error', {
        order: ['defineOptions', 'defineProps', 'defineEmits', 'defineSlots']
      }],
      'vue/no-empty-component-block': 'error'
    }
  },
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      // TypeScript 特定规则
      '@typescript-eslint/consistent-type-definitions': ['error', 'interface'],
      '@typescript-eslint/prefer-function-type': 'error'
    }
  },
  {
    files: ['packages/**/*'],
    rules: {
      // 包开发特定规则
      'no-console': 'warn',
      'no-debugger': 'error'
    }
  },
  {
    files: ['playground/**/*', 'docs/**/*'],
    rules: {
      // 开发环境允许 console
      'no-console': 'off'
    }
  }
)
