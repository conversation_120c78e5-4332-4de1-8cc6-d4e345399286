{"name": "via-grid", "version": "0.0.0", "description": "基于 vxe-table 的现代化表格组件库", "type": "module", "private": true, "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"setup": "pnpm install && pnpm build", "build": "pnpm -r --filter='./packages/*' run build", "dev": "pnpm -r --parallel run dev", "dev:docs": "pnpm -C docs run dev", "dev:vue2": "pnpm -C playground/vue2 run dev", "dev:vue3": "pnpm -C playground/vue3 run dev", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "typecheck": "vue-tsc --noEmit", "clean": "pnpm -r run clean && rimraf node_modules", "changeset": "changeset", "version": "changeset version", "release": "pnpm build && changeset publish"}, "devDependencies": {"@antfu/eslint-config": "^2.6.4", "@changesets/cli": "^2.27.1", "@types/node": "^20.11.5", "@unocss/eslint-plugin": "^0.58.3", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue2": "^2.3.1", "@vue/test-utils": "^2.4.4", "es-toolkit": "^1.28.1", "eslint": "^8.56.0", "jsdom": "^24.0.0", "prettier": "^3.2.4", "rimraf": "^5.0.5", "typescript": "^5.3.3", "unocss": "^0.58.3", "vite": "^5.0.12", "vitest": "^1.2.1", "vue": "^3.4.15", "vue-tsc": "^1.8.27"}, "peerDependencies": {"vue": "^2.6.0 || ^3.2.0"}, "keywords": ["vue", "vue2", "vue3", "table", "grid", "vxe-table", "element-plus", "element-ui", "ant-design-vue", "naive-ui", "typescript"], "author": "ViaGrid Team", "license": "MIT", "homepage": "https://github.com/viarotel/via-grid#readme", "repository": {"type": "git", "url": "git+https://github.com/viarotel/via-grid.git"}, "bugs": {"url": "https://github.com/viarotel/via-grid/issues"}}