import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts']
  },
  resolve: {
    alias: {
      '@via-grid/shared': './packages/shared/src',
      '@via-grid/core': './packages/core/src',
      '@via-grid/vue2': './packages/vue2/src',
      '@via-grid/vue3': './packages/vue3/src'
    }
  }
})
