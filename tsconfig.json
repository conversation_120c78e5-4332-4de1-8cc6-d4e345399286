{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    
    /* Vue */
    "allowJs": true,
    "checkJs": false,
    
    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@via-grid/shared": ["./packages/shared/src"],
      "@via-grid/core": ["./packages/core/src"],
      "@via-grid/vue2": ["./packages/vue2/src"],
      "@via-grid/vue3": ["./packages/vue3/src"]
    }
  },
  "include": [
    "packages/*/src/**/*",
    "playground/*/src/**/*",
    "docs/**/*",
    "*.config.*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.d.ts"
  ]
}
